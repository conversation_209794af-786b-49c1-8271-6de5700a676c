import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { voiceApi, audioApi } from '@/services/api';
import { VoiceStore, AudioRecording } from '@/types';
import toast from 'react-hot-toast';

interface VoiceState extends VoiceStore {
  // Additional state
  hasPermission: boolean;
  mediaRecorder?: MediaRecorder;
  audioStream?: MediaStream;
  audioContext?: AudioContext;
  analyser?: AnalyserNode;
  currentAudio?: HTMLAudioElement;
  recordingStartTime?: number;
  recordingDuration: number;
}

export const useVoiceStore = create<VoiceState>()(
  devtools(
    (set, get) => ({
      // Initial state
      isRecording: false,
      isProcessing: false,
      audioLevel: 0,
      transcript: '',
      confidence: 0,
      error: undefined,
      hasPermission: false,
      recordingDuration: 0,

      // Actions
      startRecording: async () => {
        const state = get();
        
        try {
          // Check if already recording
          if (state.isRecording) {
            console.warn('Already recording');
            return;
          }

          set({ error: undefined });

          // Request microphone permission
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true,
              sampleRate: 16000,
            },
          });

          // Set up audio context for level monitoring
          const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
          const analyser = audioContext.createAnalyser();
          const source = audioContext.createMediaStreamSource(stream);
          
          analyser.fftSize = 256;
          analyser.smoothingTimeConstant = 0.8;
          source.connect(analyser);

          // Set up media recorder
          const mediaRecorder = new MediaRecorder(stream, {
            mimeType: MediaRecorder.isTypeSupported('audio/webm') ? 'audio/webm' : 'audio/mp4',
          });

          const audioChunks: Blob[] = [];

          mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              audioChunks.push(event.data);
            }
          };

          mediaRecorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });
            const duration = state.recordingDuration;
            
            // Clean up
            stream.getTracks().forEach(track => track.stop());
            audioContext.close();

            // Process the recording
            await get().processAudio(audioBlob);
          };

          mediaRecorder.onerror = (event) => {
            console.error('MediaRecorder error:', event);
            set({ 
              error: 'Recording failed',
              isRecording: false,
              isProcessing: false,
            });
            toast.error('Recording failed');
          };

          // Start recording
          mediaRecorder.start();

          // Start audio level monitoring
          const monitorAudioLevel = () => {
            if (!get().isRecording) return;

            const dataArray = new Uint8Array(analyser.frequencyBinCount);
            analyser.getByteFrequencyData(dataArray);
            
            // Calculate average volume
            const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
            const normalizedLevel = Math.min(average / 128, 1); // Normalize to 0-1
            
            set({ audioLevel: normalizedLevel });
            requestAnimationFrame(monitorAudioLevel);
          };

          // Start duration tracking
          const startTime = Date.now();
          const updateDuration = () => {
            if (!get().isRecording) return;
            
            const duration = (Date.now() - startTime) / 1000;
            set({ recordingDuration: duration });
            
            // Auto-stop at max duration
            const maxDuration = parseInt(process.env.NEXT_PUBLIC_MAX_RECORDING_DURATION || '60');
            if (duration >= maxDuration) {
              get().stopRecording();
              toast.warning(`Recording stopped at ${maxDuration} second limit`);
              return;
            }
            
            setTimeout(updateDuration, 100);
          };

          set({
            isRecording: true,
            hasPermission: true,
            mediaRecorder,
            audioStream: stream,
            audioContext,
            analyser,
            recordingStartTime: startTime,
            recordingDuration: 0,
            transcript: '',
            confidence: 0,
          });

          // Start monitoring
          monitorAudioLevel();
          updateDuration();

          console.log('Recording started');

        } catch (error: any) {
          console.error('Failed to start recording:', error);
          
          let errorMessage = 'Failed to start recording';
          
          if (error.name === 'NotAllowedError') {
            errorMessage = 'Microphone permission denied. Please allow microphone access and try again.';
          } else if (error.name === 'NotFoundError') {
            errorMessage = 'No microphone found. Please connect a microphone and try again.';
          } else if (error.name === 'NotSupportedError') {
            errorMessage = 'Audio recording is not supported in this browser.';
          }

          set({ 
            error: errorMessage,
            isRecording: false,
            hasPermission: false,
          });
          
          toast.error(errorMessage);
        }
      },

      stopRecording: async () => {
        const state = get();
        
        if (!state.isRecording || !state.mediaRecorder) {
          console.warn('Not currently recording');
          return;
        }

        try {
          set({ isRecording: false, isProcessing: true });
          
          // Stop the media recorder (this will trigger the onstop event)
          state.mediaRecorder.stop();
          
          console.log('Recording stopped');

        } catch (error: any) {
          console.error('Failed to stop recording:', error);
          
          set({ 
            error: 'Failed to stop recording',
            isRecording: false,
            isProcessing: false,
          });
          
          toast.error('Failed to stop recording');
        }
      },

      processAudio: async (audioBlob: Blob) => {
        try {
          set({ isProcessing: true, error: undefined });

          console.log('Processing audio:', {
            size: audioBlob.size,
            type: audioBlob.type,
          });

          // Transcribe audio
          const transcriptionResult = await voiceApi.transcribe(audioBlob, {
            language: 'en',
          });

          set({
            transcript: transcriptionResult.transcript,
            confidence: transcriptionResult.confidence,
            isProcessing: false,
          });

          console.log('Transcription completed:', {
            transcript: transcriptionResult.transcript,
            confidence: transcriptionResult.confidence,
          });

          return transcriptionResult;

        } catch (error: any) {
          console.error('Failed to process audio:', error);
          
          set({ 
            error: error.error?.message || 'Failed to process audio',
            isProcessing: false,
          });
          
          toast.error('Failed to process audio');
          throw error;
        }
      },

      playAudio: async (audioUrl: string) => {
        try {
          // Stop any currently playing audio
          const state = get();
          if (state.currentAudio) {
            state.currentAudio.pause();
            state.currentAudio.currentTime = 0;
          }

          // Create new audio element
          const audio = new Audio(audioUrl);
          
          // Set up event listeners
          audio.onloadstart = () => {
            console.log('Audio loading started');
          };

          audio.oncanplay = () => {
            console.log('Audio can start playing');
          };

          audio.onplay = () => {
            console.log('Audio playback started');
          };

          audio.onended = () => {
            console.log('Audio playback ended');
            set({ currentAudio: undefined });
          };

          audio.onerror = (error) => {
            console.error('Audio playback error:', error);
            toast.error('Failed to play audio');
            set({ currentAudio: undefined });
          };

          // Start playback
          set({ currentAudio: audio });
          await audio.play();

        } catch (error: any) {
          console.error('Failed to play audio:', error);
          toast.error('Failed to play audio');
        }
      },

      setAudioLevel: (level: number) => {
        set({ audioLevel: Math.max(0, Math.min(1, level)) });
      },

      setTranscript: (transcript: string, confidence?: number) => {
        set({ 
          transcript,
          confidence: confidence !== undefined ? confidence : get().confidence,
        });
      },

      setError: (error: string | null) => {
        set({ error: error || undefined });
      },

      reset: () => {
        const state = get();
        
        // Clean up any ongoing recording
        if (state.isRecording && state.mediaRecorder) {
          state.mediaRecorder.stop();
        }

        // Clean up audio stream
        if (state.audioStream) {
          state.audioStream.getTracks().forEach(track => track.stop());
        }

        // Clean up audio context
        if (state.audioContext) {
          state.audioContext.close();
        }

        // Stop any playing audio
        if (state.currentAudio) {
          state.currentAudio.pause();
          state.currentAudio.currentTime = 0;
        }

        set({
          isRecording: false,
          isProcessing: false,
          audioLevel: 0,
          transcript: '',
          confidence: 0,
          error: undefined,
          recordingDuration: 0,
          mediaRecorder: undefined,
          audioStream: undefined,
          audioContext: undefined,
          analyser: undefined,
          currentAudio: undefined,
          recordingStartTime: undefined,
        });
      },

      // Additional helper methods
      requestPermission: async (): Promise<boolean> => {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          stream.getTracks().forEach(track => track.stop()); // Clean up test stream
          
          set({ hasPermission: true });
          return true;
        } catch (error) {
          set({ hasPermission: false });
          return false;
        }
      },

      checkPermission: async (): Promise<boolean> => {
        try {
          const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
          const hasPermission = permission.state === 'granted';
          set({ hasPermission });
          return hasPermission;
        } catch (error) {
          // Fallback: try to access microphone
          return await get().requestPermission();
        }
      },

      cancelRecording: () => {
        const state = get();
        
        if (state.isRecording && state.mediaRecorder) {
          // Stop recording without processing
          state.mediaRecorder.stop();
          
          // Clean up
          if (state.audioStream) {
            state.audioStream.getTracks().forEach(track => track.stop());
          }
          
          if (state.audioContext) {
            state.audioContext.close();
          }
        }

        set({
          isRecording: false,
          isProcessing: false,
          audioLevel: 0,
          recordingDuration: 0,
          transcript: '',
          confidence: 0,
          error: undefined,
        });

        console.log('Recording cancelled');
      },
    }),
    {
      name: 'voice-store',
    }
  )
);
