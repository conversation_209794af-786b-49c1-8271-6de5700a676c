import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';
import { createError } from '@/middleware/errorHandler';
import { PaginatedResponse, ConversationFilter, SortParams } from '@/types';

export class ConversationService {
  async getConversationWithMessages(
    conversationId: string,
    options: {
      limit?: number;
      offset?: number;
      before?: Date;
      after?: Date;
    } = {}
  ) {
    try {
      const { limit = 50, offset = 0, before, after } = options;

      const conversation = await prisma.conversation.findUnique({
        where: { id: conversationId },
        include: {
          messages: {
            where: {
              ...(before && { createdAt: { lt: before } }),
              ...(after && { createdAt: { gt: after } }),
            },
            orderBy: { createdAt: 'asc' },
            skip: offset,
            take: limit,
          },
        },
      });

      if (!conversation) {
        return null;
      }

      return {
        ...conversation,
        metadata: this.parseMetadata(conversation.metadata),
        messages: conversation.messages.map(msg => ({
          ...msg,
          metadata: this.parseMetadata(msg.metadata),
        })),
      };

    } catch (error: any) {
      logger.error('Failed to get conversation with messages', {
        conversationId,
        error: error.message,
      });
      throw createError.database('Failed to retrieve conversation');
    }
  }

  async getUserConversations(
    userId?: string,
    options: {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<PaginatedResponse<any>> {
    try {
      const { page = 1, limit = 20, sortBy = 'updatedAt', sortOrder = 'desc' } = options;
      const skip = (page - 1) * limit;

      const where = userId ? { userId } : {};

      const [conversations, total] = await Promise.all([
        prisma.conversation.findMany({
          where,
          orderBy: { [sortBy]: sortOrder },
          skip,
          take: limit,
          include: {
            _count: {
              select: { messages: true },
            },
          },
        }),
        prisma.conversation.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        data: conversations.map(conv => ({
          ...conv,
          metadata: this.parseMetadata(conv.metadata),
          messageCount: conv._count.messages,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };

    } catch (error: any) {
      logger.error('Failed to get user conversations', {
        userId,
        error: error.message,
      });
      throw createError.database('Failed to retrieve conversations');
    }
  }

  async updateConversationTitle(
    conversationId: string,
    title: string,
    userId?: string
  ) {
    try {
      const where: any = { id: conversationId };
      if (userId) {
        where.userId = userId;
      }

      const conversation = await prisma.conversation.findUnique({ where });
      
      if (!conversation) {
        throw createError.notFound('Conversation');
      }

      const updatedConversation = await prisma.conversation.update({
        where: { id: conversationId },
        data: {
          title,
          updatedAt: new Date(),
        },
      });

      logger.info('Conversation title updated', {
        conversationId,
        newTitle: title,
        userId,
      });

      return {
        ...updatedConversation,
        metadata: this.parseMetadata(updatedConversation.metadata),
      };

    } catch (error: any) {
      if (error.isOperational) {
        throw error;
      }

      logger.error('Failed to update conversation title', {
        conversationId,
        title,
        error: error.message,
      });
      throw createError.database('Failed to update conversation title');
    }
  }

  async deleteConversation(conversationId: string, userId?: string) {
    try {
      const where: any = { id: conversationId };
      if (userId) {
        where.userId = userId;
      }

      const conversation = await prisma.conversation.findUnique({ where });
      
      if (!conversation) {
        throw createError.notFound('Conversation');
      }

      // Delete all messages first (cascade should handle this, but being explicit)
      await prisma.message.deleteMany({
        where: { conversationId },
      });

      // Delete the conversation
      await prisma.conversation.delete({
        where: { id: conversationId },
      });

      logger.info('Conversation deleted', {
        conversationId,
        userId,
      });

    } catch (error: any) {
      if (error.isOperational) {
        throw error;
      }

      logger.error('Failed to delete conversation', {
        conversationId,
        error: error.message,
      });
      throw createError.database('Failed to delete conversation');
    }
  }

  async clearUserConversations(userId?: string): Promise<number> {
    try {
      const where = userId ? { userId } : {};

      // Get conversation IDs to delete
      const conversations = await prisma.conversation.findMany({
        where,
        select: { id: true },
      });

      const conversationIds = conversations.map(c => c.id);

      if (conversationIds.length === 0) {
        return 0;
      }

      // Delete all messages for these conversations
      await prisma.message.deleteMany({
        where: {
          conversationId: { in: conversationIds },
        },
      });

      // Delete all conversations
      const result = await prisma.conversation.deleteMany({ where });

      logger.info('User conversations cleared', {
        userId,
        deletedCount: result.count,
      });

      return result.count;

    } catch (error: any) {
      logger.error('Failed to clear user conversations', {
        userId,
        error: error.message,
      });
      throw createError.database('Failed to clear conversations');
    }
  }

  async getConversationStats(userId?: string) {
    try {
      const where = userId ? { userId } : {};

      const [
        totalConversations,
        totalMessages,
        recentConversations,
        avgMessagesPerConversation,
      ] = await Promise.all([
        prisma.conversation.count({ where }),
        prisma.message.count({
          where: {
            conversation: where,
          },
        }),
        prisma.conversation.count({
          where: {
            ...where,
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
            },
          },
        }),
        prisma.conversation.aggregate({
          where,
          _avg: {
            id: true, // This is a placeholder - we'll calculate properly below
          },
        }),
      ]);

      // Calculate average messages per conversation
      const avgMessages = totalConversations > 0 
        ? Math.round(totalMessages / totalConversations * 100) / 100 
        : 0;

      return {
        totalConversations,
        totalMessages,
        recentConversations,
        avgMessagesPerConversation: avgMessages,
        stats: {
          conversationsThisWeek: recentConversations,
          messagesThisWeek: await this.getRecentMessageCount(userId, 7),
          longestConversation: await this.getLongestConversation(userId),
        },
      };

    } catch (error: any) {
      logger.error('Failed to get conversation stats', {
        userId,
        error: error.message,
      });
      throw createError.database('Failed to retrieve conversation statistics');
    }
  }

  private async getRecentMessageCount(userId?: string, days: number = 7): Promise<number> {
    const where = userId ? { conversation: { userId } } : {};
    
    return prisma.message.count({
      where: {
        ...where,
        createdAt: {
          gte: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
        },
      },
    });
  }

  private async getLongestConversation(userId?: string) {
    const where = userId ? { userId } : {};
    
    const conversations = await prisma.conversation.findMany({
      where,
      include: {
        _count: {
          select: { messages: true },
        },
      },
      orderBy: {
        messages: {
          _count: 'desc',
        },
      },
      take: 1,
    });

    return conversations[0] ? {
      id: conversations[0].id,
      title: conversations[0].title,
      messageCount: conversations[0]._count.messages,
    } : null;
  }

  private parseMetadata(metadata: string): Record<string, any> {
    try {
      return JSON.parse(metadata);
    } catch {
      return {};
    }
  }
}

export default ConversationService;
