"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const errorHandler_1 = require("@/middleware/errorHandler");
const database_1 = require("@/config/database");
const gemini_1 = require("@/config/gemini");
const logger_1 = require("@/utils/logger");
const router = (0, express_1.Router)();
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const startTime = Date.now();
    const databaseStatus = await (0, database_1.checkDatabaseConnection)();
    const geminiStatus = await (0, gemini_1.testGeminiConnection)();
    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime();
    let overallStatus = 'healthy';
    if (!databaseStatus || !geminiStatus) {
        overallStatus = 'unhealthy';
    }
    else if (!databaseStatus || !geminiStatus) {
        overallStatus = 'degraded';
    }
    const healthStatus = {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0',
        services: {
            database: databaseStatus ? 'connected' : 'error',
            gemini: geminiStatus ? 'available' : 'error',
            speechService: 'available',
        },
        uptime: Math.floor(uptime),
        memory: {
            used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
            total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
            percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100),
        },
    };
    (0, logger_1.logHealth)('api-server', overallStatus, {
        responseTime: Date.now() - startTime,
        services: healthStatus.services,
    });
    const statusCode = overallStatus === 'healthy' ? 200 :
        overallStatus === 'degraded' ? 200 : 503;
    res.status(statusCode).json({
        success: overallStatus !== 'unhealthy',
        data: healthStatus,
        message: `Service is ${overallStatus}`,
        timestamp: new Date().toISOString(),
    });
}));
router.get('/detailed', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const startTime = Date.now();
    const checks = await Promise.allSettled([
        (0, database_1.checkDatabaseConnection)(),
        testOpenAIConnection(),
        checkDiskSpace(),
        checkMemoryUsage(),
        checkEnvironmentVariables(),
    ]);
    const [databaseResult, openaiResult, diskResult, memoryResult, envResult,] = checks;
    const healthDetails = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV,
        uptime: Math.floor(process.uptime()),
        responseTime: Date.now() - startTime,
        checks: {
            database: {
                status: databaseResult.status === 'fulfilled' && databaseResult.value ? 'pass' : 'fail',
                message: databaseResult.status === 'fulfilled' ? 'Database connection successful' : 'Database connection failed',
                error: databaseResult.status === 'rejected' ? databaseResult.reason?.message : undefined,
            },
            openai: {
                status: openaiResult.status === 'fulfilled' && openaiResult.value ? 'pass' : 'fail',
                message: openaiResult.status === 'fulfilled' ? 'OpenAI API connection successful' : 'OpenAI API connection failed',
                error: openaiResult.status === 'rejected' ? openaiResult.reason?.message : undefined,
            },
            disk: {
                status: diskResult.status === 'fulfilled' && diskResult.value ? 'pass' : 'warn',
                message: diskResult.status === 'fulfilled' ? 'Disk space sufficient' : 'Disk space check failed',
                details: diskResult.status === 'fulfilled' ? diskResult.value : undefined,
            },
            memory: {
                status: memoryResult.status === 'fulfilled' && memoryResult.value ? 'pass' : 'warn',
                message: memoryResult.status === 'fulfilled' ? 'Memory usage normal' : 'Memory usage high',
                details: memoryResult.status === 'fulfilled' ? memoryResult.value : undefined,
            },
            environment: {
                status: envResult.status === 'fulfilled' && envResult.value ? 'pass' : 'fail',
                message: envResult.status === 'fulfilled' ? 'Environment variables configured' : 'Environment variables missing',
                details: envResult.status === 'fulfilled' ? envResult.value : undefined,
            },
        },
    };
    const failedChecks = Object.values(healthDetails.checks).filter(check => check.status === 'fail');
    const warnChecks = Object.values(healthDetails.checks).filter(check => check.status === 'warn');
    if (failedChecks.length > 0) {
        healthDetails.status = 'unhealthy';
    }
    else if (warnChecks.length > 0) {
        healthDetails.status = 'degraded';
    }
    const statusCode = healthDetails.status === 'healthy' ? 200 :
        healthDetails.status === 'degraded' ? 200 : 503;
    res.status(statusCode).json({
        success: healthDetails.status !== 'unhealthy',
        data: healthDetails,
        message: `Detailed health check completed - ${healthDetails.status}`,
        timestamp: new Date().toISOString(),
    });
}));
router.get('/ready', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const databaseReady = await (0, database_1.checkDatabaseConnection)();
    const openaiReady = await testOpenAIConnection();
    const isReady = databaseReady && openaiReady;
    res.status(isReady ? 200 : 503).json({
        success: isReady,
        data: {
            ready: isReady,
            services: {
                database: databaseReady,
                openai: openaiReady,
            },
        },
        message: isReady ? 'Service is ready' : 'Service is not ready',
        timestamp: new Date().toISOString(),
    });
}));
router.get('/live', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.status(200).json({
        success: true,
        data: {
            alive: true,
            uptime: Math.floor(process.uptime()),
            timestamp: new Date().toISOString(),
        },
        message: 'Service is alive',
        timestamp: new Date().toISOString(),
    });
}));
async function checkDiskSpace() {
    try {
        const fs = require('fs');
        const stats = fs.statSync('.');
        return {
            available: true,
            message: 'Disk space check not implemented',
        };
    }
    catch (error) {
        return {
            available: false,
            error: error.message,
        };
    }
}
async function checkMemoryUsage() {
    const memoryUsage = process.memoryUsage();
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    return {
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        percentage: Math.round(memoryUsagePercent),
        status: memoryUsagePercent < 90 ? 'normal' : 'high',
    };
}
async function checkEnvironmentVariables() {
    const requiredVars = [
        'DATABASE_URL',
        'OPENAI_API_KEY',
        'NODE_ENV',
        'PORT',
    ];
    const missing = requiredVars.filter(varName => !process.env[varName]);
    return {
        allPresent: missing.length === 0,
        missing,
        total: requiredVars.length,
        present: requiredVars.length - missing.length,
    };
}
exports.default = router;
//# sourceMappingURL=health.js.map