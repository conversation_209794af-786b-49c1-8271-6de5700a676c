import React, { useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { clsx } from 'clsx';
import { VoiceVisualizerProps } from '@/types';

export function VoiceVisualizer({
  audioLevel,
  isActive,
  barCount = 5,
  className,
}: VoiceVisualizerProps) {
  // Generate bar heights based on audio level
  const barHeights = useMemo(() => {
    const heights: number[] = [];
    
    for (let i = 0; i < barCount; i++) {
      if (!isActive) {
        heights.push(0.1); // Minimum height when inactive
      } else {
        // Create a wave pattern with the center bars being tallest
        const centerIndex = Math.floor(barCount / 2);
        const distanceFromCenter = Math.abs(i - centerIndex);
        const maxDistance = Math.floor(barCount / 2);
        
        // Base height decreases with distance from center
        const baseHeight = 1 - (distanceFromCenter / maxDistance) * 0.5;
        
        // Apply audio level with some randomness for natural look
        const randomFactor = 0.8 + Math.random() * 0.4; // 0.8 to 1.2
        const height = Math.max(0.1, Math.min(1, baseHeight * audioLevel * randomFactor));
        
        heights.push(height);
      }
    }
    
    return heights;
  }, [audioLevel, isActive, barCount]);

  // Animation variants for bars
  const barVariants = {
    inactive: {
      scaleY: 0.1,
      opacity: 0.3,
      transition: { duration: 0.3, ease: 'easeOut' },
    },
    active: (height: number) => ({
      scaleY: height,
      opacity: 1,
      transition: {
        duration: 0.1,
        ease: 'easeOut',
      },
    }),
  };

  // Container animation
  const containerVariants = {
    inactive: {
      scale: 0.9,
      opacity: 0.5,
    },
    active: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.2,
        ease: 'easeOut',
      },
    },
  };

  return (
    <motion.div
      className={clsx(
        'flex items-end justify-center space-x-1 h-16',
        className
      )}
      variants={containerVariants}
      animate={isActive ? 'active' : 'inactive'}
    >
      {barHeights.map((height, index) => (
        <motion.div
          key={index}
          className={clsx(
            'w-2 rounded-full origin-bottom',
            isActive
              ? 'bg-gradient-to-t from-primary-600 to-primary-400'
              : 'bg-gray-300 dark:bg-gray-600'
          )}
          style={{ height: '100%' }}
          variants={barVariants}
          animate={isActive ? 'active' : 'inactive'}
          custom={height}
        />
      ))}
    </motion.div>
  );
}

// Alternative circular visualizer
export function CircularVoiceVisualizer({
  audioLevel,
  isActive,
  className,
}: Omit<VoiceVisualizerProps, 'barCount'>) {
  const radius = 40;
  const strokeWidth = 4;
  const normalizedRadius = radius - strokeWidth * 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  
  // Calculate stroke dash offset based on audio level
  const strokeDashoffset = circumference - (audioLevel * circumference);

  return (
    <div className={clsx('relative', className)}>
      <svg
        height={radius * 2}
        width={radius * 2}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          stroke="currentColor"
          fill="transparent"
          strokeWidth={strokeWidth}
          r={normalizedRadius}
          cx={radius}
          cy={radius}
          className="text-gray-300 dark:text-gray-600"
        />
        
        {/* Audio level circle */}
        <AnimatePresence>
          {isActive && (
            <motion.circle
              stroke="currentColor"
              fill="transparent"
              strokeWidth={strokeWidth}
              strokeDasharray={`${circumference} ${circumference}`}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap="round"
              r={normalizedRadius}
              cx={radius}
              cy={radius}
              className="text-primary-500"
              initial={{ strokeDashoffset: circumference }}
              animate={{ strokeDashoffset }}
              transition={{ duration: 0.1, ease: 'easeOut' }}
            />
          )}
        </AnimatePresence>
      </svg>
      
      {/* Center indicator */}
      <motion.div
        className="absolute inset-0 flex items-center justify-center"
        animate={{
          scale: isActive ? 1 + audioLevel * 0.3 : 1,
        }}
        transition={{ duration: 0.1 }}
      >
        <div
          className={clsx(
            'w-4 h-4 rounded-full',
            isActive
              ? 'bg-primary-500'
              : 'bg-gray-400 dark:bg-gray-600'
          )}
        />
      </motion.div>
    </div>
  );
}

// Waveform visualizer
export function WaveformVisualizer({
  audioLevel,
  isActive,
  barCount = 20,
  className,
}: VoiceVisualizerProps) {
  // Generate waveform data
  const waveformData = useMemo(() => {
    const data: number[] = [];
    
    for (let i = 0; i < barCount; i++) {
      if (!isActive) {
        data.push(0.1);
      } else {
        // Create a more natural waveform pattern
        const frequency = 0.3; // Adjust for wave frequency
        const phase = (i / barCount) * Math.PI * 2 * frequency;
        const baseWave = Math.sin(phase) * 0.5 + 0.5; // 0 to 1
        
        // Add some randomness
        const randomFactor = 0.7 + Math.random() * 0.6; // 0.7 to 1.3
        
        // Apply audio level
        const height = Math.max(0.1, Math.min(1, baseWave * audioLevel * randomFactor));
        data.push(height);
      }
    }
    
    return data;
  }, [audioLevel, isActive, barCount]);

  return (
    <div className={clsx('flex items-center justify-center space-x-0.5 h-12', className)}>
      {waveformData.map((height, index) => (
        <motion.div
          key={index}
          className={clsx(
            'w-1 rounded-full',
            isActive
              ? 'bg-primary-500'
              : 'bg-gray-300 dark:bg-gray-600'
          )}
          animate={{
            height: `${height * 100}%`,
          }}
          transition={{
            duration: 0.1,
            ease: 'easeOut',
          }}
        />
      ))}
    </div>
  );
}

// Pulse visualizer
export function PulseVisualizer({
  audioLevel,
  isActive,
  className,
}: Omit<VoiceVisualizerProps, 'barCount'>) {
  return (
    <div className={clsx('relative flex items-center justify-center', className)}>
      {/* Outer pulse rings */}
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className="absolute rounded-full border-2 border-primary-500"
          animate={
            isActive
              ? {
                  scale: [1, 1.5 + audioLevel, 1],
                  opacity: [0.8, 0.2, 0],
                }
              : {
                  scale: 1,
                  opacity: 0,
                }
          }
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: index * 0.3,
            ease: 'easeOut',
          }}
          style={{
            width: 60,
            height: 60,
          }}
        />
      ))}
      
      {/* Center circle */}
      <motion.div
        className={clsx(
          'w-8 h-8 rounded-full',
          isActive
            ? 'bg-primary-500'
            : 'bg-gray-400 dark:bg-gray-600'
        )}
        animate={{
          scale: isActive ? 1 + audioLevel * 0.5 : 1,
        }}
        transition={{ duration: 0.1 }}
      />
    </div>
  );
}

export default VoiceVisualizer;
