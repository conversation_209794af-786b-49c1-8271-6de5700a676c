"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const errorHandler_1 = require("@/middleware/errorHandler");
const validation_1 = require("@/middleware/validation");
const rateLimiter_1 = require("@/middleware/rateLimiter");
const chatService_1 = require("@/services/chatService");
const conversationService_1 = require("@/services/conversationService");
const logger_1 = require("@/utils/logger");
const router = (0, express_1.Router)();
const chatService = new chatService_1.ChatService();
const conversationService = new conversationService_1.ConversationService();
router.use(rateLimiter_1.chatRateLimiter);
router.post('/message', (0, validation_1.validate)(validation_1.chatSchemas.sendMessage), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { message, conversationId, context } = req.body;
    const startTime = Date.now();
    logger_1.logger.info('Processing chat message', {
        requestId: req.requestId,
        messageLength: message.length,
        conversationId,
        hasContext: !!context,
    });
    try {
        const result = await chatService.processMessage({
            message,
            conversationId,
            context,
            userId: req.user?.id,
        });
        const responseTime = Date.now() - startTime;
        logger_1.logger.info('Chat message processed successfully', {
            requestId: req.requestId,
            conversationId: result.conversationId,
            responseTime,
            tokensUsed: result.metadata.tokensUsed,
        });
        res.json({
            success: true,
            data: result,
            message: 'Message processed successfully',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to process chat message', {
            requestId: req.requestId,
            error: error.message,
            messageLength: message.length,
        });
        throw error;
    }
}));
router.get('/history/:conversationId', (0, validation_1.validate)(validation_1.chatSchemas.getHistory), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { conversationId } = req.params;
    const { limit, offset, before, after } = req.query;
    logger_1.logger.debug('Retrieving conversation history', {
        requestId: req.requestId,
        conversationId,
        limit,
        offset,
    });
    try {
        const conversation = await conversationService.getConversationWithMessages(conversationId, {
            limit: Number(limit),
            offset: Number(offset),
            before: before ? new Date(before) : undefined,
            after: after ? new Date(after) : undefined,
        });
        if (!conversation) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'CONVERSATION_NOT_FOUND',
                    message: 'Conversation not found',
                },
                timestamp: new Date().toISOString(),
            });
        }
        if (req.user && conversation.userId && conversation.userId !== req.user.id) {
            return res.status(403).json({
                success: false,
                error: {
                    code: 'FORBIDDEN',
                    message: 'Access denied to this conversation',
                },
                timestamp: new Date().toISOString(),
            });
        }
        const response = {
            conversationId: conversation.id,
            title: conversation.title,
            messages: conversation.messages || [],
            totalMessages: conversation.messages?.length || 0,
            hasMore: (conversation.messages?.length || 0) >= Number(limit),
            metadata: conversation.metadata,
            createdAt: conversation.createdAt,
            updatedAt: conversation.updatedAt,
        };
        logger_1.logger.debug('Conversation history retrieved', {
            requestId: req.requestId,
            conversationId,
            messageCount: response.messages.length,
        });
        res.json({
            success: true,
            data: response,
            message: 'Conversation history retrieved successfully',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to retrieve conversation history', {
            requestId: req.requestId,
            conversationId,
            error: error.message,
        });
        throw error;
    }
}));
router.get('/conversations', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { page = 1, limit = 20, sortBy = 'updatedAt', sortOrder = 'desc' } = req.query;
    const userId = req.user?.id;
    logger_1.logger.debug('Retrieving user conversations', {
        requestId: req.requestId,
        userId,
        page,
        limit,
    });
    try {
        const conversations = await conversationService.getUserConversations(userId, {
            page: Number(page),
            limit: Number(limit),
            sortBy: sortBy,
            sortOrder: sortOrder,
        });
        res.json({
            success: true,
            data: conversations,
            message: 'Conversations retrieved successfully',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to retrieve conversations', {
            requestId: req.requestId,
            userId,
            error: error.message,
        });
        throw error;
    }
}));
router.put('/conversations/:conversationId/title', (0, validation_1.validate)({
    params: validation_1.chatSchemas.deleteConversation.params,
    body: {
        title: require('joi').string().min(1).max(100).required(),
    },
}), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { conversationId } = req.params;
    const { title } = req.body;
    const userId = req.user?.id;
    logger_1.logger.debug('Updating conversation title', {
        requestId: req.requestId,
        conversationId,
        newTitle: title,
    });
    try {
        const updatedConversation = await conversationService.updateConversationTitle(conversationId, title, userId);
        res.json({
            success: true,
            data: {
                conversationId: updatedConversation.id,
                title: updatedConversation.title,
                updatedAt: updatedConversation.updatedAt,
            },
            message: 'Conversation title updated successfully',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to update conversation title', {
            requestId: req.requestId,
            conversationId,
            error: error.message,
        });
        throw error;
    }
}));
router.delete('/history/:conversationId', (0, validation_1.validate)(validation_1.chatSchemas.deleteConversation), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { conversationId } = req.params;
    const userId = req.user?.id;
    logger_1.logger.info('Deleting conversation', {
        requestId: req.requestId,
        conversationId,
        userId,
    });
    try {
        await conversationService.deleteConversation(conversationId, userId);
        logger_1.logger.info('Conversation deleted successfully', {
            requestId: req.requestId,
            conversationId,
        });
        res.json({
            success: true,
            message: 'Conversation deleted successfully',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to delete conversation', {
            requestId: req.requestId,
            conversationId,
            error: error.message,
        });
        throw error;
    }
}));
router.delete('/conversations', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user?.id;
    logger_1.logger.info('Clearing all conversations', {
        requestId: req.requestId,
        userId,
    });
    try {
        const deletedCount = await conversationService.clearUserConversations(userId);
        logger_1.logger.info('All conversations cleared', {
            requestId: req.requestId,
            userId,
            deletedCount,
        });
        res.json({
            success: true,
            data: {
                deletedCount,
            },
            message: `${deletedCount} conversations deleted successfully`,
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to clear conversations', {
            requestId: req.requestId,
            userId,
            error: error.message,
        });
        throw error;
    }
}));
router.get('/stats', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user?.id;
    logger_1.logger.debug('Retrieving conversation statistics', {
        requestId: req.requestId,
        userId,
    });
    try {
        const stats = await conversationService.getConversationStats(userId);
        res.json({
            success: true,
            data: stats,
            message: 'Conversation statistics retrieved successfully',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to retrieve conversation statistics', {
            requestId: req.requestId,
            userId,
            error: error.message,
        });
        throw error;
    }
}));
exports.default = router;
//# sourceMappingURL=chat.js.map