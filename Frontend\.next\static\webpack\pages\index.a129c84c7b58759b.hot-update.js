"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Mic_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Mic,Settings,Square!=!lucide-react */ \"__barrel_optimize__?names=MessageSquare,Mic,Settings,Square!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction HomePage() {\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            role: \"assistant\",\n            content: \"Hello! I'm your AI assistant. How can I help you today?\"\n        }\n    ]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 語音錄音功能\n    const handleVoiceRecording = async ()=>{\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n            alert(\"您的瀏覽器不支援語音功能。\");\n            return;\n        }\n        if (isRecording) {\n            // 如果正在錄音，這裡應該停止錄音\n            setIsRecording(false);\n            return;\n        }\n        try {\n            setIsRecording(true);\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            const mediaRecorder = new MediaRecorder(stream);\n            const chunks = [];\n            mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0) {\n                    chunks.push(event.data);\n                }\n            };\n            mediaRecorder.onstop = async ()=>{\n                const audioBlob = new Blob(chunks, {\n                    type: \"audio/wav\"\n                });\n                // 嘗試發送到後端進行語音轉文字\n                try {\n                    const formData = new FormData();\n                    formData.append(\"audio\", audioBlob, \"recording.wav\");\n                    const response = await fetch(\"http://localhost:8000/api/voice/transcribe\", {\n                        method: \"POST\",\n                        body: formData\n                    });\n                    if (response.ok) {\n                        const data = await response.json();\n                        const transcript = data.data.transcript || \"語音轉錄完成\";\n                        setMessage(transcript);\n                    } else {\n                        setMessage(\"語音已錄製完成 (後端轉錄失敗，請檢查後端服務)\");\n                    }\n                } catch (error) {\n                    console.error(\"語音處理錯誤:\", error);\n                    setMessage(\"語音已錄製完成 (模擬轉錄結果 - 請手動輸入您的問題)\");\n                }\n                // 停止所有音軌\n                stream.getTracks().forEach((track)=>track.stop());\n                setIsRecording(false);\n            };\n            // 開始錄音\n            mediaRecorder.start();\n        } catch (error) {\n            console.error(\"麥克風錯誤:\", error);\n            alert(\"無法啟動麥克風。請確保您已授權麥克風權限。\");\n            setIsRecording(false);\n        }\n    };\n    const sendMessage = async ()=>{\n        if (!message.trim()) return;\n        const userMessage = {\n            role: \"user\",\n            content: message\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setMessage(\"\");\n        setLoading(true);\n        try {\n            const res = await fetch(\"http://localhost:8000/api/chat/message\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message\n                })\n            });\n            if (res.ok) {\n                const data = await res.json();\n                const aiMessage = {\n                    role: \"assistant\",\n                    content: data.data.response\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiMessage\n                    ]);\n            } else {\n                const errorMessage = {\n                    role: \"assistant\",\n                    content: \"Sorry, I encountered an error. Please try again.\"\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }\n        } catch (error) {\n            const errorMessage = {\n                role: \"assistant\",\n                content: \"Cannot connect to the server. Please check your connection.\"\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Voice AI Chat\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"AI-powered voice chat application\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white border-b border-gray-200 px-4 py-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__.MessageSquare, {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Voice AI Chat\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/simple\",\n                                            className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200\",\n                                            children: \"Simple Test\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Settings, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto space-y-4\",\n                                    children: [\n                                        messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex \".concat(msg.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg \".concat(msg.role === \"user\" ? \"bg-blue-600 text-white\" : \"bg-white text-gray-900 border border-gray-200\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: msg.content\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)),\n                                        loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-start\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white border border-gray-200 rounded-lg px-4 py-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"AI is thinking...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border-t border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: [\n                                        isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center space-x-2 px-4 py-2 bg-red-100 text-red-700 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-red-500 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"正在錄音... (5秒後自動停止)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-1 h-16\",\n                                                children: [\n                                                    ...Array(7)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 rounded-full transition-all duration-300 \".concat(isRecording ? \"bg-red-500 animate-pulse\" : \"bg-gray-300\"),\n                                                        style: {\n                                                            height: isRecording ? \"\".concat(30 + Math.random() * 30, \"px\") : \"\".concat(20 + Math.random() * 20, \"px\")\n                                                        }\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleVoiceRecording,\n                                                disabled: loading,\n                                                className: \"w-16 h-16 rounded-full flex items-center justify-center text-white transition-all duration-300 \".concat(isRecording ? \"bg-red-600 hover:bg-red-700 animate-pulse\" : \"bg-blue-600 hover:bg-blue-700\", \" disabled:bg-gray-400 disabled:cursor-not-allowed\"),\n                                                children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Square, {\n                                                    className: \"w-8 h-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Mic, {\n                                                    className: \"w-8 h-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: isRecording ? \"正在錄音中... 點擊停止或等待自動停止\" : \"點擊麥克風開始語音輸入 (錄音5秒)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: message,\n                                                    onChange: (e)=>setMessage(e.target.value),\n                                                    onKeyPress: handleKeyPress,\n                                                    placeholder: \"Type your message or use voice...\",\n                                                    className: \"flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none\",\n                                                    style: {\n                                                        color: \"#000000\"\n                                                    },\n                                                    rows: 2\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: sendMessage,\n                                                    disabled: loading || !message.trim(),\n                                                    className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\",\n                                                    children: \"Send\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(HomePage, \"hJ9jfGuBYf0phepkrt1+aFccOpg=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ })

});