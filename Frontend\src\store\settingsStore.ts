import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { AppSettings, VoiceSettings, UISettings, SettingsStore } from '@/types';

// Default settings
const defaultVoiceSettings: VoiceSettings = {
  voice: {
    id: 'alloy',
    name: 'Alloy',
    language: 'en',
    gender: 'neutral',
  },
  speed: 1.0,
  language: 'en-US',
  autoPlay: true,
};

const defaultUISettings: UISettings = {
  theme: 'system',
  fontSize: 'medium',
  showTimestamps: true,
  showConfidence: true,
  compactMode: false,
  enableAnimations: true,
  enableSounds: true,
};

const defaultAISettings: AppSettings['ai'] = {
  responseLength: 'medium',
  personality: 'helpful',
  contextMemory: 10,
  temperature: 0.7,
};

const defaultSettings: AppSettings = {
  voice: defaultVoiceSettings,
  ui: defaultUISettings,
  ai: defaultAISettings,
};

interface SettingsState extends SettingsStore {
  // Additional state
  isLoading: boolean;
  hasUnsavedChanges: boolean;
  lastSaved?: string;
}

export const useSettingsStore = create<SettingsState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        settings: defaultSettings,
        isLoading: false,
        hasUnsavedChanges: false,
        lastSaved: undefined,

        // Actions
        updateVoiceSettings: (newSettings: Partial<VoiceSettings>) => {
          set(state => ({
            settings: {
              ...state.settings,
              voice: {
                ...state.settings.voice,
                ...newSettings,
              },
            },
            hasUnsavedChanges: true,
          }));

          // Auto-save after a delay
          setTimeout(() => {
            get().saveSettings();
          }, 1000);
        },

        updateUISettings: (newSettings: Partial<UISettings>) => {
          set(state => ({
            settings: {
              ...state.settings,
              ui: {
                ...state.settings.ui,
                ...newSettings,
              },
            },
            hasUnsavedChanges: true,
          }));

          // Apply theme immediately
          if (newSettings.theme) {
            applyTheme(newSettings.theme);
          }

          // Apply font size immediately
          if (newSettings.fontSize) {
            applyFontSize(newSettings.fontSize);
          }

          // Auto-save after a delay
          setTimeout(() => {
            get().saveSettings();
          }, 1000);
        },

        updateAISettings: (newSettings: Partial<AppSettings['ai']>) => {
          set(state => ({
            settings: {
              ...state.settings,
              ai: {
                ...state.settings.ai,
                ...newSettings,
              },
            },
            hasUnsavedChanges: true,
          }));

          // Auto-save after a delay
          setTimeout(() => {
            get().saveSettings();
          }, 1000);
        },

        resetSettings: () => {
          set({
            settings: defaultSettings,
            hasUnsavedChanges: true,
          });

          // Apply default theme and font size
          applyTheme(defaultSettings.ui.theme);
          applyFontSize(defaultSettings.ui.fontSize);

          // Save immediately
          get().saveSettings();
        },

        loadSettings: () => {
          set({ isLoading: true });

          try {
            // Settings are automatically loaded by persist middleware
            const state = get();
            
            // Apply current settings to DOM
            applyTheme(state.settings.ui.theme);
            applyFontSize(state.settings.ui.fontSize);

            set({ 
              isLoading: false,
              hasUnsavedChanges: false,
            });

          } catch (error) {
            console.error('Failed to load settings:', error);
            
            // Fallback to default settings
            set({
              settings: defaultSettings,
              isLoading: false,
              hasUnsavedChanges: false,
            });
          }
        },

        saveSettings: () => {
          try {
            // Settings are automatically saved by persist middleware
            set({
              hasUnsavedChanges: false,
              lastSaved: new Date().toISOString(),
            });

            console.log('Settings saved successfully');

          } catch (error) {
            console.error('Failed to save settings:', error);
          }
        },

        // Additional helper methods
        getVoiceById: (voiceId: string) => {
          // This would typically fetch from available voices
          // For now, return a default voice structure
          return {
            id: voiceId,
            name: voiceId.charAt(0).toUpperCase() + voiceId.slice(1),
            language: 'en',
            gender: 'neutral' as const,
          };
        },

        exportSettings: () => {
          const state = get();
          const exportData = {
            settings: state.settings,
            exportedAt: new Date().toISOString(),
            version: '1.0.0',
          };

          const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json',
          });

          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `voice-chat-settings-${new Date().toISOString().split('T')[0]}.json`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        },

        importSettings: (file: File) => {
          return new Promise<void>((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (event) => {
              try {
                const data = JSON.parse(event.target?.result as string);
                
                if (data.settings && data.version) {
                  set({
                    settings: {
                      ...defaultSettings,
                      ...data.settings,
                    },
                    hasUnsavedChanges: true,
                  });

                  // Apply imported settings
                  const newSettings = get().settings;
                  applyTheme(newSettings.ui.theme);
                  applyFontSize(newSettings.ui.fontSize);

                  get().saveSettings();
                  resolve();
                } else {
                  reject(new Error('Invalid settings file format'));
                }
              } catch (error) {
                reject(new Error('Failed to parse settings file'));
              }
            };

            reader.onerror = () => {
              reject(new Error('Failed to read settings file'));
            };

            reader.readAsText(file);
          });
        },

        validateSettings: (settings: Partial<AppSettings>): boolean => {
          try {
            // Basic validation
            if (settings.voice?.speed && (settings.voice.speed < 0.25 || settings.voice.speed > 4.0)) {
              return false;
            }

            if (settings.ai?.temperature && (settings.ai.temperature < 0 || settings.ai.temperature > 2)) {
              return false;
            }

            if (settings.ai?.contextMemory && (settings.ai.contextMemory < 1 || settings.ai.contextMemory > 50)) {
              return false;
            }

            return true;
          } catch (error) {
            return false;
          }
        },
      }),
      {
        name: 'settings-store',
        version: 1,
        migrate: (persistedState: any, version: number) => {
          // Handle settings migration between versions
          if (version === 0) {
            // Migrate from version 0 to 1
            return {
              ...defaultSettings,
              ...persistedState,
            };
          }
          return persistedState;
        },
      }
    ),
    {
      name: 'settings-store',
    }
  )
);

// Helper functions for applying settings to DOM
function applyTheme(theme: UISettings['theme']) {
  if (typeof window === 'undefined') return;

  const root = document.documentElement;
  
  if (theme === 'dark') {
    root.classList.add('dark');
  } else if (theme === 'light') {
    root.classList.remove('dark');
  } else {
    // System theme
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    if (prefersDark) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }
}

function applyFontSize(fontSize: UISettings['fontSize']) {
  if (typeof window === 'undefined') return;

  const root = document.documentElement;
  
  // Remove existing font size classes
  root.classList.remove('text-sm', 'text-base', 'text-lg');
  
  // Apply new font size
  switch (fontSize) {
    case 'small':
      root.classList.add('text-sm');
      break;
    case 'large':
      root.classList.add('text-lg');
      break;
    default:
      root.classList.add('text-base');
      break;
  }
}

// Initialize theme on load
if (typeof window !== 'undefined') {
  // Listen for system theme changes
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  mediaQuery.addEventListener('change', (e) => {
    const settings = useSettingsStore.getState().settings;
    if (settings.ui.theme === 'system') {
      applyTheme('system');
    }
  });
}
