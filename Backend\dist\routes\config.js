"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const errorHandler_1 = require("@/middleware/errorHandler");
const openai_1 = require("@/config/openai");
const environment_1 = require("@/config/environment");
const router = (0, express_1.Router)();
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const clientConfig = {
        features: {
            speechRecognition: true,
            speechSynthesis: true,
            audioRecording: true,
        },
        limits: {
            maxRecordingDuration: environment_1.config.MAX_RECORDING_DURATION,
            maxMessageLength: 1000,
            maxConversationHistory: 100,
            maxFileSize: environment_1.config.MAX_FILE_SIZE,
        },
        voiceOptions: openai_1.AVAILABLE_VOICES.map(voice => ({
            id: voice.id,
            name: voice.name,
            language: voice.language,
            gender: voice.gender,
        })),
        supportedLanguages: [
            { code: 'en', name: 'English', nativeName: 'English' },
            { code: 'es', name: 'Spanish', nativeName: 'Español' },
            { code: 'fr', name: 'French', nativeName: 'Français' },
            { code: 'de', name: 'German', nativeName: 'Deutsch' },
            { code: 'it', name: 'Italian', nativeName: 'Italiano' },
            { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
            { code: 'ru', name: 'Russian', nativeName: 'Русский' },
            { code: 'ja', name: 'Japanese', nativeName: '日本語' },
            { code: 'ko', name: 'Korean', nativeName: '한국어' },
            { code: 'zh', name: 'Chinese', nativeName: '中文' },
        ],
    };
    res.json({
        success: true,
        data: clientConfig,
        message: 'Client configuration retrieved successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.get('/voices', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const voices = openai_1.AVAILABLE_VOICES.map(voice => ({
        id: voice.id,
        name: voice.name,
        language: voice.language,
        gender: voice.gender,
        description: `${voice.name} - ${voice.gender} voice in ${voice.language}`,
    }));
    res.json({
        success: true,
        data: voices,
        message: 'Voice options retrieved successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.get('/languages', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const languages = [
        {
            code: 'en',
            name: 'English',
            nativeName: 'English',
            regions: ['US', 'UK', 'AU', 'CA'],
            speechRecognition: true,
            textToSpeech: true,
        },
        {
            code: 'es',
            name: 'Spanish',
            nativeName: 'Español',
            regions: ['ES', 'MX', 'AR'],
            speechRecognition: true,
            textToSpeech: true,
        },
        {
            code: 'fr',
            name: 'French',
            nativeName: 'Français',
            regions: ['FR', 'CA'],
            speechRecognition: true,
            textToSpeech: true,
        },
        {
            code: 'de',
            name: 'German',
            nativeName: 'Deutsch',
            regions: ['DE', 'AT', 'CH'],
            speechRecognition: true,
            textToSpeech: true,
        },
        {
            code: 'it',
            name: 'Italian',
            nativeName: 'Italiano',
            regions: ['IT'],
            speechRecognition: true,
            textToSpeech: true,
        },
        {
            code: 'pt',
            name: 'Portuguese',
            nativeName: 'Português',
            regions: ['PT', 'BR'],
            speechRecognition: true,
            textToSpeech: true,
        },
        {
            code: 'ru',
            name: 'Russian',
            nativeName: 'Русский',
            regions: ['RU'],
            speechRecognition: true,
            textToSpeech: true,
        },
        {
            code: 'ja',
            name: 'Japanese',
            nativeName: '日本語',
            regions: ['JP'],
            speechRecognition: true,
            textToSpeech: true,
        },
        {
            code: 'ko',
            name: 'Korean',
            nativeName: '한국어',
            regions: ['KR'],
            speechRecognition: true,
            textToSpeech: true,
        },
        {
            code: 'zh',
            name: 'Chinese',
            nativeName: '中文',
            regions: ['CN', 'TW', 'HK'],
            speechRecognition: true,
            textToSpeech: true,
        },
    ];
    res.json({
        success: true,
        data: languages,
        message: 'Supported languages retrieved successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.get('/limits', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const limits = {
        rateLimits: {
            general: {
                requests: environment_1.config.RATE_LIMIT_MAX_REQUESTS,
                windowMs: environment_1.config.RATE_LIMIT_WINDOW_MS,
                description: 'General API requests per minute',
            },
            voice: {
                requests: environment_1.config.RATE_LIMIT_VOICE_MAX_REQUESTS,
                windowMs: environment_1.config.RATE_LIMIT_WINDOW_MS,
                description: 'Voice processing requests per minute',
            },
            chat: {
                requests: 30,
                windowMs: environment_1.config.RATE_LIMIT_WINDOW_MS,
                description: 'Chat messages per minute',
            },
            upload: {
                requests: 10,
                windowMs: environment_1.config.RATE_LIMIT_WINDOW_MS,
                description: 'File uploads per minute',
            },
        },
        fileLimits: {
            maxFileSize: environment_1.config.MAX_FILE_SIZE,
            maxRecordingDuration: environment_1.config.MAX_RECORDING_DURATION,
            allowedFormats: environment_1.config.ALLOWED_AUDIO_FORMATS.split(','),
        },
        messageLimits: {
            maxMessageLength: 1000,
            maxConversationHistory: 100,
            maxContextMessages: 50,
        },
        aiLimits: {
            maxTokens: environment_1.config.OPENAI_MAX_TOKENS,
            model: environment_1.config.OPENAI_MODEL,
            temperature: environment_1.config.OPENAI_TEMPERATURE,
        },
    };
    res.json({
        success: true,
        data: limits,
        message: 'API limits retrieved successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.get('/features', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const features = {
        voiceProcessing: {
            enabled: true,
            speechToText: true,
            textToSpeech: true,
            realTimeTranscription: true,
            voiceCloning: false,
        },
        chat: {
            enabled: true,
            conversationHistory: true,
            contextAwareness: true,
            multiLanguage: true,
            fileAttachments: false,
        },
        ui: {
            darkMode: true,
            accessibility: true,
            mobileOptimized: true,
            offlineMode: false,
        },
        analytics: {
            usageTracking: process.env.NODE_ENV === 'production',
            errorReporting: process.env.NODE_ENV === 'production',
            performanceMonitoring: true,
        },
        experimental: {
            backgroundProcessing: false,
            advancedVoiceSettings: false,
            customVoiceTraining: false,
            multiUserConversations: false,
        },
    };
    res.json({
        success: true,
        data: features,
        message: 'Feature flags retrieved successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.get('/system', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const systemInfo = {
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV,
        nodeVersion: process.version,
        platform: process.platform,
        architecture: process.arch,
        uptime: Math.floor(process.uptime()),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        locale: 'en-US',
        apiVersion: 'v1',
        buildDate: new Date().toISOString(),
        supportedProtocols: ['HTTP/1.1', 'HTTP/2'],
        cors: {
            enabled: true,
            origins: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
        },
    };
    res.json({
        success: true,
        data: systemInfo,
        message: 'System information retrieved successfully',
        timestamp: new Date().toISOString(),
    });
}));
router.get('/docs', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const documentation = {
        apiDocs: process.env.NODE_ENV === 'development' ? '/api-docs' : null,
        openApiSpec: process.env.NODE_ENV === 'development' ? '/api/openapi.json' : null,
        guides: {
            gettingStarted: '/docs/getting-started',
            authentication: '/docs/authentication',
            voiceProcessing: '/docs/voice-processing',
            errorHandling: '/docs/error-handling',
        },
        examples: {
            curl: '/docs/examples/curl',
            javascript: '/docs/examples/javascript',
            python: '/docs/examples/python',
        },
        changelog: '/docs/changelog',
        support: {
            email: '<EMAIL>',
            github: 'https://github.com/your-org/voice-chat-app',
            discord: 'https://discord.gg/your-server',
        },
    };
    res.json({
        success: true,
        data: documentation,
        message: 'Documentation links retrieved successfully',
        timestamp: new Date().toISOString(),
    });
}));
exports.default = router;
//# sourceMappingURL=config.js.map