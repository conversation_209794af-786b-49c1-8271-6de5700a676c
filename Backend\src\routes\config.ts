import { Router } from 'express';
import { async<PERSON>and<PERSON> } from '@/middleware/errorHandler';
import { AVAILABLE_VOICES } from '@/config/gemini';
import { config } from '@/config/environment';
import { ClientConfig } from '@/types';

const router = Router();

// Get client configuration
router.get('/', asyncHandler(async (req, res) => {
  const clientConfig: ClientConfig = {
    features: {
      speechRecognition: true, // Web Speech API
      speechSynthesis: true,   // Web Speech API
      audioRecording: true,    // MediaRecorder API
    },
    limits: {
      maxRecordingDuration: config.MAX_RECORDING_DURATION,
      maxMessageLength: 1000,
      maxConversationHistory: 100,
      maxFileSize: config.MAX_FILE_SIZE,
    },
    voiceOptions: AVAILABLE_VOICES.map(voice => ({
      id: voice.id,
      name: voice.name,
      language: voice.language,
      gender: voice.gender,
    })),
    supportedLanguages: [
      { code: 'en', name: 'English', nativeName: 'English' },
      { code: 'es', name: 'Spanish', nativeName: 'Español' },
      { code: 'fr', name: 'French', nativeName: 'Français' },
      { code: 'de', name: 'German', nativeName: 'Deutsch' },
      { code: 'it', name: 'Italian', nativeName: 'Italiano' },
      { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
      { code: 'ru', name: 'Russian', nativeName: 'Русский' },
      { code: 'ja', name: 'Japanese', nativeName: '日本語' },
      { code: 'ko', name: 'Korean', nativeName: '한국어' },
      { code: 'zh', name: 'Chinese', nativeName: '中文' },
    ],
  };

  res.json({
    success: true,
    data: clientConfig,
    message: 'Client configuration retrieved successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Get voice options
router.get('/voices', asyncHandler(async (req, res) => {
  const voices = AVAILABLE_VOICES.map(voice => ({
    id: voice.id,
    name: voice.name,
    language: voice.language,
    gender: voice.gender,
    description: `${voice.name} - ${voice.gender} voice in ${voice.language}`,
  }));

  res.json({
    success: true,
    data: voices,
    message: 'Voice options retrieved successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Get supported languages
router.get('/languages', asyncHandler(async (req, res) => {
  const languages = [
    { 
      code: 'en', 
      name: 'English', 
      nativeName: 'English',
      regions: ['US', 'UK', 'AU', 'CA'],
      speechRecognition: true,
      textToSpeech: true,
    },
    { 
      code: 'es', 
      name: 'Spanish', 
      nativeName: 'Español',
      regions: ['ES', 'MX', 'AR'],
      speechRecognition: true,
      textToSpeech: true,
    },
    { 
      code: 'fr', 
      name: 'French', 
      nativeName: 'Français',
      regions: ['FR', 'CA'],
      speechRecognition: true,
      textToSpeech: true,
    },
    { 
      code: 'de', 
      name: 'German', 
      nativeName: 'Deutsch',
      regions: ['DE', 'AT', 'CH'],
      speechRecognition: true,
      textToSpeech: true,
    },
    { 
      code: 'it', 
      name: 'Italian', 
      nativeName: 'Italiano',
      regions: ['IT'],
      speechRecognition: true,
      textToSpeech: true,
    },
    { 
      code: 'pt', 
      name: 'Portuguese', 
      nativeName: 'Português',
      regions: ['PT', 'BR'],
      speechRecognition: true,
      textToSpeech: true,
    },
    { 
      code: 'ru', 
      name: 'Russian', 
      nativeName: 'Русский',
      regions: ['RU'],
      speechRecognition: true,
      textToSpeech: true,
    },
    { 
      code: 'ja', 
      name: 'Japanese', 
      nativeName: '日本語',
      regions: ['JP'],
      speechRecognition: true,
      textToSpeech: true,
    },
    { 
      code: 'ko', 
      name: 'Korean', 
      nativeName: '한국어',
      regions: ['KR'],
      speechRecognition: true,
      textToSpeech: true,
    },
    { 
      code: 'zh', 
      name: 'Chinese', 
      nativeName: '中文',
      regions: ['CN', 'TW', 'HK'],
      speechRecognition: true,
      textToSpeech: true,
    },
  ];

  res.json({
    success: true,
    data: languages,
    message: 'Supported languages retrieved successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Get API limits and quotas
router.get('/limits', asyncHandler(async (req, res) => {
  const limits = {
    rateLimits: {
      general: {
        requests: config.RATE_LIMIT_MAX_REQUESTS,
        windowMs: config.RATE_LIMIT_WINDOW_MS,
        description: 'General API requests per minute',
      },
      voice: {
        requests: config.RATE_LIMIT_VOICE_MAX_REQUESTS,
        windowMs: config.RATE_LIMIT_WINDOW_MS,
        description: 'Voice processing requests per minute',
      },
      chat: {
        requests: 30,
        windowMs: config.RATE_LIMIT_WINDOW_MS,
        description: 'Chat messages per minute',
      },
      upload: {
        requests: 10,
        windowMs: config.RATE_LIMIT_WINDOW_MS,
        description: 'File uploads per minute',
      },
    },
    fileLimits: {
      maxFileSize: config.MAX_FILE_SIZE,
      maxRecordingDuration: config.MAX_RECORDING_DURATION,
      allowedFormats: config.ALLOWED_AUDIO_FORMATS.split(','),
    },
    messageLimits: {
      maxMessageLength: 1000,
      maxConversationHistory: 100,
      maxContextMessages: 50,
    },
    aiLimits: {
      maxTokens: config.GEMINI_MAX_TOKENS,
      model: config.MODEL_NAME,
      temperature: config.GEMINI_TEMPERATURE,
    },
  };

  res.json({
    success: true,
    data: limits,
    message: 'API limits retrieved successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Get feature flags
router.get('/features', asyncHandler(async (req, res) => {
  const features = {
    voiceProcessing: {
      enabled: true,
      speechToText: true,
      textToSpeech: true,
      realTimeTranscription: true,
      voiceCloning: false, // Future feature
    },
    chat: {
      enabled: true,
      conversationHistory: true,
      contextAwareness: true,
      multiLanguage: true,
      fileAttachments: false, // Future feature
    },
    ui: {
      darkMode: true,
      accessibility: true,
      mobileOptimized: true,
      offlineMode: false, // Future feature
    },
    analytics: {
      usageTracking: process.env.NODE_ENV === 'production',
      errorReporting: process.env.NODE_ENV === 'production',
      performanceMonitoring: true,
    },
    experimental: {
      backgroundProcessing: false,
      advancedVoiceSettings: false,
      customVoiceTraining: false,
      multiUserConversations: false,
    },
  };

  res.json({
    success: true,
    data: features,
    message: 'Feature flags retrieved successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Get system information (non-sensitive)
router.get('/system', asyncHandler(async (req, res) => {
  const systemInfo = {
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV,
    nodeVersion: process.version,
    platform: process.platform,
    architecture: process.arch,
    uptime: Math.floor(process.uptime()),
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    locale: 'en-US',
    apiVersion: 'v1',
    buildDate: new Date().toISOString(), // In real app, this would be build time
    supportedProtocols: ['HTTP/1.1', 'HTTP/2'],
    cors: {
      enabled: true,
      origins: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    },
  };

  res.json({
    success: true,
    data: systemInfo,
    message: 'System information retrieved successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Get API documentation links
router.get('/docs', asyncHandler(async (req, res) => {
  const documentation = {
    apiDocs: process.env.NODE_ENV === 'development' ? '/api-docs' : null,
    openApiSpec: process.env.NODE_ENV === 'development' ? '/api/openapi.json' : null,
    guides: {
      gettingStarted: '/docs/getting-started',
      authentication: '/docs/authentication',
      voiceProcessing: '/docs/voice-processing',
      errorHandling: '/docs/error-handling',
    },
    examples: {
      curl: '/docs/examples/curl',
      javascript: '/docs/examples/javascript',
      python: '/docs/examples/python',
    },
    changelog: '/docs/changelog',
    support: {
      email: '<EMAIL>',
      github: 'https://github.com/your-org/voice-chat-app',
      discord: 'https://discord.gg/your-server',
    },
  };

  res.json({
    success: true,
    data: documentation,
    message: 'Documentation links retrieved successfully',
    timestamp: new Date().toISOString(),
  });
}));

export default router;
