export interface TranscribeRequest {
    audioBuffer: Buffer;
    language?: string;
    model?: string;
    temperature?: number;
}
export interface TranscribeResponse {
    transcript: string;
    confidence: number;
    language: string;
    duration: number;
    alternatives?: Array<{
        transcript: string;
        confidence: number;
    }>;
}
export interface SynthesizeRequest {
    text: string;
    voice?: string;
    speed?: number;
    model?: string;
}
export interface SynthesizeResponse {
    audioUrl: string;
    duration: number;
    format: string;
    size: number;
}
export declare class VoiceService {
    private uploadDir;
    constructor();
    transcribeAudio(request: TranscribeRequest): Promise<TranscribeResponse>;
    synthesizeSpeech(request: SynthesizeRequest): Promise<SynthesizeResponse>;
    saveAudioFile(audioBuffer: Buffer, originalName: string, mimeType: string, metadata?: Record<string, any>): Promise<{
        filename: string;
        url: string;
    }>;
    getAudioFile(filename: string): Promise<{
        buffer: Buffer;
        mimeType: string;
    } | null>;
    cleanupExpiredFiles(): Promise<number>;
}
export default VoiceService;
//# sourceMappingURL=voiceService.d.ts.map