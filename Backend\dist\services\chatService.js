"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatService = void 0;
const gemini_1 = require("@/config/gemini");
const database_1 = require("@/config/database");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
class ChatService {
    async processMessage(request) {
        const startTime = Date.now();
        try {
            logger_1.logger.info('Processing chat message', {
                messageLength: request.message.length,
                conversationId: request.conversationId,
                userId: request.userId,
            });
            let conversation;
            if (request.conversationId) {
                conversation = await database_1.prisma.conversation.findUnique({
                    where: { id: request.conversationId },
                    include: {
                        messages: {
                            orderBy: { createdAt: 'desc' },
                            take: request.context?.previousMessages || 5,
                        },
                    },
                });
                if (!conversation) {
                    throw errorHandler_1.createError.notFound('Conversation');
                }
            }
            else {
                conversation = await database_1.prisma.conversation.create({
                    data: {
                        userId: request.userId,
                        title: this.generateConversationTitle(request.message),
                        metadata: JSON.stringify({}),
                    },
                    include: {
                        messages: true,
                    },
                });
            }
            const messages = this.buildConversationContext(conversation.messages || [], request.message);
            const aiResponse = await (0, gemini_1.generateChatCompletion)(messages, {
                maxTokens: gemini_1.GEMINI_CONFIG.maxTokens,
                temperature: gemini_1.GEMINI_CONFIG.temperature,
                model: gemini_1.GEMINI_CONFIG.model,
            });
            const responseText = aiResponse.choices[0]?.message?.content || '';
            const tokensUsed = aiResponse.usage?.total_tokens || 0;
            await database_1.prisma.message.create({
                data: {
                    conversationId: conversation.id,
                    role: 'USER',
                    content: request.message,
                    metadata: JSON.stringify({
                        timestamp: new Date().toISOString(),
                    }),
                },
            });
            await database_1.prisma.message.create({
                data: {
                    conversationId: conversation.id,
                    role: 'ASSISTANT',
                    content: responseText,
                    tokensUsed,
                    responseTime: Date.now() - startTime,
                    modelUsed: gemini_1.GEMINI_CONFIG.model,
                    metadata: JSON.stringify({
                        timestamp: new Date().toISOString(),
                        finishReason: aiResponse.choices[0]?.finish_reason,
                    }),
                },
            });
            await database_1.prisma.conversation.update({
                where: { id: conversation.id },
                data: { updatedAt: new Date() },
            });
            const responseTime = Date.now() - startTime;
            (0, logger_1.logAPI)('gemini', 'chat-completion', responseTime);
            logger_1.logger.info('Chat message processed successfully', {
                conversationId: conversation.id,
                responseTime,
                tokensUsed,
                responseLength: responseText.length,
            });
            return {
                conversationId: conversation.id,
                response: responseText,
                metadata: {
                    responseTime,
                    tokensUsed,
                    modelUsed: gemini_1.GEMINI_CONFIG.model,
                },
            };
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            logger_1.logger.error('Failed to process chat message', {
                error: error.message,
                responseTime,
                messageLength: request.message.length,
            });
            if (error.error?.type === 'rate_limit_error') {
                throw errorHandler_1.createError.rateLimit('Gemini API rate limit exceeded');
            }
            if (error.error?.type === 'authentication_error') {
                throw errorHandler_1.createError.aiService('Gemini API authentication failed', 401);
            }
            throw errorHandler_1.createError.aiService(error.error?.message || 'Failed to process message', error.statusCode || 500);
        }
    }
    buildConversationContext(previousMessages, currentMessage) {
        const messages = [];
        messages.push({
            role: 'system',
            content: gemini_1.GEMINI_CONFIG.systemPrompt,
        });
        const sortedMessages = previousMessages
            .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
            .slice(-10);
        for (const msg of sortedMessages) {
            messages.push({
                role: msg.role.toLowerCase(),
                content: msg.content,
            });
        }
        messages.push({
            role: 'user',
            content: currentMessage,
        });
        return messages;
    }
    generateConversationTitle(firstMessage) {
        const words = firstMessage.trim().split(/\s+/);
        const title = words.slice(0, 6).join(' ');
        if (title.length > 50) {
            return title.substring(0, 47) + '...';
        }
        return title || 'New Conversation';
    }
}
exports.ChatService = ChatService;
exports.default = ChatService;
//# sourceMappingURL=chatService.js.map