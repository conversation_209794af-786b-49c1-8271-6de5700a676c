"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.startServer = startServer;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const environment_1 = require("@/config/environment");
const errorHandler_1 = require("@/middleware/errorHandler");
const requestLogger_1 = require("@/middleware/requestLogger");
const rateLimiter_1 = require("@/middleware/rateLimiter");
const errorHandler_2 = require("@/middleware/errorHandler");
const logger_1 = require("@/utils/logger");
const health_1 = __importDefault(require("@/routes/health"));
const config_1 = __importDefault(require("@/routes/config"));
const chat_1 = __importDefault(require("@/routes/chat"));
const voice_1 = __importDefault(require("@/routes/voice"));
const audio_1 = __importDefault(require("@/routes/audio"));
const feedback_1 = __importDefault(require("@/routes/feedback"));
const app = (0, express_1.default)();
(0, errorHandler_1.setupGlobalErrorHandlers)();
app.use((0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
    crossOriginEmbedderPolicy: false,
}));
app.use((0, cors_1.default)({
    origin: environment_1.config.CORS_ORIGIN.split(',').map(origin => origin.trim()),
    credentials: environment_1.config.CORS_CREDENTIALS,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-Request-ID',
        'X-Request-Time',
        'X-User-Agent',
    ],
    exposedHeaders: [
        'X-Request-ID',
        'X-RateLimit-Limit',
        'X-RateLimit-Remaining',
        'X-RateLimit-Reset',
    ],
}));
app.use((0, compression_1.default)());
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use(requestLogger_1.requestLogger);
if (environment_1.config.RATE_LIMIT_ENABLED) {
    app.use('/api', rateLimiter_1.rateLimiter);
}
app.use('/api/health', health_1.default);
app.use('/api/config', config_1.default);
app.use('/api/chat', chat_1.default);
app.use('/api/voice', voice_1.default);
app.use('/api/audio', audio_1.default);
app.use('/api/feedback', feedback_1.default);
app.get('/', (req, res) => {
    res.json({
        success: true,
        message: 'Voice AI Chat API',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        endpoints: {
            health: '/api/health',
            config: '/api/config',
            chat: '/api/chat',
            voice: '/api/voice',
            audio: '/api/audio',
            feedback: '/api/feedback',
        },
    });
});
if (environment_1.config.NODE_ENV === 'development') {
    app.get('/api', (req, res) => {
        res.json({
            success: true,
            message: 'Voice AI Chat API Documentation',
            version: '1.0.0',
            environment: environment_1.config.NODE_ENV,
            endpoints: {
                health: {
                    'GET /api/health': 'Basic health check',
                    'GET /api/health/detailed': 'Detailed health check',
                    'GET /api/health/ready': 'Readiness probe',
                    'GET /api/health/live': 'Liveness probe',
                },
                config: {
                    'GET /api/config': 'Get client configuration',
                    'GET /api/config/voices': 'Get available voices',
                    'GET /api/config/languages': 'Get supported languages',
                    'GET /api/config/limits': 'Get API limits',
                    'GET /api/config/features': 'Get feature flags',
                },
                chat: {
                    'POST /api/chat/message': 'Send chat message',
                    'GET /api/chat/history/:id': 'Get conversation history',
                    'GET /api/chat/conversations': 'Get user conversations',
                    'DELETE /api/chat/history/:id': 'Delete conversation',
                    'GET /api/chat/stats': 'Get conversation statistics',
                },
                voice: {
                    'POST /api/voice/transcribe': 'Transcribe audio to text',
                    'POST /api/voice/synthesize': 'Synthesize text to speech',
                    'POST /api/voice/process': 'Complete voice interaction',
                    'GET /api/voice/voices': 'Get available voices',
                },
                audio: {
                    'POST /api/audio/upload': 'Upload audio file',
                    'GET /api/audio/:filename': 'Download audio file',
                    'GET /api/audio/:filename/info': 'Get audio file info',
                    'POST /api/audio/cleanup': 'Cleanup expired files',
                },
                feedback: {
                    'POST /api/feedback': 'Submit feedback',
                    'GET /api/feedback/stats': 'Get feedback statistics',
                    'GET /api/feedback/recent': 'Get recent feedback',
                },
            },
        });
    });
}
app.use(errorHandler_2.notFoundHandler);
app.use(errorHandler_2.errorHandler);
function startServer(port = environment_1.config.PORT) {
    return new Promise((resolve, reject) => {
        try {
            const server = app.listen(port, () => {
                (0, logger_1.logStartup)('voice-ai-chat-api', '1.0.0', port);
                logger_1.logger.info(`Server started successfully on port ${port}`);
                resolve();
            });
            server.on('error', (error) => {
                if (error.code === 'EADDRINUSE') {
                    logger_1.logger.error(`Port ${port} is already in use`);
                    reject(new Error(`Port ${port} is already in use`));
                }
                else {
                    logger_1.logger.error('Server startup error:', error);
                    reject(error);
                }
            });
            process.on('SIGTERM', () => {
                logger_1.logger.info('SIGTERM received, shutting down gracefully');
                server.close(() => {
                    logger_1.logger.info('Server closed');
                    process.exit(0);
                });
            });
            process.on('SIGINT', () => {
                logger_1.logger.info('SIGINT received, shutting down gracefully');
                server.close(() => {
                    logger_1.logger.info('Server closed');
                    process.exit(0);
                });
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to start server:', error);
            reject(error);
        }
    });
}
exports.default = app;
//# sourceMappingURL=app.js.map