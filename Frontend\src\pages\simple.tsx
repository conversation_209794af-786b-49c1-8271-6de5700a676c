import React, { useState, useEffect } from 'react';
import Head from 'next/head';

export default function SimplePage() {
  const [message, setMessage] = useState('');
  const [response, setResponse] = useState('');
  const [loading, setLoading] = useState(false);
  const [backendStatus, setBackendStatus] = useState('checking...');

  // Check backend status on load
  useEffect(() => {
    checkBackendStatus();
  }, []);

  const checkBackendStatus = async () => {
    try {
      const res = await fetch('http://localhost:8000/api/health');
      if (res.ok) {
        const data = await res.json();
        setBackendStatus(`✅ Connected (${data.data.status})`);
      } else {
        setBackendStatus('❌ Backend Error');
      }
    } catch (error) {
      setBackendStatus('❌ Backend Offline');
    }
  };

  const sendMessage = async () => {
    if (!message.trim()) return;

    setLoading(true);
    try {
      const res = await fetch('http://localhost:8000/api/chat/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message }),
      });

      if (res.ok) {
        const data = await res.json();
        setResponse(data.data.response);
      } else {
        setResponse('Error: Failed to get response');
      }
    } catch (error) {
      setResponse('Error: Cannot connect to backend');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Simple Voice AI Chat Test</title>
      </Head>

      <div className="min-h-screen bg-gray-100 p-8">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            Voice AI Chat - Simple Test
          </h1>

          {/* Backend Status */}
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-lg font-semibold mb-2">Backend Status</h2>
            <p className="text-gray-600">{backendStatus}</p>
            <button
              onClick={checkBackendStatus}
              className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Refresh Status
            </button>
          </div>

          {/* Chat Interface */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Test Chat</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Your Message:
                </label>
                <textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  placeholder="Type your message here..."
                />
              </div>

              <button
                onClick={sendMessage}
                disabled={loading || !message.trim()}
                className="w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                {loading ? 'Sending...' : 'Send Message'}
              </button>

              {response && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    AI Response:
                  </label>
                  <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                    <p className="text-gray-800">{response}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Navigation */}
          <div className="mt-8 text-center">
            <a
              href="/"
              className="inline-block px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              Go to Full Voice AI Chat
            </a>
          </div>
        </div>
      </div>
    </>
  );
}
