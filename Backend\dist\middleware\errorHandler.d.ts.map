{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAK1D,qBAAa,QAAS,SAAQ,KAAK;IAC1B,UAAU,EAAE,MAAM,CAAC;IACnB,IAAI,EAAE,MAAM,CAAC;IACb,aAAa,EAAE,OAAO,CAAC;gBAElB,OAAO,EAAE,MAAM,EAAE,UAAU,GAAE,MAAY,EAAE,IAAI,GAAE,MAAyB;CAQvF;AAGD,qBAAa,eAAgB,SAAQ,QAAQ;IACpC,OAAO,EAAE,GAAG,CAAC;gBAER,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAI3C;AAGD,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,QAAQ,GAAE,MAAmB;CAG1C;AAGD,qBAAa,iBAAkB,SAAQ,QAAQ;gBACjC,OAAO,GAAE,MAA8B;CAGpD;AAGD,qBAAa,cAAe,SAAQ,QAAQ;gBAC9B,OAAO,GAAE,MAA2B;CAGjD;AAGD,qBAAa,cAAe,SAAQ,QAAQ;gBAC9B,OAAO,GAAE,MAA8B;CAGpD;AAGD,qBAAa,eAAgB,SAAQ,QAAQ;gBAC/B,OAAO,EAAE,MAAM;CAG5B;AAGD,qBAAa,oBAAqB,SAAQ,QAAQ;gBACpC,OAAO,EAAE,MAAM;CAG5B;AAGD,qBAAa,cAAe,SAAQ,QAAQ;gBAC9B,OAAO,EAAE,MAAM,EAAE,UAAU,GAAE,MAAY;CAGtD;AAGD,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,EAAE,MAAM;CAG5B;AAeD,wBAAgB,YAAY,CAC1B,KAAK,EAAE,KAAK,EACZ,GAAG,EAAE,OAAO,EACZ,GAAG,EAAE,QAAQ,EACb,IAAI,EAAE,YAAY,GACjB,IAAI,CA6GN;AAGD,wBAAgB,YAAY,CAC1B,EAAE,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,KAAK,OAAO,CAAC,GAAG,CAAC,IAE7D,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,UAGxD;AAGD,wBAAgB,eAAe,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,GAAG,IAAI,CAGrF;AAGD,wBAAgB,wBAAwB,IAAI,IAAI,CAU/C;AAGD,eAAO,MAAM,WAAW;0BACA,MAAM,YAAY,GAAG;0BACrB,MAAM;6BACH,MAAM;0BACT,MAAM;0BACN,MAAM;0BACN,MAAM;+BACD,MAAM;yBACZ,MAAM,eAAe,MAAM;wBAC5B,MAAM;uBACP,MAAM,eAAe,MAAM,SAAS,MAAM;CAE9D,CAAC"}