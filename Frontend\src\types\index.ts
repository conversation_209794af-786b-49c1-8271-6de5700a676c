// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  timestamp: string;
}

export interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

// Chat Types
export interface Message {
  id: string;
  conversationId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  audioUrl?: string;
  audioDuration?: number;
  audioFormat?: string;
  audioSize?: number;
  metadata?: Record<string, any>;
  transcript?: string;
  transcriptConfidence?: number;
  transcriptLanguage?: string;
  tokensUsed?: number;
  responseTime?: number;
  modelUsed?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Conversation {
  id: string;
  userId?: string;
  title?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  messages?: Message[];
}

export interface SendMessageRequest {
  message: string;
  conversationId?: string;
  context?: {
    previousMessages?: number;
    userPreferences?: Record<string, any>;
  };
}

export interface SendMessageResponse {
  conversationId: string;
  response: string;
  audioUrl?: string;
  metadata: {
    responseTime: number;
    tokensUsed: number;
    confidence?: number;
    modelUsed: string;
  };
}

// Voice Processing Types
export interface VoiceSettings {
  voice: VoiceOption;
  speed: number;
  language: string;
  autoPlay: boolean;
}

export interface VoiceOption {
  id: string;
  name: string;
  language: string;
  gender: 'male' | 'female' | 'neutral';
}

export interface TranscriptionResult {
  transcript: string;
  confidence: number;
  language: string;
  duration: number;
  alternatives?: Array<{
    transcript: string;
    confidence: number;
  }>;
}

export interface SpeechSynthesisOptions {
  text: string;
  voice?: string;
  speed?: number;
  pitch?: number;
  volume?: number;
}

// Audio Recording Types
export interface AudioRecording {
  blob: Blob;
  duration: number;
  size: number;
  mimeType: string;
  url?: string;
}

export interface RecordingState {
  isRecording: boolean;
  isProcessing: boolean;
  duration: number;
  audioLevel: number;
  error?: string;
}

// UI State Types
export interface UISettings {
  theme: 'light' | 'dark' | 'system';
  fontSize: 'small' | 'medium' | 'large';
  showTimestamps: boolean;
  showConfidence: boolean;
  compactMode: boolean;
  enableAnimations: boolean;
  enableSounds: boolean;
}

export interface AppSettings {
  voice: VoiceSettings;
  ui: UISettings;
  ai: {
    responseLength: 'short' | 'medium' | 'long';
    personality: string;
    contextMemory: number;
    temperature: number;
  };
}

// Component Props Types
export interface PushToTalkButtonProps {
  onStartRecording: () => void;
  onStopRecording: () => void;
  isRecording: boolean;
  isProcessing: boolean;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export interface MessageBubbleProps {
  message: Message;
  onPlayAudio?: (audioUrl: string) => void;
  onCopyText?: (text: string) => void;
  showTimestamp?: boolean;
  showConfidence?: boolean;
  className?: string;
}

export interface ChatContainerProps {
  messages: Message[];
  isLoading?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
  className?: string;
}

export interface VoiceVisualizerProps {
  audioLevel: number;
  isActive: boolean;
  barCount?: number;
  className?: string;
}

export interface TranscriptionDisplayProps {
  transcript: string;
  confidence?: number;
  language?: string;
  isLive?: boolean;
  className?: string;
}

// Hook Types
export interface UseSpeechRecognitionOptions {
  language?: string;
  continuous?: boolean;
  interimResults?: boolean;
  maxAlternatives?: number;
}

export interface UseSpeechRecognitionReturn {
  transcript: string;
  interimTranscript: string;
  finalTranscript: string;
  isListening: boolean;
  hasRecognitionSupport: boolean;
  startListening: () => void;
  stopListening: () => void;
  resetTranscript: () => void;
  error?: string;
}

export interface UseSpeechSynthesisOptions {
  voice?: SpeechSynthesisVoice;
  rate?: number;
  pitch?: number;
  volume?: number;
}

export interface UseSpeechSynthesisReturn {
  speak: (text: string, options?: UseSpeechSynthesisOptions) => void;
  cancel: () => void;
  pause: () => void;
  resume: () => void;
  speaking: boolean;
  supported: boolean;
  voices: SpeechSynthesisVoice[];
}

export interface UseAudioRecorderOptions {
  mimeType?: string;
  audioBitsPerSecond?: number;
  maxDuration?: number;
  onDataAvailable?: (data: Blob) => void;
  onStop?: (recording: AudioRecording) => void;
  onError?: (error: Error) => void;
}

export interface UseAudioRecorderReturn {
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  pauseRecording: () => void;
  resumeRecording: () => void;
  recordingState: RecordingState;
  audioLevel: number;
  hasPermission: boolean;
  requestPermission: () => Promise<boolean>;
}

// Store Types
export interface ChatStore {
  // State
  conversations: Conversation[];
  currentConversation?: Conversation;
  messages: Message[];
  isLoading: boolean;
  error?: string;
  
  // Actions
  sendMessage: (message: string, conversationId?: string) => Promise<void>;
  loadConversations: () => Promise<void>;
  loadConversation: (conversationId: string) => Promise<void>;
  createConversation: () => Promise<string>;
  deleteConversation: (conversationId: string) => Promise<void>;
  clearMessages: () => void;
  setError: (error: string | null) => void;
}

export interface VoiceStore {
  // State
  isRecording: boolean;
  isProcessing: boolean;
  audioLevel: number;
  transcript: string;
  confidence: number;
  error?: string;
  
  // Actions
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<void>;
  processAudio: (audioBlob: Blob) => Promise<void>;
  playAudio: (audioUrl: string) => Promise<void>;
  setAudioLevel: (level: number) => void;
  setTranscript: (transcript: string, confidence?: number) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

export interface SettingsStore {
  // State
  settings: AppSettings;
  
  // Actions
  updateVoiceSettings: (settings: Partial<VoiceSettings>) => void;
  updateUISettings: (settings: Partial<UISettings>) => void;
  updateAISettings: (settings: Partial<AppSettings['ai']>) => void;
  resetSettings: () => void;
  loadSettings: () => void;
  saveSettings: () => void;
}

// Configuration Types
export interface ClientConfig {
  features: {
    speechRecognition: boolean;
    speechSynthesis: boolean;
    audioRecording: boolean;
  };
  limits: {
    maxRecordingDuration: number;
    maxMessageLength: number;
    maxConversationHistory: number;
    maxFileSize: number;
  };
  voiceOptions: VoiceOption[];
  supportedLanguages: Array<{
    code: string;
    name: string;
    nativeName: string;
  }>;
}

// Error Types
export interface AppError extends Error {
  code?: string;
  statusCode?: number;
  details?: any;
}

// Event Types
export interface AudioLevelEvent {
  level: number;
  timestamp: number;
}

export interface TranscriptionEvent {
  transcript: string;
  confidence: number;
  isFinal: boolean;
  timestamp: number;
}

export interface RecordingEvent {
  type: 'start' | 'stop' | 'pause' | 'resume' | 'error';
  data?: any;
  timestamp: number;
}

// Utility Types
export type Theme = 'light' | 'dark' | 'system';
export type Language = 'en' | 'es' | 'fr' | 'de' | 'it' | 'pt' | 'ru' | 'ja' | 'ko' | 'zh';
export type MessageRole = 'user' | 'assistant' | 'system';
export type RecordingStatus = 'idle' | 'recording' | 'paused' | 'processing' | 'error';
export type ConnectionStatus = 'connected' | 'connecting' | 'disconnected' | 'error';

// Browser API Extensions
declare global {
  interface Window {
    webkitSpeechRecognition: any;
    SpeechRecognition: any;
  }
  
  interface Navigator {
    mediaDevices: MediaDevices;
  }
}

// React Component Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface IconProps extends BaseComponentProps {
  size?: number | string;
  color?: string;
}

// Animation Types
export interface AnimationConfig {
  duration: number;
  easing: string;
  delay?: number;
}

export interface TransitionConfig {
  enter: AnimationConfig;
  exit: AnimationConfig;
}

// Responsive Types
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';

export interface ResponsiveValue<T> {
  [key in Breakpoint]?: T;
}
