const http = require('http');

console.log('🧪 Testing Voice AI Chat Production APIs with Real Gemini...\n');

// Test function
function testAPI(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Run tests
async function runTests() {
  try {
    console.log('🔥 PRODUCTION MODE TESTS WITH REAL GEMINI API\n');

    // Test 1: Health Check
    console.log('1️⃣ Testing Health Check...');
    const healthResult = await testAPI('/api/health');
    console.log(`   Status: ${healthResult.status}`);
    console.log(`   Response: ${healthResult.data.data.status}`);
    console.log(`   Version: ${healthResult.data.data.version}`);
    console.log(`   Environment: ${healthResult.data.data.environment}`);
    console.log(`   Services: ${JSON.stringify(healthResult.data.data.services)}\n`);

    // Test 2: Configuration
    console.log('2️⃣ Testing Configuration...');
    const configResult = await testAPI('/api/config');
    console.log(`   Status: ${configResult.status}`);
    console.log(`   Features: ${JSON.stringify(configResult.data.data.features)}`);
    console.log(`   Limits: ${JSON.stringify(configResult.data.data.limits)}\n`);

    // Test 3: Real Gemini Chat
    console.log('3️⃣ Testing Real Gemini Chat...');
    const startTime = Date.now();
    const chatResult = await testAPI('/api/chat/message', 'POST', {
      message: 'Hello! I am testing the Voice AI Chat system with real Gemini API. Can you tell me a fun fact about artificial intelligence?'
    });
    const responseTime = Date.now() - startTime;
    
    console.log(`   Status: ${chatResult.status}`);
    console.log(`   Response Time: ${responseTime}ms`);
    console.log(`   Conversation ID: ${chatResult.data.data.conversationId}`);
    console.log(`   Tokens Used: ${chatResult.data.data.metadata.tokensUsed}`);
    console.log(`   Model: ${chatResult.data.data.metadata.model}`);
    console.log(`   AI Response: ${chatResult.data.data.response.substring(0, 150)}...`);
    
    const conversationId = chatResult.data.data.conversationId;

    // Test 4: Follow-up Message (Context Test)
    console.log('\n4️⃣ Testing Context Awareness...');
    const followUpResult = await testAPI('/api/chat/message', 'POST', {
      message: 'That\'s interesting! Can you tell me more about that topic?',
      conversationId: conversationId
    });
    
    console.log(`   Status: ${followUpResult.status}`);
    console.log(`   Same Conversation: ${followUpResult.data.data.conversationId === conversationId}`);
    console.log(`   Tokens Used: ${followUpResult.data.data.metadata.tokensUsed}`);
    console.log(`   AI Response: ${followUpResult.data.data.response.substring(0, 150)}...`);

    // Test 5: Conversation History
    console.log('\n5️⃣ Testing Conversation History...');
    const historyResult = await testAPI(`/api/chat/history/${conversationId}`);
    console.log(`   Status: ${historyResult.status}`);
    console.log(`   Messages Count: ${historyResult.data.data.messages.length}`);
    console.log(`   Conversation Title: ${historyResult.data.data.conversation.title}`);
    
    // Show message history
    historyResult.data.data.messages.forEach((msg, index) => {
      console.log(`   Message ${index + 1}: [${msg.role.toUpperCase()}] ${msg.content.substring(0, 80)}...`);
    });

    // Test 6: All Conversations
    console.log('\n6️⃣ Testing All Conversations...');
    const conversationsResult = await testAPI('/api/chat/conversations');
    console.log(`   Status: ${conversationsResult.status}`);
    console.log(`   Total Conversations: ${conversationsResult.data.data.conversations.length}`);
    
    conversationsResult.data.data.conversations.forEach((conv, index) => {
      console.log(`   Conversation ${index + 1}: ${conv.title} (${conv.messageCount} messages)`);
    });

    // Test 7: Voice Endpoints
    console.log('\n7️⃣ Testing Voice Endpoints...');
    
    // Available Voices
    const voicesResult = await testAPI('/api/voice/voices');
    console.log(`   Available Voices: ${voicesResult.data.data.voices.length} voices`);
    console.log(`   Voice Names: ${voicesResult.data.data.voices.map(v => v.name).join(', ')}`);
    
    // Text-to-Speech
    const ttsResult = await testAPI('/api/voice/synthesize', 'POST', {
      text: 'This is a test of the text-to-speech functionality.',
      voice: 'alloy',
      speed: 1.0
    });
    console.log(`   TTS Status: ${ttsResult.status}`);
    console.log(`   Audio URL: ${ttsResult.data.data.audioUrl}`);
    console.log(`   Duration: ${ttsResult.data.data.duration} seconds`);

    console.log('\n🎉 ALL PRODUCTION TESTS COMPLETED SUCCESSFULLY!');
    console.log('\n📊 TEST SUMMARY:');
    console.log('   ✅ Health Check - Working');
    console.log('   ✅ Configuration - Working');
    console.log('   ✅ Real Gemini Chat - Working');
    console.log('   ✅ Context Awareness - Working');
    console.log('   ✅ Conversation History - Working');
    console.log('   ✅ All Conversations - Working');
    console.log('   ✅ Voice Endpoints - Working');
    console.log('\n🚀 PRODUCTION SYSTEM IS FULLY OPERATIONAL!');
    console.log('🤖 Real Gemini API Integration: SUCCESS');
    console.log('🗄️  Database Persistence: SUCCESS');
    console.log('🎯 All Features: WORKING');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Start tests
runTests();
