"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const errorHandler_1 = require("@/middleware/errorHandler");
const validation_1 = require("@/middleware/validation");
const rateLimiter_1 = require("@/middleware/rateLimiter");
const voiceService_1 = require("@/services/voiceService");
const logger_1 = require("@/utils/logger");
const environment_1 = require("@/config/environment");
const router = (0, express_1.Router)();
const voiceService = new voiceService_1.VoiceService();
const upload = (0, multer_1.default)({
    storage: multer_1.default.memoryStorage(),
    limits: {
        fileSize: environment_1.config.MAX_FILE_SIZE,
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = environment_1.config.ALLOWED_AUDIO_FORMATS.split(',');
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        }
        else {
            cb(new Error(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`));
        }
    },
});
router.post('/upload', rateLimiter_1.uploadRateLimiter, upload.single('audio'), (0, validation_1.validate)(validation_1.fileSchemas.audioUpload), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.file) {
        return res.status(400).json({
            success: false,
            error: {
                code: 'NO_AUDIO_FILE',
                message: 'No audio file provided',
            },
            timestamp: new Date().toISOString(),
        });
    }
    logger_1.logger.info('Processing audio file upload', {
        requestId: req.requestId,
        filename: req.file.originalname,
        size: req.file.size,
        mimeType: req.file.mimetype,
    });
    try {
        (0, validation_1.validateAudioFile)(req.file);
        let metadata = {};
        if (req.body.metadata) {
            try {
                metadata = JSON.parse(req.body.metadata);
            }
            catch (error) {
                logger_1.logger.warn('Invalid metadata JSON provided', {
                    requestId: req.requestId,
                    metadata: req.body.metadata,
                });
            }
        }
        const result = await voiceService.saveAudioFile(req.file.buffer, req.file.originalname, req.file.mimetype, {
            ...metadata,
            uploadedAt: new Date().toISOString(),
            userAgent: req.get('User-Agent'),
            ip: req.ip,
        });
        logger_1.logger.info('Audio file uploaded successfully', {
            requestId: req.requestId,
            filename: result.filename,
            url: result.url,
            size: req.file.size,
        });
        res.json({
            success: true,
            data: {
                filename: result.filename,
                url: result.url,
                size: req.file.size,
                format: req.file.mimetype,
            },
            message: 'Audio file uploaded successfully',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Audio file upload failed', {
            requestId: req.requestId,
            error: error.message,
            filename: req.file.originalname,
            size: req.file.size,
        });
        throw error;
    }
}));
router.get('/:filename', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { filename } = req.params;
    logger_1.logger.debug('Audio file requested', {
        requestId: req.requestId,
        filename,
        userAgent: req.get('User-Agent'),
    });
    try {
        const audioFile = await voiceService.getAudioFile(filename);
        if (!audioFile) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'AUDIO_FILE_NOT_FOUND',
                    message: 'Audio file not found',
                },
                timestamp: new Date().toISOString(),
            });
        }
        res.set({
            'Content-Type': audioFile.mimeType,
            'Content-Length': audioFile.buffer.length.toString(),
            'Cache-Control': 'public, max-age=3600',
            'Accept-Ranges': 'bytes',
        });
        const range = req.headers.range;
        if (range) {
            const parts = range.replace(/bytes=/, '').split('-');
            const start = parseInt(parts[0], 10);
            const end = parts[1] ? parseInt(parts[1], 10) : audioFile.buffer.length - 1;
            const chunksize = (end - start) + 1;
            res.status(206);
            res.set({
                'Content-Range': `bytes ${start}-${end}/${audioFile.buffer.length}`,
                'Content-Length': chunksize.toString(),
            });
            res.end(audioFile.buffer.slice(start, end + 1));
        }
        else {
            res.end(audioFile.buffer);
        }
        logger_1.logger.debug('Audio file served', {
            requestId: req.requestId,
            filename,
            size: audioFile.buffer.length,
            mimeType: audioFile.mimeType,
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to serve audio file', {
            requestId: req.requestId,
            filename,
            error: error.message,
        });
        res.status(500).json({
            success: false,
            error: {
                code: 'AUDIO_SERVE_ERROR',
                message: 'Failed to serve audio file',
            },
            timestamp: new Date().toISOString(),
        });
    }
}));
router.get('/:filename/info', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { filename } = req.params;
    logger_1.logger.debug('Audio file info requested', {
        requestId: req.requestId,
        filename,
    });
    try {
        const audioFile = await voiceService.getAudioFile(filename);
        if (!audioFile) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'AUDIO_FILE_NOT_FOUND',
                    message: 'Audio file not found',
                },
                timestamp: new Date().toISOString(),
            });
        }
        res.json({
            success: true,
            data: {
                filename,
                size: audioFile.buffer.length,
                mimeType: audioFile.mimeType,
                url: `/api/audio/${filename}`,
            },
            message: 'Audio file info retrieved successfully',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to get audio file info', {
            requestId: req.requestId,
            filename,
            error: error.message,
        });
        throw error;
    }
}));
router.post('/cleanup', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    logger_1.logger.info('Starting audio file cleanup', {
        requestId: req.requestId,
    });
    try {
        const deletedCount = await voiceService.cleanupExpiredFiles();
        logger_1.logger.info('Audio file cleanup completed', {
            requestId: req.requestId,
            deletedCount,
        });
        res.json({
            success: true,
            data: {
                deletedCount,
            },
            message: `Cleaned up ${deletedCount} expired audio files`,
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Audio file cleanup failed', {
            requestId: req.requestId,
            error: error.message,
        });
        throw error;
    }
}));
exports.default = router;
//# sourceMappingURL=audio.js.map