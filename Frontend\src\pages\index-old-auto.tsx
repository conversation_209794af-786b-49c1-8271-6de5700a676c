import React, { useState } from 'react';
import Head from 'next/head';
import { MessageSquare, Setting<PERSON>, Mi<PERSON>, MicOff, Square } from 'lucide-react';

export default function HomePage() {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([
    { role: 'assistant', content: 'Hello! I\'m your AI assistant. How can I help you today?' }
  ]);
  const [loading, setLoading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [audioStream, setAudioStream] = useState(null);

  // 語音錄音功能 - 持續錄音模式
  const handleVoiceRecording = async () => {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      alert('您的瀏覽器不支援語音功能。');
      return;
    }

    if (isRecording) {
      // 停止錄音
      stopRecording();
      return;
    }

    // 開始錄音
    try {
      setIsRecording(true);
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setAudioStream(stream);

      const recorder = new MediaRecorder(stream);
      setMediaRecorder(recorder);

      const chunks = [];

      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };

      recorder.onstop = async () => {
        const audioBlob = new Blob(chunks, { type: 'audio/wav' });

        // 發送到後端進行語音轉文字
        await processAudioTranscription(audioBlob);

        // 清理資源
        stream.getTracks().forEach(track => track.stop());
        setAudioStream(null);
        setMediaRecorder(null);
        setIsRecording(false);
      };

      // 開始錄音 - 持續錄音直到用戶手動停止
      recorder.start();

    } catch (error) {
      console.error('麥克風錯誤:', error);
      alert('無法啟動麥克風。請確保您已授權麥克風權限。');
      setIsRecording(false);
    }
  };

  // 停止錄音
  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.stop();
    }
  };

  // 處理語音轉文字
  const processAudioTranscription = async (audioBlob) => {
    try {
      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.wav');

      const response = await fetch('http://localhost:8000/api/voice/transcribe', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        const transcript = data.data.transcript || "語音轉錄完成";
        setMessage(transcript);
      } else {
        // 如果後端失敗，使用模擬轉錄
        setMessage("語音已錄製完成 (後端轉錄失敗，請檢查後端服務)");
      }
    } catch (error) {
      console.error('語音處理錯誤:', error);
      setMessage("語音已錄製完成 (模擬轉錄結果 - 請手動輸入您的問題)");
    }
  };

  const sendMessage = async () => {
    if (!message.trim()) return;

    const userMessage = { role: 'user', content: message };
    setMessages(prev => [...prev, userMessage]);
    setMessage('');
    setLoading(true);

    try {
      const res = await fetch('http://localhost:8000/api/chat/message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message }),
      });

      if (res.ok) {
        const data = await res.json();
        const aiMessage = { role: 'assistant', content: data.data.response };
        setMessages(prev => [...prev, aiMessage]);
      } else {
        const errorMessage = { role: 'assistant', content: 'Sorry, I encountered an error. Please try again.' };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      const errorMessage = { role: 'assistant', content: 'Cannot connect to the server. Please check your connection.' };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <>
      <Head>
        <title>Voice AI Chat</title>
        <meta name="description" content="AI-powered voice chat application" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen bg-gray-50 flex flex-col">
        {/* Header */}
        <header className="bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <MessageSquare className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-xl font-semibold text-gray-900">
                Voice AI Chat
              </h1>
            </div>

            <div className="flex items-center space-x-2">
              <a
                href="/simple"
                className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
              >
                Simple Test
              </a>
              <button className="p-2 text-gray-600 hover:text-gray-900 transition-colors">
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Chat Messages */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="max-w-4xl mx-auto space-y-4">
              {messages.map((msg, index) => (
                <div
                  key={index}
                  className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      msg.role === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-900 border border-gray-200'
                    }`}
                  >
                    <p className="text-sm">{msg.content}</p>
                  </div>
                </div>
              ))}
              
              {loading && (
                <div className="flex justify-start">
                  <div className="bg-white border border-gray-200 rounded-lg px-4 py-2">
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      <span className="text-sm text-gray-600">AI is thinking...</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Voice Controls Area */}
          <div className="bg-white border-t border-gray-200 p-6">
            <div className="max-w-4xl mx-auto">
              {/* Recording Status */}
              {isRecording && (
                <div className="mb-4 text-center">
                  <div className="inline-flex items-center space-x-2 px-4 py-2 bg-red-100 text-red-700 rounded-lg">
                    <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium">正在錄音... (5秒後自動停止)</span>
                  </div>
                </div>
              )}

              {/* Voice Visualizer */}
              <div className="mb-6">
                <div className="flex items-center justify-center space-x-1 h-16">
                  {[...Array(7)].map((_, i) => (
                    <div
                      key={i}
                      className={`w-2 rounded-full transition-all duration-300 ${
                        isRecording 
                          ? 'bg-red-500 animate-pulse' 
                          : 'bg-gray-300'
                      }`}
                      style={{ 
                        height: isRecording 
                          ? `${30 + Math.random() * 30}px` 
                          : `${20 + Math.random() * 20}px` 
                      }}
                    />
                  ))}
                </div>
              </div>

              {/* Push to Talk Button */}
              <div className="flex justify-center mb-6">
                <button 
                  onClick={handleVoiceRecording}
                  disabled={loading}
                  className={`w-16 h-16 rounded-full flex items-center justify-center text-white transition-all duration-300 ${
                    isRecording
                      ? 'bg-red-600 hover:bg-red-700 animate-pulse'
                      : 'bg-blue-600 hover:bg-blue-700'
                  } disabled:bg-gray-400 disabled:cursor-not-allowed`}
                >
                  {isRecording ? (
                    <Square className="w-8 h-8" />
                  ) : (
                    <Mic className="w-8 h-8" />
                  )}
                </button>
              </div>

              {/* Instructions */}
              <div className="text-center mb-4">
                <p className="text-sm text-gray-600">
                  {isRecording 
                    ? '正在錄音中... 點擊停止或等待自動停止' 
                    : '點擊麥克風開始語音輸入 (錄音5秒)'
                  }
                </p>
              </div>

              {/* Text Input */}
              <div className="flex space-x-4">
                <textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message or use voice..."
                  className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                  style={{ color: '#000000' }}
                  rows={2}
                />
                <button
                  onClick={sendMessage}
                  disabled={loading || !message.trim()}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  Send
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
