import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';
export declare function validate(schema: {
    body?: Joi.ObjectSchema;
    params?: Joi.ObjectSchema;
    query?: Joi.ObjectSchema;
    headers?: Joi.ObjectSchema;
}): (req: Request, res: Response, next: NextFunction) => void;
export declare const commonSchemas: {
    uuid: Joi.StringSchema<string>;
    optionalUuid: Joi.StringSchema<string>;
    pagination: Joi.ObjectSchema<any>;
    sort: Joi.ObjectSchema<any>;
};
export declare const chatSchemas: {
    sendMessage: {
        body: Joi.ObjectSchema<any>;
    };
    getHistory: {
        params: Joi.ObjectSchema<any>;
        query: Joi.ObjectSchema<any>;
    };
    deleteConversation: {
        params: Joi.ObjectSchema<any>;
    };
};
export declare const voiceSchemas: {
    transcribe: {
        body: Joi.ObjectSchema<any>;
    };
    synthesize: {
        body: Joi.ObjectSchema<any>;
    };
    process: {
        body: Joi.ObjectSchema<any>;
    };
};
export declare const fileSchemas: {
    audioUpload: {
        body: Joi.ObjectSchema<any>;
    };
};
export declare const feedbackSchemas: {
    submit: {
        body: Joi.ObjectSchema<any>;
    };
};
export declare function validateAudioFile(file: Express.Multer.File): void;
export declare function validateAudioDuration(duration: number): void;
export declare function validateRequestSize(maxSize?: number): (req: Request, res: Response, next: NextFunction) => void;
export declare function validateContentType(allowedTypes: string[]): (req: Request, res: Response, next: NextFunction) => void;
export declare const validators: {
    conversationExists: (conversationId: string) => Promise<boolean>;
    hasPermission: (userId: string, resource: string, action: string) => Promise<boolean>;
    isValidApiKey: (apiKey: string) => boolean;
    isValidUrl: (url: string) => boolean;
    isValidEmail: (email: string) => boolean;
};
export declare const sanitizers: {
    stripHtml: (text: string) => string;
    normalizeWhitespace: (text: string) => string;
    removeSpecialChars: (text: string) => string;
    truncate: (text: string, maxLength: number) => string;
};
//# sourceMappingURL=validation.d.ts.map