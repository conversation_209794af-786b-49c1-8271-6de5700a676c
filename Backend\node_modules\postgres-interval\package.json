{"name": "postgres-interval", "main": "index.js", "version": "1.2.0", "description": "Parse <PERSON> interval columns", "license": "MIT", "repository": "bendrucker/postgres-interval", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "standard && tape test.js"}, "keywords": ["postgres", "interval", "parser"], "dependencies": {"xtend": "^4.0.0"}, "devDependencies": {"tape": "^4.0.0", "standard": "^12.0.1"}, "files": ["index.js", "index.d.ts", "readme.md"]}