"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lucide-react";
exports.ids = ["vendor-chunks/lucide-react"];
exports.modules = {

/***/ "./node_modules/lucide-react/dist/esm/createLucideIcon.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/createLucideIcon.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createLucideIcon),\n/* harmony export */   toKebabCase: () => (/* binding */ toKebabCase)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultAttributes.js */ \"./node_modules/lucide-react/dist/esm/defaultAttributes.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\nconst toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase().trim();\nconst createLucideIcon = (iconName, iconNode)=>{\n    const Component = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(({ color = \"currentColor\", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = \"\", children, ...rest }, ref)=>/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", {\n            ref,\n            ..._defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            width: size,\n            height: size,\n            stroke: color,\n            strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n            className: [\n                \"lucide\",\n                `lucide-${toKebabCase(iconName)}`,\n                className\n            ].join(\" \"),\n            ...rest\n        }, [\n            ...iconNode.map(([tag, attrs])=>/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(tag, attrs)),\n            ...Array.isArray(children) ? children : [\n                children\n            ]\n        ]));\n    Component.displayName = `${iconName}`;\n    return Component;\n};\n //# sourceMappingURL=createLucideIcon.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/createLucideIcon.js\n");

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/defaultAttributes.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/defaultAttributes.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultAttributes)\n/* harmony export */ });\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ var defaultAttributes = {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: 2,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n};\n //# sourceMappingURL=defaultAttributes.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2RlZmF1bHRBdHRyaWJ1dGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztJQUFBLElBQWVBLG9CQUFBO0lBQ2JDLE9BQU87SUFDUEMsT0FBTztJQUNQQyxRQUFRO0lBQ1JDLFNBQVM7SUFDVEMsTUFBTTtJQUNOQyxRQUFRO0lBQ1JDLGFBQWE7SUFDYkMsZUFBZTtJQUNmQyxnQkFBZ0I7QUFDbEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92b2ljZS1jaGF0LWZyb250ZW5kLy4uLy4uL3NyYy9kZWZhdWx0QXR0cmlidXRlcy50cz8zNzBjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcbiAgeG1sbnM6ICdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycsXG4gIHdpZHRoOiAyNCxcbiAgaGVpZ2h0OiAyNCxcbiAgdmlld0JveDogJzAgMCAyNCAyNCcsXG4gIGZpbGw6ICdub25lJyxcbiAgc3Ryb2tlOiAnY3VycmVudENvbG9yJyxcbiAgc3Ryb2tlV2lkdGg6IDIsXG4gIHN0cm9rZUxpbmVjYXA6ICdyb3VuZCcsXG4gIHN0cm9rZUxpbmVqb2luOiAncm91bmQnLFxufTtcbiJdLCJuYW1lcyI6WyJkZWZhdWx0QXR0cmlidXRlcyIsInhtbG5zIiwid2lkdGgiLCJoZWlnaHQiLCJ2aWV3Qm94IiwiZmlsbCIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/defaultAttributes.js\n");

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/message-square.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/message-square.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MessageSquare)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst MessageSquare = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"MessageSquare\", [\n    [\n        \"path\",\n        {\n            d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\",\n            key: \"1lielz\"\n        }\n    ]\n]);\n //# sourceMappingURL=message-square.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL21lc3NhZ2Utc3F1YXJlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0sTUFBQUEsZ0JBQWdCQyxnRUFBZ0JBLENBQUMsaUJBQWlCO0lBQ3REO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQWlFQyxLQUFLO1FBQUE7S0FBVTtDQUMvRiIsInNvdXJjZXMiOlsid2VicGFjazovL3ZvaWNlLWNoYXQtZnJvbnRlbmQvLi4vLi4vLi4vc3JjL2ljb25zL21lc3NhZ2Utc3F1YXJlLnRzPzBiZGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBNZXNzYWdlU3F1YXJlXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NakVnTVRWaE1pQXlJREFnTUNBeExUSWdNa2czYkMwMElEUldOV0V5SURJZ01DQXdJREVnTWkweWFERTBZVElnTWlBd0lEQWdNU0F5SURKNklpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL21lc3NhZ2Utc3F1YXJlXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgTWVzc2FnZVNxdWFyZSA9IGNyZWF0ZUx1Y2lkZUljb24oJ01lc3NhZ2VTcXVhcmUnLCBbXG4gIFsncGF0aCcsIHsgZDogJ00yMSAxNWEyIDIgMCAwIDEtMiAySDdsLTQgNFY1YTIgMiAwIDAgMSAyLTJoMTRhMiAyIDAgMCAxIDIgMnonLCBrZXk6ICcxbGllbHonIH1dLFxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IE1lc3NhZ2VTcXVhcmU7XG4iXSwibmFtZXMiOlsiTWVzc2FnZVNxdWFyZSIsImNyZWF0ZUx1Y2lkZUljb24iLCJkIiwia2V5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/message-square.js\n");

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/mic.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/mic.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Mic)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Mic = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Mic\", [\n    [\n        \"path\",\n        {\n            d: \"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z\",\n            key: \"131961\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19 10v2a7 7 0 0 1-14 0v-2\",\n            key: \"1vc78b\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"19\",\n            y2: \"22\",\n            key: \"x3vr5v\"\n        }\n    ]\n]);\n //# sourceMappingURL=mic.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/mic.js\n");

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/settings.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/settings.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Settings)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Settings = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Settings\", [\n    [\n        \"path\",\n        {\n            d: \"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z\",\n            key: \"1qme2f\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"3\",\n            key: \"1v7zrd\"\n        }\n    ]\n]);\n //# sourceMappingURL=settings.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/settings.js\n");

/***/ })

};
;