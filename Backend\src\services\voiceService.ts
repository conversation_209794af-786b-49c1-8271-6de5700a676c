import { transcribeAudio, generateSpeech } from '@/config/openai';
import { prisma } from '@/config/database';
import { logger, logAPI } from '@/utils/logger';
import { createError } from '@/middleware/errorHandler';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs/promises';
import path from 'path';

export interface TranscribeRequest {
  audioBuffer: Buffer;
  language?: string;
  model?: string;
  temperature?: number;
}

export interface TranscribeResponse {
  transcript: string;
  confidence: number;
  language: string;
  duration: number;
  alternatives?: Array<{
    transcript: string;
    confidence: number;
  }>;
}

export interface SynthesizeRequest {
  text: string;
  voice?: string;
  speed?: number;
  model?: string;
}

export interface SynthesizeResponse {
  audioUrl: string;
  duration: number;
  format: string;
  size: number;
}

export class VoiceService {
  private uploadDir: string;

  constructor() {
    this.uploadDir = process.env.UPLOAD_DIR || './uploads';
  }

  async transcribeAudio(request: TranscribeRequest): Promise<TranscribeResponse> {
    const startTime = Date.now();

    try {
      logger.info('Starting audio transcription', {
        audioSize: request.audioBuffer.length,
        language: request.language,
        model: request.model,
      });

      // Call OpenAI Whisper API
      const result = await transcribeAudio(request.audioBuffer, {
        language: request.language,
        model: request.model,
        temperature: request.temperature,
      });

      const responseTime = Date.now() - startTime;
      
      logAPI('openai', 'whisper-transcription', responseTime);

      logger.info('Audio transcription completed', {
        transcriptLength: result.text?.length || 0,
        responseTime,
      });

      // For now, we'll estimate confidence and duration
      // In a real implementation, you might use additional audio processing libraries
      const estimatedDuration = request.audioBuffer.length / 16000; // Rough estimate
      const estimatedConfidence = 0.9; // OpenAI Whisper is generally high confidence

      return {
        transcript: result.text || '',
        confidence: estimatedConfidence,
        language: request.language || 'en',
        duration: estimatedDuration,
      };

    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      
      logger.error('Audio transcription failed', {
        error: error.message,
        responseTime,
        audioSize: request.audioBuffer.length,
      });

      if (error.error?.type === 'rate_limit_error') {
        throw createError.rateLimit('OpenAI API rate limit exceeded');
      }

      throw createError.aiService(
        error.error?.message || 'Failed to transcribe audio',
        error.statusCode || 500
      );
    }
  }

  async synthesizeSpeech(request: SynthesizeRequest): Promise<SynthesizeResponse> {
    const startTime = Date.now();

    try {
      logger.info('Starting speech synthesis', {
        textLength: request.text.length,
        voice: request.voice,
        speed: request.speed,
      });

      // Generate speech using OpenAI TTS
      const audioBuffer = await generateSpeech(request.text, {
        voice: request.voice as any,
        speed: request.speed,
        model: request.model,
      });

      // Save audio file
      const filename = `tts_${uuidv4()}.mp3`;
      const filepath = path.join(this.uploadDir, filename);
      
      await fs.writeFile(filepath, audioBuffer);

      // Save to database
      await prisma.audioFile.create({
        data: {
          filename,
          originalName: `speech_${Date.now()}.mp3`,
          mimeType: 'audio/mpeg',
          size: audioBuffer.length,
          path: filepath,
          url: `/api/audio/${filename}`,
          status: 'PROCESSED',
          metadata: JSON.stringify({
            voice: request.voice,
            speed: request.speed,
            textLength: request.text.length,
            generatedAt: new Date().toISOString(),
          }),
        },
      });

      const responseTime = Date.now() - startTime;
      
      logAPI('openai', 'tts-synthesis', responseTime);

      logger.info('Speech synthesis completed', {
        filename,
        audioSize: audioBuffer.length,
        responseTime,
      });

      // Estimate duration (rough calculation for MP3)
      const estimatedDuration = request.text.length * 0.1; // ~100ms per character

      return {
        audioUrl: `/api/audio/${filename}`,
        duration: estimatedDuration,
        format: 'audio/mpeg',
        size: audioBuffer.length,
      };

    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      
      logger.error('Speech synthesis failed', {
        error: error.message,
        responseTime,
        textLength: request.text.length,
      });

      if (error.error?.type === 'rate_limit_error') {
        throw createError.rateLimit('OpenAI API rate limit exceeded');
      }

      throw createError.aiService(
        error.error?.message || 'Failed to synthesize speech',
        error.statusCode || 500
      );
    }
  }

  async saveAudioFile(
    audioBuffer: Buffer,
    originalName: string,
    mimeType: string,
    metadata?: Record<string, any>
  ): Promise<{ filename: string; url: string }> {
    try {
      const filename = `upload_${uuidv4()}_${originalName}`;
      const filepath = path.join(this.uploadDir, filename);
      
      await fs.writeFile(filepath, audioBuffer);

      // Save to database
      await prisma.audioFile.create({
        data: {
          filename,
          originalName,
          mimeType,
          size: audioBuffer.length,
          path: filepath,
          url: `/api/audio/${filename}`,
          status: 'UPLOADED',
          metadata: JSON.stringify(metadata || {}),
        },
      });

      logger.info('Audio file saved', {
        filename,
        originalName,
        size: audioBuffer.length,
        mimeType,
      });

      return {
        filename,
        url: `/api/audio/${filename}`,
      };

    } catch (error: any) {
      logger.error('Failed to save audio file', {
        error: error.message,
        originalName,
        size: audioBuffer.length,
      });
      throw createError.database('Failed to save audio file');
    }
  }

  async getAudioFile(filename: string): Promise<{ buffer: Buffer; mimeType: string } | null> {
    try {
      const audioFile = await prisma.audioFile.findUnique({
        where: { filename },
      });

      if (!audioFile) {
        return null;
      }

      const buffer = await fs.readFile(audioFile.path);

      return {
        buffer,
        mimeType: audioFile.mimeType,
      };

    } catch (error: any) {
      logger.error('Failed to get audio file', {
        error: error.message,
        filename,
      });
      return null;
    }
  }

  async cleanupExpiredFiles(): Promise<number> {
    try {
      const expiredFiles = await prisma.audioFile.findMany({
        where: {
          OR: [
            { expiresAt: { lt: new Date() } },
            { 
              createdAt: { 
                lt: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 hours old
              },
              status: 'UPLOADED'
            }
          ],
        },
      });

      let deletedCount = 0;

      for (const file of expiredFiles) {
        try {
          await fs.unlink(file.path);
          await prisma.audioFile.delete({ where: { id: file.id } });
          deletedCount++;
        } catch (error) {
          logger.warn('Failed to delete expired file', {
            filename: file.filename,
            error: error.message,
          });
        }
      }

      if (deletedCount > 0) {
        logger.info('Cleaned up expired audio files', { deletedCount });
      }

      return deletedCount;

    } catch (error: any) {
      logger.error('Failed to cleanup expired files', {
        error: error.message,
      });
      return 0;
    }
  }
}

export default VoiceService;
