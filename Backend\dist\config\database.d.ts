import { PrismaClient } from '@prisma/client';
declare let prisma: PrismaClient;
declare global {
    var __prisma: PrismaClient | undefined;
}
export declare function checkDatabaseConnection(): Promise<boolean>;
export declare function disconnectDatabase(): Promise<void>;
export declare function withTransaction<T>(callback: (tx: PrismaClient) => Promise<T>): Promise<T>;
export declare function getDatabaseMetrics(): Promise<any>;
export { prisma };
export default prisma;
//# sourceMappingURL=database.d.ts.map