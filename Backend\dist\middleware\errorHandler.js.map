{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AAmGA,oCAkHC;AAGD,oCAMC;AAGD,0CAGC;AAGD,4DAUC;AAhPD,2CAAkD;AAClD,4CAAoD;AAGpD,MAAa,QAAS,SAAQ,KAAK;IAKjC,YAAY,OAAe,EAAE,aAAqB,GAAG,EAAE,OAAe,gBAAgB;QACpF,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAbD,4BAaC;AAGD,MAAa,eAAgB,SAAQ,QAAQ;IAG3C,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;CACF;AAPD,0CAOC;AAGD,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,WAAmB,UAAU;QACvC,KAAK,CAAC,GAAG,QAAQ,YAAY,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;IACnD,CAAC;CACF;AAJD,sCAIC;AAGD,MAAa,iBAAkB,SAAQ,QAAQ;IAC7C,YAAY,UAAkB,qBAAqB;QACjD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IACtC,CAAC;CACF;AAJD,8CAIC;AAGD,MAAa,cAAe,SAAQ,QAAQ;IAC1C,YAAY,UAAkB,kBAAkB;QAC9C,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;IACnC,CAAC;CACF;AAJD,wCAIC;AAGD,MAAa,cAAe,SAAQ,QAAQ;IAC1C,YAAY,UAAkB,qBAAqB;QACjD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,qBAAqB,CAAC,CAAC;IAC7C,CAAC;CACF;AAJD,wCAIC;AAGD,MAAa,eAAgB,SAAQ,QAAQ;IAC3C,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,mBAAmB,CAAC,CAAC;IAC3C,CAAC;CACF;AAJD,0CAIC;AAGD,MAAa,oBAAqB,SAAQ,QAAQ;IAChD,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,wBAAwB,CAAC,CAAC;IAChD,CAAC;CACF;AAJD,oDAIC;AAGD,MAAa,cAAe,SAAQ,QAAQ;IAC1C,YAAY,OAAe,EAAE,aAAqB,GAAG;QACnD,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,kBAAkB,CAAC,CAAC;IACjD,CAAC;CACF;AAJD,wCAIC;AAGD,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,gBAAgB,CAAC,CAAC;IACxC,CAAC;CACF;AAJD,sCAIC;AAeD,SAAgB,YAAY,CAC1B,KAAY,EACZ,GAAY,EACZ,GAAa,EACb,IAAkB;IAGlB,IAAA,iBAAQ,EAAC,KAAK,EAAE;QACd,GAAG,EAAE,GAAG,CAAC,WAAW;QACpB,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;KACjC,CAAC,CAAC;IAGH,IAAI,UAAU,GAAG,GAAG,CAAC;IACrB,IAAI,IAAI,GAAG,gBAAgB,CAAC;IAC5B,IAAI,OAAO,GAAG,8BAA8B,CAAC;IAC7C,IAAI,OAAO,GAAQ,SAAS,CAAC;IAG7B,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QAC9B,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QAC9B,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;QAClB,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAExB,IAAI,KAAK,YAAY,eAAe,EAAE,CAAC;YACrC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC1B,CAAC;IACH,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QAE5C,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,kBAAkB,CAAC;QAC1B,OAAO,GAAG,2BAA2B,CAAC;QACtC,OAAO,GAAI,KAAa,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC;YACtD,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC;YAC7B,OAAO,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC,CAAC;IACN,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;QAExC,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,mBAAmB,CAAC;QAE3B,QAAS,KAAa,CAAC,IAAI,EAAE,CAAC;YAC5B,KAAK,iBAAiB;gBACpB,OAAO,GAAG,6CAA6C,CAAC;gBACxD,MAAM;YACR,KAAK,kBAAkB;gBACrB,OAAO,GAAG,yBAAyB,CAAC;gBACpC,MAAM;YACR,KAAK,uBAAuB;gBAC1B,OAAO,GAAG,uBAAuB,CAAC;gBAClC,MAAM;YACR;gBACE,OAAO,GAAG,mBAAmB,CAAC;QAClC,CAAC;IACH,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,+BAA+B,EAAE,CAAC;QAE1D,MAAM,WAAW,GAAG,KAAY,CAAC;QACjC,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,gBAAgB,CAAC;QAExB,QAAQ,WAAW,CAAC,IAAI,EAAE,CAAC;YACzB,KAAK,OAAO;gBACV,OAAO,GAAG,+CAA+C,CAAC;gBAC1D,MAAM;YACR,KAAK,OAAO;gBACV,OAAO,GAAG,kBAAkB,CAAC;gBAC7B,UAAU,GAAG,GAAG,CAAC;gBACjB,IAAI,GAAG,WAAW,CAAC;gBACnB,MAAM;YACR,KAAK,OAAO;gBACV,OAAO,GAAG,kCAAkC,CAAC;gBAC7C,MAAM;YACR;gBACE,OAAO,GAAG,2BAA2B,CAAC;QAC1C,CAAC;IACH,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,6BAA6B,EAAE,CAAC;QAExD,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,kBAAkB,CAAC;QAC1B,OAAO,GAAG,uBAAuB,CAAC;IACpC,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;QAE7C,MAAM,WAAW,GAAG,IAAA,0BAAiB,EAAC,KAAK,CAAC,CAAC;QAC7C,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;QACpC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;QACxB,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;IAChC,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QAE3E,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,cAAc,CAAC;QACtB,OAAO,GAAG,8BAA8B,CAAC;IAC3C,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QAEtC,UAAU,GAAG,GAAG,CAAC;QACjB,IAAI,GAAG,YAAY,CAAC;QACpB,OAAO,GAAG,mBAAmB,CAAC;IAChC,CAAC;IAGD,MAAM,aAAa,GAAkB;QACnC,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,IAAI;YACJ,OAAO;YACP,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,CAAC;YAC3B,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;SACtE;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC;IAGF,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7C,CAAC;AAGD,SAAgB,YAAY,CAC1B,EAAqE;IAErE,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC;AAGD,SAAgB,eAAe,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;IAC7E,MAAM,KAAK,GAAG,IAAI,aAAa,CAAC,SAAS,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,WAAW,YAAY,CAAC,CAAC;IACpF,IAAI,CAAC,KAAK,CAAC,CAAC;AACd,CAAC;AAGD,SAAgB,wBAAwB;IACtC,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAY,EAAE,EAAE;QAC/C,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QAC3C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAW,EAAE,OAAqB,EAAE,EAAE;QACtE,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QACpE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACL,CAAC;AAGY,QAAA,WAAW,GAAG;IACzB,UAAU,EAAE,CAAC,OAAe,EAAE,OAAa,EAAE,EAAE,CAAC,IAAI,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC;IACrF,QAAQ,EAAE,CAAC,QAAiB,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC;IAC5D,YAAY,EAAE,CAAC,OAAgB,EAAE,EAAE,CAAC,IAAI,iBAAiB,CAAC,OAAO,CAAC;IAClE,SAAS,EAAE,CAAC,OAAgB,EAAE,EAAE,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC;IAC5D,SAAS,EAAE,CAAC,OAAgB,EAAE,EAAE,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC;IAC5D,UAAU,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,IAAI,eAAe,CAAC,OAAO,CAAC;IAC7D,eAAe,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,IAAI,oBAAoB,CAAC,OAAO,CAAC;IACvE,SAAS,EAAE,CAAC,OAAe,EAAE,UAAmB,EAAE,EAAE,CAAC,IAAI,cAAc,CAAC,OAAO,EAAE,UAAU,CAAC;IAC5F,QAAQ,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,OAAO,CAAC;IACzD,OAAO,EAAE,CAAC,OAAe,EAAE,UAAmB,EAAE,IAAa,EAAE,EAAE,CAC/D,IAAI,QAAQ,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC;CAC1C,CAAC"}