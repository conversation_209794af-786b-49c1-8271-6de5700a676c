{"version": 3, "file": "audio.js", "sourceRoot": "", "sources": ["../../src/routes/audio.ts"], "names": [], "mappings": ";;;;;AAAA,qCAAiC;AACjC,oDAA4B;AAC5B,4DAAyD;AACzD,wDAAmF;AACnF,0DAA6D;AAC7D,0DAAuD;AACvD,2CAAwC;AACxC,sDAA8C;AAE9C,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AACxB,MAAM,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;AAGxC,MAAM,MAAM,GAAG,IAAA,gBAAM,EAAC;IACpB,OAAO,EAAE,gBAAM,CAAC,aAAa,EAAE;IAC/B,MAAM,EAAE;QACN,QAAQ,EAAE,oBAAM,CAAC,aAAa;KAC/B;IACD,UAAU,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;QAC5B,MAAM,YAAY,GAAG,oBAAM,CAAC,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7D,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACjB,CAAC;aAAM,CAAC;YACN,EAAE,CAAC,IAAI,KAAK,CAAC,qCAAqC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;CACF,CAAC,CAAC;AAGH,MAAM,CAAC,IAAI,CAAC,SAAS,EACnB,+BAAiB,EACjB,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EACtB,IAAA,qBAAQ,EAAC,wBAAW,CAAC,WAAW,CAAC,EACjC,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,wBAAwB;aAClC;YACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;QAC1C,SAAS,EAAE,GAAG,CAAC,SAAS;QACxB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY;QAC/B,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI;QACnB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ;KAC5B,CAAC,CAAC;IAEH,IAAI,CAAC;QAEH,IAAA,8BAAiB,EAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAG5B,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtB,IAAI,CAAC;gBACH,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;oBAC5C,SAAS,EAAE,GAAG,CAAC,SAAS;oBACxB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ;iBAC5B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAGD,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,aAAa,CAC7C,GAAG,CAAC,IAAI,CAAC,MAAM,EACf,GAAG,CAAC,IAAI,CAAC,YAAY,EACrB,GAAG,CAAC,IAAI,CAAC,QAAQ,EACjB;YACE,GAAG,QAAQ;YACX,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACpC,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,EAAE,EAAE,GAAG,CAAC,EAAE;SACX,CACF,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE;YAC9C,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,GAAG,EAAE,MAAM,CAAC,GAAG;YACf,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI;SACpB,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI;gBACnB,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ;aAC1B;YACD,OAAO,EAAE,kCAAkC;YAC3C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;YACvC,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,KAAK,EAAE,KAAK,CAAC,OAAO;YACpB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY;YAC/B,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI;SACpB,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC,CACH,CAAC;AAGF,MAAM,CAAC,GAAG,CAAC,YAAY,EACrB,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC9B,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAEhC,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;QACnC,SAAS,EAAE,GAAG,CAAC,SAAS;QACxB,QAAQ;QACR,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;KACjC,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,MAAM,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE5D,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,sBAAsB;oBAC5B,OAAO,EAAE,sBAAsB;iBAChC;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;QAGD,GAAG,CAAC,GAAG,CAAC;YACN,cAAc,EAAE,SAAS,CAAC,QAAQ;YAClC,gBAAgB,EAAE,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE;YACpD,eAAe,EAAE,sBAAsB;YACvC,eAAe,EAAE,OAAO;SACzB,CAAC,CAAC;QAGH,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC;QAChC,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACrD,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACrC,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YAC5E,MAAM,SAAS,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;YAEpC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAChB,GAAG,CAAC,GAAG,CAAC;gBACN,eAAe,EAAE,SAAS,KAAK,IAAI,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE;gBACnE,gBAAgB,EAAE,SAAS,CAAC,QAAQ,EAAE;aACvC,CAAC,CAAC;YAEH,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE;YAChC,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,QAAQ;YACR,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,MAAM;YAC7B,QAAQ,EAAE,SAAS,CAAC,QAAQ;SAC7B,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;YACzC,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,QAAQ;YACR,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,mBAAmB;gBACzB,OAAO,EAAE,4BAA4B;aACtC;YACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CACH,CAAC;AAGF,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAC1B,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC9B,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAEhC,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;QACxC,SAAS,EAAE,GAAG,CAAC,SAAS;QACxB,QAAQ;KACT,CAAC,CAAC;IAEH,IAAI,CAAC;QAGH,MAAM,SAAS,GAAG,MAAM,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE5D,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,sBAAsB;oBAC5B,OAAO,EAAE,sBAAsB;iBAChC;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;QAED,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,QAAQ;gBACR,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,MAAM;gBAC7B,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,GAAG,EAAE,cAAc,QAAQ,EAAE;aAC9B;YACD,OAAO,EAAE,wCAAwC;YACjD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;YAC5C,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,QAAQ;YACR,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC,CACH,CAAC;AAGF,MAAM,CAAC,IAAI,CAAC,UAAU,EACpB,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC9B,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;QACzC,SAAS,EAAE,GAAG,CAAC,SAAS;KACzB,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,MAAM,YAAY,CAAC,mBAAmB,EAAE,CAAC;QAE9D,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;YAC1C,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,YAAY;SACb,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,YAAY;aACb;YACD,OAAO,EAAE,cAAc,YAAY,sBAAsB;YACzD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;YACxC,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,KAAK,EAAE,KAAK,CAAC,OAAO;SACrB,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC,CACH,CAAC;AAEF,kBAAe,MAAM,CAAC"}