# Production Environment Configuration
NODE_ENV=production

# Database Configuration (SQLite for demo, PostgreSQL for production)
DATABASE_URL="file:./prisma/dev.db"
# For PostgreSQL: DATABASE_URL="postgresql://username:password@localhost:5432/voice_ai_chat"

# Google Gemini API Configuration
GOOGLE_API_KEY="AIzaSyDt1F6Vu77zJ-ZbpmeNrxShGPnTpSU4Zlg"
GEMINI_MODEL="gemini-2.5-flash"

# Server Configuration
PORT=8000
HOST=localhost
CORS_ORIGIN="http://localhost:3000"

# Security Configuration
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
BCRYPT_ROUNDS=12
SESSION_SECRET="your-session-secret-change-in-production"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR="./uploads"
ALLOWED_AUDIO_FORMATS="audio/wav,audio/mp3,audio/ogg,audio/webm"

# Audio Processing
MAX_AUDIO_DURATION=300
AUDIO_SAMPLE_RATE=16000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE="./logs/app.log"
LOG_MAX_SIZE=10485760
LOG_MAX_FILES=5

# Cache Configuration
REDIS_URL="redis://localhost:6379"
CACHE_TTL=3600

# External Services
OPENAI_API_KEY=""
SPEECH_SERVICE_URL=""
SPEECH_SERVICE_KEY=""

# Monitoring
SENTRY_DSN=""
ANALYTICS_ID=""

# Feature Flags
ENABLE_VOICE_UPLOAD=true
ENABLE_REAL_TIME_TRANSCRIPTION=true
ENABLE_CONVERSATION_EXPORT=true
ENABLE_USER_ANALYTICS=true
ENABLE_FEEDBACK_COLLECTION=true

# Development/Debug
DEBUG_MODE=false
MOCK_EXTERNAL_SERVICES=false
ENABLE_API_DOCS=true
ENABLE_METRICS=true
