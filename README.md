# Voice-Enabled AI Chat Application

A real-time voice-to-voice AI conversation application that allows users to interact with AI through speech, featuring push-to-talk functionality, live transcription, and natural speech synthesis.

## 🎯 Features

- **Push-to-Talk Interface**: Press and hold to record voice input
- **Real-time Speech Recognition**: Live transcription with confidence scoring
- **AI-Powered Responses**: Integration with OpenAI GPT for intelligent conversations
- **Text-to-Speech Synthesis**: Natural voice playback of AI responses
- **Conversation History**: Visual chat interface with message history
- **Cross-Platform Support**: Works on desktop and mobile browsers
- **Accessibility Features**: Keyboard navigation and screen reader support

## 🏗️ Architecture

### Frontend
- **Framework**: Next.js 14 with React 18
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Speech APIs**: Web Speech API (SpeechRecognition & SpeechSynthesis)

### Backend
- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **AI Integration**: OpenAI GPT-4 API
- **Audio Processing**: Multer for file uploads

## 📁 Project Structure

```
AI_CustomerService/
├── PRD/                          # Product Requirements Documents
│   ├── Product_Requirements_Document.md
│   ├── Technical_Architecture.md
│   ├── API_Documentation.md
│   ├── UI_Mockups_and_User_Flow.md
│   ├── Testing_Requirements.md
│   └── Deployment_Guidelines.md
├── Frontend/                     # React/Next.js application
│   ├── src/
│   │   ├── components/          # React components
│   │   ├── hooks/              # Custom React hooks
│   │   ├── services/           # API services
│   │   ├── store/              # State management
│   │   ├── types/              # TypeScript types
│   │   └── utils/              # Utility functions
│   ├── public/                 # Static assets
│   └── package.json
├── Backend/                      # Node.js/Express API
│   ├── src/
│   │   ├── controllers/        # Request handlers
│   │   ├── services/           # Business logic
│   │   ├── models/             # Data models
│   │   ├── middleware/         # Express middleware
│   │   ├── routes/             # API routes
│   │   ├── config/             # Configuration
│   │   └── utils/              # Utility functions
│   ├── prisma/                 # Database schema
│   ├── uploads/                # Temporary file storage
│   └── package.json
└── README.md
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ installed
- PostgreSQL database (or SQLite for development)
- OpenAI API key

### 1. Clone the Repository
```bash
git clone <repository-url>
cd AI_CustomerService
```

### 2. Backend Setup
```bash
cd Backend
npm install

# Copy environment file
cp .env.example .env

# Edit .env with your configuration
# DATABASE_URL=postgresql://user:password@localhost:5432/voicechat
# OPENAI_API_KEY=your_openai_api_key

# Run database migrations
npx prisma migrate dev

# Start the backend server
npm run dev
```

### 3. Frontend Setup
```bash
cd ../Frontend
npm install

# Copy environment file
cp .env.local.example .env.local

# Edit .env.local with your configuration
# NEXT_PUBLIC_API_URL=http://localhost:8000

# Start the frontend development server
npm run dev
```

### 4. Access the Application
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/api-docs

## 🎮 Usage

1. **Grant Microphone Permission**: Allow microphone access when prompted
2. **Start Conversation**: Press and hold the microphone button to record
3. **Speak Your Message**: Talk while holding the button
4. **Release to Send**: Release the button to process your message
5. **Listen to Response**: The AI response will be played automatically
6. **View History**: Scroll up to see previous conversation messages

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```bash
NODE_ENV=development
PORT=8000
DATABASE_URL=postgresql://user:password@localhost:5432/voicechat
OPENAI_API_KEY=your_openai_api_key
CORS_ORIGIN=http://localhost:3000
LOG_LEVEL=debug
RATE_LIMIT_ENABLED=true
MAX_RECORDING_DURATION=60
MAX_FILE_SIZE=10485760
```

#### Frontend (.env.local)
```bash
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_ENVIRONMENT=development
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_MAX_RECORDING_DURATION=60
```

## 🧪 Testing

### Backend Testing
```bash
cd Backend
npm run test              # Run unit tests
npm run test:integration  # Run integration tests
npm run test:coverage     # Run tests with coverage
```

### Frontend Testing
```bash
cd Frontend
npm run test              # Run unit tests
npm run test:e2e          # Run end-to-end tests
npm run test:a11y         # Run accessibility tests
```

## 📦 Deployment

### Production Deployment

#### Frontend (Vercel)
```bash
cd Frontend
npm install -g vercel
vercel login
vercel --prod
```

#### Backend (Railway)
```bash
cd Backend
npm install -g @railway/cli
railway login
railway init
railway up
```

### Environment Setup
1. Set up production database (PostgreSQL)
2. Configure environment variables
3. Set up domain and SSL certificates
4. Configure monitoring and logging

## 🔒 Security

- HTTPS enforcement in production
- Input validation and sanitization
- Rate limiting on API endpoints
- CORS configuration
- Secure API key management
- Content Security Policy headers

## 🎯 Browser Support

| Browser | Version | Desktop | Mobile |
|---------|---------|---------|---------|
| Chrome | 80+ | ✅ | ✅ |
| Firefox | 75+ | ✅ | ✅ |
| Safari | 13+ | ✅ | ✅ |
| Edge | 80+ | ✅ | ✅ |

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Write tests for new features
- Update documentation as needed
- Follow the existing code style
- Ensure accessibility compliance

## 📝 API Documentation

The API documentation is available at `/api-docs` when running the backend server. Key endpoints include:

- `POST /api/chat/message` - Send message to AI
- `POST /api/voice/transcribe` - Convert speech to text
- `POST /api/voice/synthesize` - Convert text to speech
- `POST /api/voice/process` - Complete voice processing pipeline
- `GET /api/chat/history/:id` - Get conversation history

## 🐛 Troubleshooting

### Common Issues

#### Microphone Not Working
- Check browser permissions
- Ensure HTTPS in production
- Verify microphone hardware

#### API Connection Issues
- Check backend server is running
- Verify API URL configuration
- Check CORS settings

#### Audio Playback Issues
- Check browser audio permissions
- Verify audio codec support
- Test with different browsers

### Debug Mode
Enable debug logging by setting `LOG_LEVEL=debug` in backend environment.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI for GPT API
- Web Speech API for browser speech capabilities
- React and Next.js communities
- All contributors and testers

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation in the `PRD/` folder
- Review the troubleshooting section above

---

**Version**: 1.0.0  
**Last Updated**: 2025-07-02  
**Maintainer**: Development Team
