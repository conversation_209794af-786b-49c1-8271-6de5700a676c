import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { clsx } from 'clsx';
import { MessageSquare, ArrowDown, Loader2 } from 'lucide-react';
import { ChatContainerProps } from '@/types';
import MessageBubble, { TypingIndicator } from './MessageBubble';

export function ChatContainer({
  messages,
  isLoading = false,
  onLoadMore,
  hasMore = false,
  className,
}: ChatContainerProps) {
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [isNearBottom, setIsNearBottom] = useState(true);
  const [autoScroll, setAutoScroll] = useState(true);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const lastMessageCountRef = useRef(messages.length);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (autoScroll && isNearBottom && messages.length > lastMessageCountRef.current) {
      scrollToBottom();
    }
    lastMessageCountRef.current = messages.length;
  }, [messages, autoScroll, isNearBottom]);

  // Handle scroll events
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
      
      // Show scroll button when not near bottom
      setShowScrollButton(distanceFromBottom > 100);
      
      // Update near bottom state
      const nearBottom = distanceFromBottom < 50;
      setIsNearBottom(nearBottom);
      
      // Disable auto-scroll if user scrolls up manually
      if (!nearBottom && autoScroll) {
        setAutoScroll(false);
      } else if (nearBottom && !autoScroll) {
        setAutoScroll(true);
      }

      // Load more messages when scrolled to top
      if (scrollTop === 0 && hasMore && onLoadMore) {
        onLoadMore();
      }
    };

    container.addEventListener('scroll', handleScroll, { passive: true });
    return () => container.removeEventListener('scroll', handleScroll);
  }, [hasMore, onLoadMore, autoScroll]);

  // Scroll to bottom function
  const scrollToBottom = (smooth = true) => {
    messagesEndRef.current?.scrollIntoView({
      behavior: smooth ? 'smooth' : 'auto',
      block: 'end',
    });
  };

  // Handle scroll button click
  const handleScrollToBottom = () => {
    setAutoScroll(true);
    scrollToBottom();
  };

  // Empty state
  if (messages.length === 0 && !isLoading) {
    return (
      <div className={clsx('flex-1 flex items-center justify-center p-8', className)}>
        <motion.div
          className="text-center max-w-md"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
            <MessageSquare className="w-8 h-8 text-primary-600 dark:text-primary-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Start a conversation
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Press and hold the microphone button to record your message, or type to get started.
          </p>
          <div className="space-y-2 text-sm text-gray-500 dark:text-gray-500">
            <p>💡 Try asking about the weather, news, or any topic</p>
            <p>🎤 Use voice for a more natural conversation</p>
            <p>⚡ Responses are generated in real-time</p>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className={clsx('relative flex-1 flex flex-col', className)}>
      {/* Messages container */}
      <div
        ref={containerRef}
        className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent"
      >
        {/* Load more indicator */}
        {hasMore && (
          <div className="flex justify-center py-4">
            <motion.div
              className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Loading more messages...</span>
            </motion.div>
          </div>
        )}

        {/* Messages */}
        <div className="space-y-4 p-4">
          <AnimatePresence initial={false}>
            {messages.map((message, index) => (
              <MessageBubble
                key={message.id}
                message={message}
                showTimestamp={
                  index === 0 ||
                  index === messages.length - 1 ||
                  (index > 0 && 
                    new Date(message.createdAt).getTime() - 
                    new Date(messages[index - 1]!.createdAt).getTime() > 300000) // 5 minutes
                }
              />
            ))}
          </AnimatePresence>

          {/* Typing indicator */}
          <AnimatePresence>
            {isLoading && (
              <TypingIndicator />
            )}
          </AnimatePresence>
        </div>

        {/* Scroll anchor */}
        <div ref={messagesEndRef} />
      </div>

      {/* Scroll to bottom button */}
      <AnimatePresence>
        {showScrollButton && (
          <motion.button
            className="absolute bottom-4 right-4 w-10 h-10 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg flex items-center justify-center transition-colors z-10"
            onClick={handleScrollToBottom}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            title="Scroll to bottom"
          >
            <ArrowDown className="w-5 h-5" />
          </motion.button>
        )}
      </AnimatePresence>
    </div>
  );
}

// Conversation list component for sidebar
export function ConversationList({
  conversations,
  currentConversationId,
  onSelectConversation,
  onDeleteConversation,
  className,
}: {
  conversations: Array<{
    id: string;
    title?: string;
    updatedAt: string;
    messageCount?: number;
  }>;
  currentConversationId?: string;
  onSelectConversation: (id: string) => void;
  onDeleteConversation?: (id: string) => void;
  className?: string;
}) {
  const [hoveredId, setHoveredId] = useState<string | null>(null);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  if (conversations.length === 0) {
    return (
      <div className={clsx('p-4 text-center', className)}>
        <p className="text-gray-500 dark:text-gray-400 text-sm">
          No conversations yet
        </p>
      </div>
    );
  }

  return (
    <div className={clsx('space-y-1', className)}>
      <AnimatePresence>
        {conversations.map((conversation) => (
          <motion.div
            key={conversation.id}
            className={clsx(
              'relative group cursor-pointer rounded-lg p-3 transition-colors',
              currentConversationId === conversation.id
                ? 'bg-primary-100 dark:bg-primary-900 border-l-4 border-primary-600'
                : 'hover:bg-gray-100 dark:hover:bg-gray-800'
            )}
            onClick={() => onSelectConversation(conversation.id)}
            onMouseEnter={() => setHoveredId(conversation.id)}
            onMouseLeave={() => setHoveredId(null)}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            layout
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {conversation.title || 'New Conversation'}
                </h4>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {formatDate(conversation.updatedAt)}
                  </span>
                  {conversation.messageCount && (
                    <span className="text-xs text-gray-400 dark:text-gray-500">
                      {conversation.messageCount} messages
                    </span>
                  )}
                </div>
              </div>

              {/* Delete button */}
              {onDeleteConversation && hoveredId === conversation.id && (
                <motion.button
                  className="ml-2 p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteConversation(conversation.id);
                  }}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  title="Delete conversation"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </motion.button>
              )}
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}

export default ChatContainer;
