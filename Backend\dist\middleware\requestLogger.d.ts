import { Request, Response, NextFunction } from 'express';
declare global {
    namespace Express {
        interface Request {
            requestId?: string;
            startTime?: number;
            user?: {
                id: string;
                email?: string;
            };
            skipRateLimit?: boolean;
        }
    }
}
export declare function requestLogger(req: Request, res: Response, next: NextFunction): void;
export declare function filterSensitiveData(data: any): any;
export declare function requestBodyLogger(req: Request, res: Response, next: NextFunction): void;
export declare function apiUsageTracker(req: Request, res: Response, next: NextFunction): void;
export declare function performanceMonitor(req: Request, res: Response, next: NextFunction): void;
export declare function errorTracker(error: Error, req: Request, res: Response, next: NextFunction): void;
export declare function healthCheckFilter(req: Request, res: Response, next: NextFunction): void;
export declare function corsLogger(req: Request, res: Response, next: NextFunction): void;
export default requestLogger;
//# sourceMappingURL=requestLogger.d.ts.map