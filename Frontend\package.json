{"name": "voice-chat-frontend", "version": "1.0.0", "description": "Frontend for Voice-Enabled AI Chat Application", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "cypress open", "test:e2e:headless": "cypress run", "analyze": "cross-env ANALYZE=true next build", "export": "next export"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.2", "@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "zustand": "^4.4.7", "axios": "^1.6.2", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "react-use": "^17.4.2", "uuid": "^9.0.1", "@types/uuid": "^9.0.7", "date-fns": "^2.30.0", "react-markdown": "^9.0.1", "lucide-react": "^0.294.0"}, "devDependencies": {"eslint": "^8.54.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "cypress": "^13.6.1", "cross-env": "^7.0.3", "@next/bundle-analyzer": "^14.0.4"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}