import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { chatApi } from '@/services/api';
import { Conversation, Message, ChatStore } from '@/types';
import toast from 'react-hot-toast';

interface ChatState extends ChatStore {
  // Additional state
  isConnected: boolean;
  lastError?: string;
  retryCount: number;
}

export const useChatStore = create<ChatState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        conversations: [],
        currentConversation: undefined,
        messages: [],
        isLoading: false,
        error: undefined,
        isConnected: true,
        lastError: undefined,
        retryCount: 0,

        // Actions
        sendMessage: async (message: string, conversationId?: string) => {
          const state = get();
          
          try {
            set({ isLoading: true, error: undefined });

            // Create optimistic user message
            const userMessage: Message = {
              id: `temp-${Date.now()}`,
              conversationId: conversationId || 'temp',
              role: 'user',
              content: message,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            };

            // Add user message to UI immediately
            set(state => ({
              messages: [...state.messages, userMessage]
            }));

            // Send message to API
            const response = await chatApi.sendMessage({
              message,
              conversationId,
              context: {
                previousMessages: 5,
              },
            });

            // Update with real message IDs and add AI response
            const realUserMessage: Message = {
              ...userMessage,
              id: `user-${Date.now()}`,
              conversationId: response.conversationId,
            };

            const aiMessage: Message = {
              id: `ai-${Date.now()}`,
              conversationId: response.conversationId,
              role: 'assistant',
              content: response.response,
              audioUrl: response.audioUrl,
              tokensUsed: response.metadata.tokensUsed,
              responseTime: response.metadata.responseTime,
              modelUsed: response.metadata.modelUsed,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            };

            // Update messages and current conversation
            set(state => ({
              messages: [
                ...state.messages.filter(m => m.id !== userMessage.id),
                realUserMessage,
                aiMessage,
              ],
              currentConversation: {
                id: response.conversationId,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              },
              isLoading: false,
              retryCount: 0,
            }));

            // Update conversations list if needed
            if (!state.conversations.find(c => c.id === response.conversationId)) {
              set(state => ({
                conversations: [
                  {
                    id: response.conversationId,
                    title: message.slice(0, 50) + (message.length > 50 ? '...' : ''),
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                  },
                  ...state.conversations,
                ],
              }));
            }

          } catch (error: any) {
            console.error('Failed to send message:', error);
            
            // Remove optimistic message on error
            set(state => ({
              messages: state.messages.filter(m => m.id !== userMessage.id),
              isLoading: false,
              error: error.error?.message || 'Failed to send message',
              retryCount: state.retryCount + 1,
            }));

            toast.error('Failed to send message. Please try again.');
          }
        },

        loadConversations: async () => {
          try {
            set({ isLoading: true, error: undefined });

            const conversations = await chatApi.getConversations({
              page: 1,
              limit: 50,
              sortBy: 'updatedAt',
              sortOrder: 'desc',
            });

            set({
              conversations: conversations.data || [],
              isLoading: false,
              isConnected: true,
              retryCount: 0,
            });

          } catch (error: any) {
            console.error('Failed to load conversations:', error);
            
            set({
              isLoading: false,
              error: error.error?.message || 'Failed to load conversations',
              isConnected: false,
              retryCount: get().retryCount + 1,
            });

            toast.error('Failed to load conversations');
          }
        },

        loadConversation: async (conversationId: string) => {
          try {
            set({ isLoading: true, error: undefined });

            const conversation = await chatApi.getHistory(conversationId, {
              limit: 100,
              offset: 0,
            });

            set({
              currentConversation: {
                id: conversation.conversationId,
                title: conversation.title,
                createdAt: conversation.createdAt,
                updatedAt: conversation.updatedAt,
                metadata: conversation.metadata,
              },
              messages: conversation.messages || [],
              isLoading: false,
              isConnected: true,
              retryCount: 0,
            });

          } catch (error: any) {
            console.error('Failed to load conversation:', error);
            
            set({
              isLoading: false,
              error: error.error?.message || 'Failed to load conversation',
              isConnected: false,
              retryCount: get().retryCount + 1,
            });

            toast.error('Failed to load conversation');
          }
        },

        createConversation: async (): Promise<string> => {
          // For now, we'll create conversations implicitly when sending the first message
          // This matches the backend behavior
          const tempId = `conv-${Date.now()}`;
          
          set(state => ({
            currentConversation: {
              id: tempId,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            messages: [],
          }));

          return tempId;
        },

        deleteConversation: async (conversationId: string) => {
          try {
            set({ isLoading: true, error: undefined });

            await chatApi.deleteConversation(conversationId);

            set(state => ({
              conversations: state.conversations.filter(c => c.id !== conversationId),
              currentConversation: state.currentConversation?.id === conversationId 
                ? undefined 
                : state.currentConversation,
              messages: state.currentConversation?.id === conversationId 
                ? [] 
                : state.messages,
              isLoading: false,
              retryCount: 0,
            }));

            toast.success('Conversation deleted');

          } catch (error: any) {
            console.error('Failed to delete conversation:', error);
            
            set({
              isLoading: false,
              error: error.error?.message || 'Failed to delete conversation',
              retryCount: get().retryCount + 1,
            });

            toast.error('Failed to delete conversation');
          }
        },

        clearMessages: () => {
          set({
            messages: [],
            currentConversation: undefined,
            error: undefined,
          });
        },

        setError: (error: string | null) => {
          set({ error: error || undefined });
        },

        // Additional helper actions
        updateConversationTitle: async (conversationId: string, title: string) => {
          try {
            await chatApi.updateTitle(conversationId, title);

            set(state => ({
              conversations: state.conversations.map(c =>
                c.id === conversationId ? { ...c, title, updatedAt: new Date().toISOString() } : c
              ),
              currentConversation: state.currentConversation?.id === conversationId
                ? { ...state.currentConversation, title, updatedAt: new Date().toISOString() }
                : state.currentConversation,
            }));

            toast.success('Title updated');

          } catch (error: any) {
            console.error('Failed to update title:', error);
            toast.error('Failed to update title');
          }
        },

        clearAllConversations: async () => {
          try {
            set({ isLoading: true });

            const result = await chatApi.clearConversations();

            set({
              conversations: [],
              currentConversation: undefined,
              messages: [],
              isLoading: false,
            });

            toast.success(`${result.deletedCount} conversations deleted`);

          } catch (error: any) {
            console.error('Failed to clear conversations:', error);
            
            set({ isLoading: false });
            toast.error('Failed to clear conversations');
          }
        },

        // Connection management
        checkConnection: async () => {
          try {
            await chatApi.getStats();
            set({ isConnected: true, retryCount: 0 });
            return true;
          } catch (error) {
            set({ isConnected: false });
            return false;
          }
        },

        retry: async () => {
          const state = get();
          if (state.retryCount < 3) {
            await state.loadConversations();
          }
        },
      }),
      {
        name: 'chat-store',
        partialize: (state) => ({
          conversations: state.conversations,
          currentConversation: state.currentConversation,
          // Don't persist messages as they should be loaded fresh
        }),
      }
    ),
    {
      name: 'chat-store',
    }
  )
);
