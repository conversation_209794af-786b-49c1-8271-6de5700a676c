import winston from 'winston';
export declare const logger: winston.Logger;
export declare function logRequest(req: any, res: any, responseTime?: number): void;
export declare function logError(error: Error, context?: any): void;
export declare function logDatabase(operation: string, table: string, duration?: number, error?: Error): void;
export declare function logAPI(service: string, operation: string, duration?: number, error?: Error): void;
export declare function logPerformance(operation: string, startTime: number, metadata?: any): void;
export declare function logSecurity(event: string, details: any, severity?: 'info' | 'warn' | 'error'): void;
export declare function logHealth(service: string, status: 'healthy' | 'unhealthy', details?: any): void;
export declare function logStartup(service: string, version: string, port?: number): void;
export declare function logShutdown(service: string, reason?: string): void;
export default logger;
//# sourceMappingURL=logger.d.ts.map