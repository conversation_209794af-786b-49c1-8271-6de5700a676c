# Deployment Guidelines - Voice-Enabled AI Chat Application

## 1. Deployment Overview

### 1.1 Deployment Strategy
The application follows a **microservices deployment pattern** with separate deployments for frontend and backend components:
- **Frontend**: Static site deployment (Vercel/Netlify)
- **Backend**: Container-based deployment (Railway/Heroku/AWS)
- **Database**: Managed database service (PostgreSQL)
- **File Storage**: Cloud storage service (AWS S3/Cloudinary)

### 1.2 Environment Structure
```
Development → Staging → Production
     ↓           ↓         ↓
   Local     Preview    Live Site
  Testing    Testing    Monitoring
```

### 1.3 Deployment Architecture
```
┌─────────────────────────────────────────────────────────────────┐
│                        PRODUCTION                               │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   CDN/Edge      │  │   Load Balancer │  │   Monitoring    │ │
│  │   (CloudFlare)  │  │   (AWS ALB)     │  │   (DataDog)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│           │                     │                     │         │
│           ▼                     ▼                     ▼         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Frontend      │  │   Backend API   │  │   Database      │ │
│  │   (Vercel)      │  │   (Railway)     │  │   (PostgreSQL)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                                │                               │
│                                ▼                               │
│                       ┌─────────────────┐                     │
│                       │   File Storage  │                     │
│                       │   (AWS S3)      │                     │
│                       └─────────────────┘                     │
└─────────────────────────────────────────────────────────────────┘
```

## 2. Environment Configuration

### 2.1 Development Environment
```bash
# Frontend (.env.local)
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_ENVIRONMENT=development
NEXT_PUBLIC_ENABLE_ANALYTICS=false

# Backend (.env)
NODE_ENV=development
PORT=8000
DATABASE_URL=sqlite:./dev.db
OPENAI_API_KEY=your_dev_key
CORS_ORIGIN=http://localhost:3000
LOG_LEVEL=debug
```

### 2.2 Staging Environment
```bash
# Frontend (.env.staging)
NEXT_PUBLIC_API_URL=https://api-staging.yourapp.com
NEXT_PUBLIC_ENVIRONMENT=staging
NEXT_PUBLIC_ENABLE_ANALYTICS=true

# Backend (.env.staging)
NODE_ENV=staging
PORT=8000
DATABASE_URL=**************************************/voicechat
OPENAI_API_KEY=your_staging_key
CORS_ORIGIN=https://staging.yourapp.com
LOG_LEVEL=info
RATE_LIMIT_ENABLED=true
```

### 2.3 Production Environment
```bash
# Frontend (.env.production)
NEXT_PUBLIC_API_URL=https://api.yourapp.com
NEXT_PUBLIC_ENVIRONMENT=production
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn

# Backend (.env.production)
NODE_ENV=production
PORT=8000
DATABASE_URL=***********************************/voicechat
OPENAI_API_KEY=your_prod_key
CORS_ORIGIN=https://yourapp.com
LOG_LEVEL=warn
RATE_LIMIT_ENABLED=true
SENTRY_DSN=your_sentry_dsn
```

## 3. Frontend Deployment

### 3.1 Vercel Deployment (Recommended)

#### 3.1.1 Vercel Configuration
```json
// vercel.json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "env": {
    "NEXT_PUBLIC_API_URL": "@api-url"
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

#### 3.1.2 Build Configuration
```json
// package.json
{
  "scripts": {
    "build": "next build",
    "start": "next start",
    "export": "next export",
    "deploy": "vercel --prod"
  }
}
```

#### 3.1.3 Deployment Commands
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy to staging
vercel

# Deploy to production
vercel --prod
```

### 3.2 Alternative: Netlify Deployment

#### 3.2.1 Netlify Configuration
```toml
# netlify.toml
[build]
  publish = "out"
  command = "npm run build && npm run export"

[build.environment]
  NODE_VERSION = "18"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"

[[redirects]]
  from = "/api/*"
  to = "https://api.yourapp.com/:splat"
  status = 200
```

## 4. Backend Deployment

### 4.1 Railway Deployment (Recommended)

#### 4.1.1 Railway Configuration
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 8000

CMD ["npm", "start"]
```

#### 4.1.2 Railway Setup
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# Initialize project
railway init

# Deploy
railway up
```

#### 4.1.3 Environment Variables Setup
```bash
# Set environment variables
railway variables set NODE_ENV=production
railway variables set DATABASE_URL=${{Postgres.DATABASE_URL}}
railway variables set OPENAI_API_KEY=your_key
```

### 4.2 Alternative: Heroku Deployment

#### 4.2.1 Heroku Configuration
```json
// package.json
{
  "engines": {
    "node": "18.x"
  },
  "scripts": {
    "heroku-postbuild": "npm run build"
  }
}
```

#### 4.2.2 Procfile
```
web: npm start
```

#### 4.2.3 Deployment Commands
```bash
# Install Heroku CLI
# Create Heroku app
heroku create your-app-name

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set OPENAI_API_KEY=your_key

# Deploy
git push heroku main
```

## 5. Database Deployment

### 5.1 PostgreSQL Setup

#### 5.1.1 Production Database Configuration
```bash
# Railway PostgreSQL
railway add postgresql

# Heroku PostgreSQL
heroku addons:create heroku-postgresql:hobby-dev

# AWS RDS PostgreSQL
aws rds create-db-instance \
  --db-instance-identifier voicechat-prod \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --master-username admin \
  --master-user-password your_password \
  --allocated-storage 20
```

#### 5.1.2 Database Migration
```bash
# Run migrations
npm run migrate:prod

# Seed initial data
npm run seed:prod
```

### 5.2 Database Backup Strategy
```bash
# Automated daily backups
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql

# Restore from backup
psql $DATABASE_URL < backup_20250702.sql
```

## 6. CI/CD Pipeline

### 6.1 GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test
      - run: npm run test:e2e

  deploy-frontend:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'

  deploy-backend:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - uses: railway-deploy@v1
        with:
          railway-token: ${{ secrets.RAILWAY_TOKEN }}
```

### 6.2 Deployment Checklist
- [ ] All tests pass
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] SSL certificates configured
- [ ] Domain DNS configured
- [ ] Monitoring setup
- [ ] Backup strategy implemented
- [ ] Security headers configured

## 7. Monitoring and Logging

### 7.1 Application Monitoring
```javascript
// Sentry configuration
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 1.0,
});
```

### 7.2 Performance Monitoring
```javascript
// Web Vitals tracking
export function reportWebVitals(metric) {
  if (metric.label === 'web-vital') {
    // Send to analytics service
    gtag('event', metric.name, {
      value: Math.round(metric.value),
      event_label: metric.id,
    });
  }
}
```

### 7.3 Health Checks
```javascript
// Backend health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version
  });
});
```

## 8. Security Configuration

### 8.1 HTTPS Configuration
```javascript
// Security headers middleware
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000');
  next();
});
```

### 8.2 Environment Security
```bash
# Secure environment variable management
# Use secrets management service
export OPENAI_API_KEY=$(aws secretsmanager get-secret-value --secret-id openai-key --query SecretString --output text)
```

## 9. Scaling Considerations

### 9.1 Horizontal Scaling
```yaml
# Docker Compose for scaling
version: '3.8'
services:
  api:
    build: ./backend
    deploy:
      replicas: 3
    environment:
      - NODE_ENV=production
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    depends_on:
      - api
```

### 9.2 Database Scaling
```sql
-- Read replicas for scaling
-- Connection pooling configuration
-- Query optimization
-- Indexing strategy
```

## 10. Rollback Strategy

### 10.1 Frontend Rollback
```bash
# Vercel rollback
vercel rollback [deployment-url]

# Netlify rollback
netlify sites:list
netlify api rollbackSiteDeploy --site-id=SITE_ID --deploy-id=DEPLOY_ID
```

### 10.2 Backend Rollback
```bash
# Railway rollback
railway rollback [deployment-id]

# Heroku rollback
heroku rollback v123
```

### 10.3 Database Rollback
```bash
# Database migration rollback
npm run migrate:rollback

# Point-in-time recovery
aws rds restore-db-instance-to-point-in-time \
  --source-db-instance-identifier voicechat-prod \
  --target-db-instance-identifier voicechat-rollback \
  --restore-time 2025-07-02T10:00:00Z
```

## 11. Maintenance Procedures

### 11.1 Regular Maintenance Tasks
- Weekly dependency updates
- Monthly security patches
- Quarterly performance reviews
- Database maintenance and optimization
- Log rotation and cleanup
- SSL certificate renewal

### 11.2 Maintenance Windows
```bash
# Scheduled maintenance script
#!/bin/bash
echo "Starting maintenance window..."

# Put app in maintenance mode
railway variables set MAINTENANCE_MODE=true

# Run maintenance tasks
npm run db:optimize
npm run logs:cleanup
npm run cache:clear

# Exit maintenance mode
railway variables set MAINTENANCE_MODE=false

echo "Maintenance completed."
```

## 12. Disaster Recovery

### 12.1 Backup Strategy
- **Database**: Daily automated backups with 30-day retention
- **File Storage**: Cross-region replication
- **Code**: Git repository with multiple remotes
- **Configuration**: Infrastructure as Code (Terraform)

### 12.2 Recovery Procedures
```bash
# Database recovery
psql $DATABASE_URL < latest_backup.sql

# Application recovery
git clone https://github.com/your-org/voice-chat-app.git
cd voice-chat-app
npm install
npm run deploy:emergency
```

---

**Document Version**: 1.0  
**Last Updated**: 2025-07-02  
**Deployment Review**: 2025-07-16
