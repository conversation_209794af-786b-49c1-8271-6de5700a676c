"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.whitelistMiddleware = exports.rateLimitInfo = exports.progressiveRateLimiter = exports.strictRateLimiter = exports.chatRateLimiter = exports.uploadRateLimiter = exports.voiceRateLimiter = exports.rateLimiter = void 0;
exports.isRateLimited = isRateLimited;
exports.getRateLimitStatus = getRateLimitStatus;
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const logger_1 = require("@/utils/logger");
const environment_1 = require("@/config/environment");
const errorHandler_1 = require("./errorHandler");
const rateLimitHandler = (req, res) => {
    const clientInfo = {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.originalUrl,
        method: req.method,
    };
    (0, logger_1.logSecurity)('RATE_LIMIT_EXCEEDED', clientInfo, 'warn');
    throw new errorHandler_1.RateLimitError('Too many requests, please try again later');
};
const skipRateLimit = (req) => {
    if (req.path === '/api/health') {
        return true;
    }
    if (process.env.NODE_ENV === 'development' && req.ip === '127.0.0.1') {
        return false;
    }
    return false;
};
const keyGenerator = (req) => {
    let key = req.ip || 'unknown';
    if (req.user?.id) {
        key += `:user:${req.user.id}`;
    }
    return key;
};
exports.rateLimiter = (0, express_rate_limit_1.default)({
    windowMs: environment_1.config.RATE_LIMIT_WINDOW_MS,
    max: environment_1.config.RATE_LIMIT_MAX_REQUESTS,
    message: {
        success: false,
        error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: 'Too many requests, please try again later',
        },
        timestamp: new Date().toISOString(),
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: rateLimitHandler,
    skip: skipRateLimit,
    keyGenerator,
    onLimitReached: (req) => {
        logger_1.logger.warn('Rate limit reached', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            url: req.originalUrl,
        });
    },
});
exports.voiceRateLimiter = (0, express_rate_limit_1.default)({
    windowMs: environment_1.config.RATE_LIMIT_WINDOW_MS,
    max: environment_1.config.RATE_LIMIT_VOICE_MAX_REQUESTS,
    message: {
        success: false,
        error: {
            code: 'VOICE_RATE_LIMIT_EXCEEDED',
            message: 'Too many voice requests, please try again later',
        },
        timestamp: new Date().toISOString(),
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: rateLimitHandler,
    skip: skipRateLimit,
    keyGenerator,
    onLimitReached: (req) => {
        logger_1.logger.warn('Voice rate limit reached', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            url: req.originalUrl,
        });
    },
});
exports.uploadRateLimiter = (0, express_rate_limit_1.default)({
    windowMs: environment_1.config.RATE_LIMIT_WINDOW_MS,
    max: 10,
    message: {
        success: false,
        error: {
            code: 'UPLOAD_RATE_LIMIT_EXCEEDED',
            message: 'Too many file uploads, please try again later',
        },
        timestamp: new Date().toISOString(),
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: rateLimitHandler,
    skip: skipRateLimit,
    keyGenerator,
});
exports.chatRateLimiter = (0, express_rate_limit_1.default)({
    windowMs: environment_1.config.RATE_LIMIT_WINDOW_MS,
    max: 30,
    message: {
        success: false,
        error: {
            code: 'CHAT_RATE_LIMIT_EXCEEDED',
            message: 'Too many chat messages, please slow down',
        },
        timestamp: new Date().toISOString(),
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: rateLimitHandler,
    skip: skipRateLimit,
    keyGenerator,
});
exports.strictRateLimiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000,
    max: 5,
    message: {
        success: false,
        error: {
            code: 'STRICT_RATE_LIMIT_EXCEEDED',
            message: 'Too many requests for this operation, please try again later',
        },
        timestamp: new Date().toISOString(),
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: rateLimitHandler,
    skip: skipRateLimit,
    keyGenerator,
});
class ProgressiveRateLimiter {
    constructor(baseLimit = 100, windowMs = 60000) {
        this.violations = new Map();
        this.baseLimit = baseLimit;
        this.windowMs = windowMs;
    }
    getLimit(key) {
        const violation = this.violations.get(key);
        if (!violation) {
            return this.baseLimit;
        }
        if (Date.now() - violation.lastViolation > 60 * 60 * 1000) {
            this.violations.delete(key);
            return this.baseLimit;
        }
        const reductionFactor = Math.min(violation.count * 0.1, 0.8);
        return Math.floor(this.baseLimit * (1 - reductionFactor));
    }
    recordViolation(key) {
        const existing = this.violations.get(key);
        if (existing) {
            existing.count += 1;
            existing.lastViolation = Date.now();
        }
        else {
            this.violations.set(key, {
                count: 1,
                lastViolation: Date.now(),
            });
        }
        logger_1.logger.warn('Progressive rate limit violation recorded', {
            key,
            violationCount: this.violations.get(key)?.count,
            newLimit: this.getLimit(key),
        });
    }
    createMiddleware() {
        return (0, express_rate_limit_1.default)({
            windowMs: this.windowMs,
            max: (req) => {
                const key = keyGenerator(req);
                return this.getLimit(key);
            },
            handler: (req, res) => {
                const key = keyGenerator(req);
                this.recordViolation(key);
                rateLimitHandler(req, res);
            },
            skip: skipRateLimit,
            keyGenerator,
        });
    }
}
exports.progressiveRateLimiter = new ProgressiveRateLimiter().createMiddleware();
const rateLimitInfo = (req, res, next) => {
    res.set({
        'X-RateLimit-Policy': 'General API: 100/min, Voice: 20/min, Chat: 30/min',
        'X-RateLimit-Remaining-General': res.get('RateLimit-Remaining') || 'unknown',
    });
    next();
};
exports.rateLimitInfo = rateLimitInfo;
async function isRateLimited(ip, endpoint) {
    return false;
}
function getRateLimitStatus(req) {
    const limit = parseInt(req.get('RateLimit-Limit') || '0');
    const remaining = parseInt(req.get('RateLimit-Remaining') || '0');
    const resetTime = parseInt(req.get('RateLimit-Reset') || '0');
    return {
        limit,
        remaining,
        reset: new Date(resetTime * 1000),
    };
}
const whitelistMiddleware = (whitelist = []) => {
    return (req, res, next) => {
        if (whitelist.includes(req.ip)) {
            req.skipRateLimit = true;
        }
        next();
    };
};
exports.whitelistMiddleware = whitelistMiddleware;
exports.default = exports.rateLimiter;
//# sourceMappingURL=rateLimiter.js.map