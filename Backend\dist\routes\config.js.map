{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/routes/config.ts"], "names": [], "mappings": ";;AAAA,qCAAiC;AACjC,4DAAyD;AACzD,4CAAmD;AACnD,sDAA8C;AAG9C,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAGxB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC9C,MAAM,YAAY,GAAiB;QACjC,QAAQ,EAAE;YACR,iBAAiB,EAAE,IAAI;YACvB,eAAe,EAAE,IAAI;YACrB,cAAc,EAAE,IAAI;SACrB;QACD,MAAM,EAAE;YACN,oBAAoB,EAAE,oBAAM,CAAC,sBAAsB;YACnD,gBAAgB,EAAE,IAAI;YACtB,sBAAsB,EAAE,GAAG;YAC3B,WAAW,EAAE,oBAAM,CAAC,aAAa;SAClC;QACD,YAAY,EAAE,yBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC3C,EAAE,EAAE,KAAK,CAAC,EAAE;YACZ,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,MAAM,EAAE,KAAK,CAAC,MAAM;SACrB,CAAC,CAAC;QACH,kBAAkB,EAAE;YAClB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE;YACtD,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE;YACtD,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE;YACtD,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE;YACrD,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE;YACvD,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE;YAC3D,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE;YACtD,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE;YACnD,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE;YACjD,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE;SAClD;KACF,CAAC;IAEF,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,6CAA6C;QACtD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AAGJ,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACpD,MAAM,MAAM,GAAG,yBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC5C,EAAE,EAAE,KAAK,CAAC,EAAE;QACZ,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,QAAQ,EAAE,KAAK,CAAC,QAAQ;QACxB,MAAM,EAAE,KAAK,CAAC,MAAM;QACpB,WAAW,EAAE,GAAG,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,MAAM,aAAa,KAAK,CAAC,QAAQ,EAAE;KAC1E,CAAC,CAAC,CAAC;IAEJ,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,sCAAsC;QAC/C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AAGJ,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACvD,MAAM,SAAS,GAAG;QAChB;YACE,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,SAAS;YACrB,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACjC,iBAAiB,EAAE,IAAI;YACvB,YAAY,EAAE,IAAI;SACnB;QACD;YACE,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,SAAS;YACrB,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YAC3B,iBAAiB,EAAE,IAAI;YACvB,YAAY,EAAE,IAAI;SACnB;QACD;YACE,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,UAAU;YACtB,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;YACrB,iBAAiB,EAAE,IAAI;YACvB,YAAY,EAAE,IAAI;SACnB;QACD;YACE,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,SAAS;YACrB,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YAC3B,iBAAiB,EAAE,IAAI;YACvB,YAAY,EAAE,IAAI;SACnB;QACD;YACE,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,UAAU;YACtB,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,iBAAiB,EAAE,IAAI;YACvB,YAAY,EAAE,IAAI;SACnB;QACD;YACE,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,YAAY;YAClB,UAAU,EAAE,WAAW;YACvB,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;YACrB,iBAAiB,EAAE,IAAI;YACvB,YAAY,EAAE,IAAI;SACnB;QACD;YACE,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,SAAS;YACrB,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,iBAAiB,EAAE,IAAI;YACvB,YAAY,EAAE,IAAI;SACnB;QACD;YACE,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,UAAU;YAChB,UAAU,EAAE,KAAK;YACjB,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,iBAAiB,EAAE,IAAI;YACvB,YAAY,EAAE,IAAI;SACnB;QACD;YACE,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,KAAK;YACjB,OAAO,EAAE,CAAC,IAAI,CAAC;YACf,iBAAiB,EAAE,IAAI;YACvB,YAAY,EAAE,IAAI;SACnB;QACD;YACE,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,IAAI;YAChB,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YAC3B,iBAAiB,EAAE,IAAI;YACvB,YAAY,EAAE,IAAI;SACnB;KACF,CAAC;IAEF,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,4CAA4C;QACrD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AAGJ,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACpD,MAAM,MAAM,GAAG;QACb,UAAU,EAAE;YACV,OAAO,EAAE;gBACP,QAAQ,EAAE,oBAAM,CAAC,uBAAuB;gBACxC,QAAQ,EAAE,oBAAM,CAAC,oBAAoB;gBACrC,WAAW,EAAE,iCAAiC;aAC/C;YACD,KAAK,EAAE;gBACL,QAAQ,EAAE,oBAAM,CAAC,6BAA6B;gBAC9C,QAAQ,EAAE,oBAAM,CAAC,oBAAoB;gBACrC,WAAW,EAAE,sCAAsC;aACpD;YACD,IAAI,EAAE;gBACJ,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,oBAAM,CAAC,oBAAoB;gBACrC,WAAW,EAAE,0BAA0B;aACxC;YACD,MAAM,EAAE;gBACN,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,oBAAM,CAAC,oBAAoB;gBACrC,WAAW,EAAE,yBAAyB;aACvC;SACF;QACD,UAAU,EAAE;YACV,WAAW,EAAE,oBAAM,CAAC,aAAa;YACjC,oBAAoB,EAAE,oBAAM,CAAC,sBAAsB;YACnD,cAAc,EAAE,oBAAM,CAAC,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC;SACxD;QACD,aAAa,EAAE;YACb,gBAAgB,EAAE,IAAI;YACtB,sBAAsB,EAAE,GAAG;YAC3B,kBAAkB,EAAE,EAAE;SACvB;QACD,QAAQ,EAAE;YACR,SAAS,EAAE,oBAAM,CAAC,iBAAiB;YACnC,KAAK,EAAE,oBAAM,CAAC,YAAY;YAC1B,WAAW,EAAE,oBAAM,CAAC,kBAAkB;SACvC;KACF,CAAC;IAEF,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,mCAAmC;QAC5C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AAGJ,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACtD,MAAM,QAAQ,GAAG;QACf,eAAe,EAAE;YACf,OAAO,EAAE,IAAI;YACb,YAAY,EAAE,IAAI;YAClB,YAAY,EAAE,IAAI;YAClB,qBAAqB,EAAE,IAAI;YAC3B,YAAY,EAAE,KAAK;SACpB;QACD,IAAI,EAAE;YACJ,OAAO,EAAE,IAAI;YACb,mBAAmB,EAAE,IAAI;YACzB,gBAAgB,EAAE,IAAI;YACtB,aAAa,EAAE,IAAI;YACnB,eAAe,EAAE,KAAK;SACvB;QACD,EAAE,EAAE;YACF,QAAQ,EAAE,IAAI;YACd,aAAa,EAAE,IAAI;YACnB,eAAe,EAAE,IAAI;YACrB,WAAW,EAAE,KAAK;SACnB;QACD,SAAS,EAAE;YACT,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;YACpD,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;YACrD,qBAAqB,EAAE,IAAI;SAC5B;QACD,YAAY,EAAE;YACZ,oBAAoB,EAAE,KAAK;YAC3B,qBAAqB,EAAE,KAAK;YAC5B,mBAAmB,EAAE,KAAK;YAC1B,sBAAsB,EAAE,KAAK;SAC9B;KACF,CAAC;IAEF,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,sCAAsC;QAC/C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AAGJ,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACpD,MAAM,UAAU,GAAG;QACjB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,OAAO;QACnD,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ;QACjC,WAAW,EAAE,OAAO,CAAC,OAAO;QAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,YAAY,EAAE,OAAO,CAAC,IAAI;QAC1B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QACpC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,eAAe,EAAE,CAAC,QAAQ;QAC1D,MAAM,EAAE,OAAO;QACf,UAAU,EAAE,IAAI;QAChB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,kBAAkB,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;QAC1C,IAAI,EAAE;YACJ,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,CAAC;SAC1E;KACF,CAAC;IAEF,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,UAAU;QAChB,OAAO,EAAE,2CAA2C;QACpD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AAGJ,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAClD,MAAM,aAAa,GAAG;QACpB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI;QACpE,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI;QAChF,MAAM,EAAE;YACN,cAAc,EAAE,uBAAuB;YACvC,cAAc,EAAE,sBAAsB;YACtC,eAAe,EAAE,wBAAwB;YACzC,aAAa,EAAE,sBAAsB;SACtC;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,qBAAqB;YAC3B,UAAU,EAAE,2BAA2B;YACvC,MAAM,EAAE,uBAAuB;SAChC;QACD,SAAS,EAAE,iBAAiB;QAC5B,OAAO,EAAE;YACP,KAAK,EAAE,qBAAqB;YAC5B,MAAM,EAAE,4CAA4C;YACpD,OAAO,EAAE,gCAAgC;SAC1C;KACF,CAAC;IAEF,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE,aAAa;QACnB,OAAO,EAAE,4CAA4C;QACrD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AAEJ,kBAAe,MAAM,CAAC"}