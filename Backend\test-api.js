const http = require('http');

console.log('🧪 Testing Voice AI Chat Backend APIs...\n');

// Test function
function testAPI(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Run tests
async function runTests() {
  try {
    // Test 1: Health Check
    console.log('1️⃣ Testing Health Check...');
    const healthResult = await testAPI('/api/health');
    console.log(`   Status: ${healthResult.status}`);
    console.log(`   Response: ${healthResult.data.status}`);
    console.log(`   Services: ${JSON.stringify(healthResult.data.services)}\n`);

    // Test 2: Configuration
    console.log('2️⃣ Testing Configuration...');
    const configResult = await testAPI('/api/config');
    console.log(`   Status: ${configResult.status}`);
    console.log(`   Features: ${JSON.stringify(configResult.data.data.features)}\n`);

    // Test 3: Chat Message
    console.log('3️⃣ Testing Chat Message...');
    const chatResult = await testAPI('/api/chat/message', 'POST', {
      message: 'Hello! This is a test message from the API test script.'
    });
    console.log(`   Status: ${chatResult.status}`);
    console.log(`   Conversation ID: ${chatResult.data.data.conversationId}`);
    console.log(`   AI Response: ${chatResult.data.data.response.substring(0, 100)}...\n`);

    // Test 4: Conversation History
    if (chatResult.data.data.conversationId) {
      console.log('4️⃣ Testing Conversation History...');
      const historyResult = await testAPI(`/api/chat/history/${chatResult.data.data.conversationId}`);
      console.log(`   Status: ${historyResult.status}`);
      console.log(`   Messages Count: ${historyResult.data.data.messages.length}`);
      console.log(`   Conversation Title: ${historyResult.data.data.conversation.title}\n`);
    }

    // Test 5: Voice Transcription (Mock)
    console.log('5️⃣ Testing Voice Transcription (Mock)...');
    const transcribeResult = await testAPI('/api/voice/transcribe', 'POST', {});
    console.log(`   Status: ${transcribeResult.status}`);
    console.log(`   Mock Transcript: ${transcribeResult.data.data.transcript}\n`);

    // Test 6: Voice Synthesis (Mock)
    console.log('6️⃣ Testing Voice Synthesis (Mock)...');
    const synthesizeResult = await testAPI('/api/voice/synthesize', 'POST', {
      text: 'This is a test message for speech synthesis.'
    });
    console.log(`   Status: ${synthesizeResult.status}`);
    console.log(`   Audio URL: ${synthesizeResult.data.data.audioUrl}`);
    console.log(`   Duration: ${synthesizeResult.data.data.duration} seconds\n`);

    // Test 7: Available Voices
    console.log('7️⃣ Testing Available Voices...');
    const voicesResult = await testAPI('/api/voice/voices');
    console.log(`   Status: ${voicesResult.status}`);
    console.log(`   Available Voices: ${voicesResult.data.data.length} voices`);
    console.log(`   Voice Names: ${voicesResult.data.data.map(v => v.name).join(', ')}\n`);

    console.log('✅ All API tests completed successfully!');
    console.log('\n🎉 Voice AI Chat Backend is working perfectly!');
    console.log('\n📋 Test Summary:');
    console.log('   ✅ Health Check - Working');
    console.log('   ✅ Configuration - Working');
    console.log('   ✅ Chat Messages - Working');
    console.log('   ✅ Conversation History - Working');
    console.log('   ✅ Voice Transcription - Working (Mock)');
    console.log('   ✅ Voice Synthesis - Working (Mock)');
    console.log('   ✅ Available Voices - Working');
    console.log('\n🔗 Backend is ready for frontend integration!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Start tests
runTests();
