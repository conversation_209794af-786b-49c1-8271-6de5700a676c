"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
const errorHandler_1 = require("@/middleware/errorHandler");
const requestLogger_1 = require("@/middleware/requestLogger");
const rateLimiter_1 = require("@/middleware/rateLimiter");
const chat_1 = __importDefault(require("@/routes/chat"));
const voice_1 = __importDefault(require("@/routes/voice"));
const health_1 = __importDefault(require("@/routes/health"));
const config_1 = __importDefault(require("@/routes/config"));
const feedback_1 = __importDefault(require("@/routes/feedback"));
const audio_1 = __importDefault(require("@/routes/audio"));
const logger_1 = require("@/utils/logger");
const environment_1 = require("@/config/environment");
dotenv_1.default.config();
(0, environment_1.validateEnvironment)();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 8000;
app.use((0, helmet_1.default)({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
}));
const corsOptions = {
    origin: process.env.CORS_ORIGIN?.split(',') || 'http://localhost:3000',
    credentials: process.env.CORS_CREDENTIALS === 'true',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    maxAge: 86400,
};
app.use((0, cors_1.default)(corsOptions));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
if (process.env.ENABLE_REQUEST_LOGGING === 'true') {
    app.use(requestLogger_1.requestLogger);
}
if (process.env.RATE_LIMIT_ENABLED === 'true') {
    app.use(rateLimiter_1.rateLimiter);
}
app.use('/api/audio', express_1.default.static(path_1.default.join(__dirname, '../uploads')));
app.use('/api/health', health_1.default);
app.use('/api/config', config_1.default);
app.use('/api/chat', chat_1.default);
app.use('/api/voice', voice_1.default);
app.use('/api/feedback', feedback_1.default);
app.use('/api/audio', audio_1.default);
if (process.env.NODE_ENV === 'development' && process.env.ENABLE_API_DOCS === 'true') {
    app.get('/api-docs', (req, res) => {
        res.json({
            title: 'Voice-Enabled AI Chat API',
            version: '1.0.0',
            description: 'API documentation for the Voice-Enabled AI Chat Application',
            endpoints: {
                health: 'GET /api/health',
                config: 'GET /api/config',
                chat: {
                    message: 'POST /api/chat/message',
                    history: 'GET /api/chat/history/:conversationId',
                    delete: 'DELETE /api/chat/history/:conversationId'
                },
                voice: {
                    transcribe: 'POST /api/voice/transcribe',
                    synthesize: 'POST /api/voice/synthesize',
                    process: 'POST /api/voice/process'
                },
                feedback: 'POST /api/feedback',
                audio: {
                    upload: 'POST /api/audio/upload',
                    get: 'GET /api/audio/:filename'
                }
            },
            documentation: 'See PRD/API_Documentation.md for detailed API documentation'
        });
    });
}
app.get('/', (req, res) => {
    res.json({
        message: 'Voice-Enabled AI Chat API',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString(),
        documentation: process.env.NODE_ENV === 'development' ? '/api-docs' : undefined
    });
});
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: {
            code: 'NOT_FOUND',
            message: `Route ${req.method} ${req.originalUrl} not found`,
        },
        timestamp: new Date().toISOString()
    });
});
app.use(errorHandler_1.errorHandler);
const server = app.listen(PORT, () => {
    logger_1.logger.info(`🚀 Server running on port ${PORT}`);
    logger_1.logger.info(`📝 Environment: ${process.env.NODE_ENV}`);
    logger_1.logger.info(`🔗 CORS Origin: ${process.env.CORS_ORIGIN}`);
    if (process.env.NODE_ENV === 'development') {
        logger_1.logger.info(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
    }
});
process.on('SIGTERM', () => {
    logger_1.logger.info('SIGTERM received, shutting down gracefully');
    server.close(() => {
        logger_1.logger.info('Process terminated');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    logger_1.logger.info('SIGINT received, shutting down gracefully');
    server.close(() => {
        logger_1.logger.info('Process terminated');
        process.exit(0);
    });
});
process.on('uncaughtException', (error) => {
    logger_1.logger.error('Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
exports.default = app;
//# sourceMappingURL=index.js.map