import { PaginatedResponse } from '@/types';
export declare class ConversationService {
    getConversationWithMessages(conversationId: string, options?: {
        limit?: number;
        offset?: number;
        before?: Date;
        after?: Date;
    }): Promise<{
        metadata: Record<string, any>;
        messages: {
            metadata: Record<string, any>;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            conversationId: string;
            role: string;
            content: string;
            audioUrl: string | null;
            audioDuration: number | null;
            audioFormat: string | null;
            audioSize: number | null;
            transcript: string | null;
            transcriptConfidence: number | null;
            transcriptLanguage: string | null;
            tokensUsed: number | null;
            responseTime: number | null;
            modelUsed: string | null;
        }[];
        id: string;
        userId: string | null;
        createdAt: Date;
        updatedAt: Date;
        title: string | null;
    } | null>;
    getUserConversations(userId?: string, options?: {
        page?: number;
        limit?: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
    }): Promise<PaginatedResponse<any>>;
    updateConversationTitle(conversationId: string, title: string, userId?: string): Promise<{
        metadata: Record<string, any>;
        id: string;
        userId: string | null;
        createdAt: Date;
        updatedAt: Date;
        title: string | null;
    }>;
    deleteConversation(conversationId: string, userId?: string): Promise<void>;
    clearUserConversations(userId?: string): Promise<number>;
    getConversationStats(userId?: string): Promise<{
        totalConversations: number;
        totalMessages: number;
        recentConversations: number;
        avgMessagesPerConversation: number;
        stats: {
            conversationsThisWeek: number;
            messagesThisWeek: number;
            longestConversation: {
                id: string;
                title: string | null;
                messageCount: number;
            } | null;
        };
    }>;
    private getRecentMessageCount;
    private getLongestConversation;
    private parseMetadata;
}
export default ConversationService;
//# sourceMappingURL=conversationService.d.ts.map