const { GoogleGenerativeAI } = require('@google/generative-ai');

// Test Gemini API connection
async function testGemini() {
  try {
    console.log('Testing Gemini API connection...');
    
    const genAI = new GoogleGenerativeAI('AIzaSyDt1F6Vu77zJ-Zbpme7rxShGPnTpSU4Zlg');
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    
    const result = await model.generateContent('Hello, this is a test message. Please respond with "Hello! Gemini is working correctly."');
    const response = await result.response;
    const text = response.text();
    
    console.log('✅ Gemini API test successful!');
    console.log('Response:', text);
    
    return true;
  } catch (error) {
    console.error('❌ Gemini API test failed:', error.message);
    return false;
  }
}

testGemini();
