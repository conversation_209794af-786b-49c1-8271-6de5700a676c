import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { config } from '@/config/environment';
import { setupGlobalErrorHandlers } from '@/middleware/errorHandler';
import { requestLogger } from '@/middleware/requestLogger';
import { rateLimiter } from '@/middleware/rateLimiter';
import { errorHandler, notFoundHandler } from '@/middleware/errorHandler';
import { logger, logStartup } from '@/utils/logger';

// Import routes
import healthRoutes from '@/routes/health';
import configRoutes from '@/routes/config';
import chatRoutes from '@/routes/chat';
import voiceRoutes from '@/routes/voice';
import audioRoutes from '@/routes/audio';
import feedbackRoutes from '@/routes/feedback';

// Create Express app
const app = express();

// Setup global error handlers
setupGlobalErrorHandlers();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// CORS configuration
app.use(cors({
  origin: config.CORS_ORIGIN.split(',').map(origin => origin.trim()),
  credentials: config.CORS_CREDENTIALS,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Request-ID',
    'X-Request-Time',
    'X-User-Agent',
  ],
  exposedHeaders: [
    'X-Request-ID',
    'X-RateLimit-Limit',
    'X-RateLimit-Remaining',
    'X-RateLimit-Reset',
  ],
}));

// Compression middleware
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
app.use(requestLogger);

// Rate limiting (if enabled)
if (config.RATE_LIMIT_ENABLED) {
  app.use('/api', rateLimiter);
}

// Health check (before rate limiting for monitoring)
app.use('/api/health', healthRoutes);

// API routes
app.use('/api/config', configRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/voice', voiceRoutes);
app.use('/api/audio', audioRoutes);
app.use('/api/feedback', feedbackRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Voice AI Chat API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/api/health',
      config: '/api/config',
      chat: '/api/chat',
      voice: '/api/voice',
      audio: '/api/audio',
      feedback: '/api/feedback',
    },
  });
});

// API documentation (development only)
if (config.NODE_ENV === 'development') {
  app.get('/api', (req, res) => {
    res.json({
      success: true,
      message: 'Voice AI Chat API Documentation',
      version: '1.0.0',
      environment: config.NODE_ENV,
      endpoints: {
        health: {
          'GET /api/health': 'Basic health check',
          'GET /api/health/detailed': 'Detailed health check',
          'GET /api/health/ready': 'Readiness probe',
          'GET /api/health/live': 'Liveness probe',
        },
        config: {
          'GET /api/config': 'Get client configuration',
          'GET /api/config/voices': 'Get available voices',
          'GET /api/config/languages': 'Get supported languages',
          'GET /api/config/limits': 'Get API limits',
          'GET /api/config/features': 'Get feature flags',
        },
        chat: {
          'POST /api/chat/message': 'Send chat message',
          'GET /api/chat/history/:id': 'Get conversation history',
          'GET /api/chat/conversations': 'Get user conversations',
          'DELETE /api/chat/history/:id': 'Delete conversation',
          'GET /api/chat/stats': 'Get conversation statistics',
        },
        voice: {
          'POST /api/voice/transcribe': 'Transcribe audio to text',
          'POST /api/voice/synthesize': 'Synthesize text to speech',
          'POST /api/voice/process': 'Complete voice interaction',
          'GET /api/voice/voices': 'Get available voices',
        },
        audio: {
          'POST /api/audio/upload': 'Upload audio file',
          'GET /api/audio/:filename': 'Download audio file',
          'GET /api/audio/:filename/info': 'Get audio file info',
          'POST /api/audio/cleanup': 'Cleanup expired files',
        },
        feedback: {
          'POST /api/feedback': 'Submit feedback',
          'GET /api/feedback/stats': 'Get feedback statistics',
          'GET /api/feedback/recent': 'Get recent feedback',
        },
      },
    });
  });
}

// 404 handler
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Start server function
export function startServer(port: number = config.PORT): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      const server = app.listen(port, () => {
        logStartup('voice-ai-chat-api', '1.0.0', port);
        logger.info(`Server started successfully on port ${port}`);
        resolve();
      });

      server.on('error', (error: any) => {
        if (error.code === 'EADDRINUSE') {
          logger.error(`Port ${port} is already in use`);
          reject(new Error(`Port ${port} is already in use`));
        } else {
          logger.error('Server startup error:', error);
          reject(error);
        }
      });

      // Graceful shutdown
      process.on('SIGTERM', () => {
        logger.info('SIGTERM received, shutting down gracefully');
        server.close(() => {
          logger.info('Server closed');
          process.exit(0);
        });
      });

      process.on('SIGINT', () => {
        logger.info('SIGINT received, shutting down gracefully');
        server.close(() => {
          logger.info('Server closed');
          process.exit(0);
        });
      });

    } catch (error) {
      logger.error('Failed to start server:', error);
      reject(error);
    }
  });
}

export default app;
