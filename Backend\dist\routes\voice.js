"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const errorHandler_1 = require("@/middleware/errorHandler");
const validation_1 = require("@/middleware/validation");
const rateLimiter_1 = require("@/middleware/rateLimiter");
const voiceService_1 = require("@/services/voiceService");
const chatService_1 = require("@/services/chatService");
const logger_1 = require("@/utils/logger");
const environment_1 = require("@/config/environment");
const router = (0, express_1.Router)();
const voiceService = new voiceService_1.VoiceService();
const chatService = new chatService_1.ChatService();
const upload = (0, multer_1.default)({
    storage: multer_1.default.memoryStorage(),
    limits: {
        fileSize: environment_1.config.MAX_FILE_SIZE,
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = environment_1.config.ALLOWED_AUDIO_FORMATS.split(',');
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        }
        else {
            cb(new Error(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`));
        }
    },
});
router.use(rateLimiter_1.voiceRateLimiter);
router.post('/transcribe', upload.single('audio'), (0, validation_1.validate)(validation_1.voiceSchemas.transcribe), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.file) {
        return res.status(400).json({
            success: false,
            error: {
                code: 'NO_AUDIO_FILE',
                message: 'No audio file provided',
            },
            timestamp: new Date().toISOString(),
        });
    }
    const { language, model, temperature } = req.body;
    logger_1.logger.info('Processing audio transcription', {
        requestId: req.requestId,
        fileSize: req.file.size,
        mimeType: req.file.mimetype,
        language,
    });
    try {
        (0, validation_1.validateAudioFile)(req.file);
        const result = await voiceService.transcribeAudio({
            audioBuffer: req.file.buffer,
            language,
            model,
            temperature: temperature ? parseFloat(temperature) : undefined,
        });
        logger_1.logger.info('Audio transcription completed', {
            requestId: req.requestId,
            transcriptLength: result.transcript.length,
            confidence: result.confidence,
        });
        res.json({
            success: true,
            data: result,
            message: 'Audio transcribed successfully',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Audio transcription failed', {
            requestId: req.requestId,
            error: error.message,
            fileSize: req.file.size,
        });
        throw error;
    }
}));
router.post('/synthesize', (0, validation_1.validate)(validation_1.voiceSchemas.synthesize), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { text, voice, speed, model } = req.body;
    logger_1.logger.info('Processing speech synthesis', {
        requestId: req.requestId,
        textLength: text.length,
        voice,
        speed,
    });
    try {
        const result = await voiceService.synthesizeSpeech({
            text,
            voice,
            speed,
            model,
        });
        logger_1.logger.info('Speech synthesis completed', {
            requestId: req.requestId,
            audioUrl: result.audioUrl,
            audioSize: result.size,
        });
        res.json({
            success: true,
            data: result,
            message: 'Speech synthesized successfully',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Speech synthesis failed', {
            requestId: req.requestId,
            error: error.message,
            textLength: text.length,
        });
        throw error;
    }
}));
router.post('/process', upload.single('audio'), (0, validation_1.validate)(validation_1.voiceSchemas.process), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.file) {
        return res.status(400).json({
            success: false,
            error: {
                code: 'NO_AUDIO_FILE',
                message: 'No audio file provided',
            },
            timestamp: new Date().toISOString(),
        });
    }
    const { conversationId, language, voiceSettings } = req.body;
    const startTime = Date.now();
    logger_1.logger.info('Processing complete voice interaction', {
        requestId: req.requestId,
        fileSize: req.file.size,
        conversationId,
        language,
    });
    try {
        (0, validation_1.validateAudioFile)(req.file);
        logger_1.logger.debug('Step 1: Transcribing audio', { requestId: req.requestId });
        const transcriptionResult = await voiceService.transcribeAudio({
            audioBuffer: req.file.buffer,
            language,
        });
        if (!transcriptionResult.transcript.trim()) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'EMPTY_TRANSCRIPTION',
                    message: 'No speech detected in audio',
                },
                timestamp: new Date().toISOString(),
            });
        }
        logger_1.logger.debug('Step 2: Processing chat message', { requestId: req.requestId });
        const chatResult = await chatService.processMessage({
            message: transcriptionResult.transcript,
            conversationId,
            userId: req.user?.id,
        });
        logger_1.logger.debug('Step 3: Synthesizing AI response', { requestId: req.requestId });
        const synthesisResult = await voiceService.synthesizeSpeech({
            text: chatResult.response,
            voice: voiceSettings?.voice || 'alloy',
            speed: voiceSettings?.speed || 1.0,
        });
        const totalProcessingTime = Date.now() - startTime;
        const result = {
            conversationId: chatResult.conversationId,
            userMessage: {
                transcript: transcriptionResult.transcript,
                confidence: transcriptionResult.confidence,
                audioUrl: undefined,
            },
            aiResponse: {
                text: chatResult.response,
                audioUrl: synthesisResult.audioUrl,
                duration: synthesisResult.duration,
            },
            metadata: {
                processingTime: totalProcessingTime,
                totalTokens: chatResult.metadata.tokensUsed,
                transcriptionConfidence: transcriptionResult.confidence,
                steps: {
                    transcription: 'completed',
                    chat: 'completed',
                    synthesis: 'completed',
                },
            },
        };
        logger_1.logger.info('Complete voice interaction processed', {
            requestId: req.requestId,
            conversationId: result.conversationId,
            processingTime: totalProcessingTime,
            transcriptLength: transcriptionResult.transcript.length,
            responseLength: chatResult.response.length,
        });
        res.json({
            success: true,
            data: result,
            message: 'Voice interaction processed successfully',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        const processingTime = Date.now() - startTime;
        logger_1.logger.error('Voice interaction processing failed', {
            requestId: req.requestId,
            error: error.message,
            processingTime,
            fileSize: req.file.size,
        });
        throw error;
    }
}));
router.get('/voices', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const voices = [
        { id: 'alloy', name: 'Alloy', gender: 'neutral', language: 'en' },
        { id: 'echo', name: 'Echo', gender: 'male', language: 'en' },
        { id: 'fable', name: 'Fable', gender: 'neutral', language: 'en' },
        { id: 'onyx', name: 'Onyx', gender: 'male', language: 'en' },
        { id: 'nova', name: 'Nova', gender: 'female', language: 'en' },
        { id: 'shimmer', name: 'Shimmer', gender: 'female', language: 'en' },
    ];
    res.json({
        success: true,
        data: voices,
        message: 'Available voices retrieved successfully',
        timestamp: new Date().toISOString(),
    });
}));
exports.default = router;
//# sourceMappingURL=voice.js.map