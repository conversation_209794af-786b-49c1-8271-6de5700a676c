"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = void 0;
exports.logRequest = logRequest;
exports.logError = logError;
exports.logDatabase = logDatabase;
exports.logAPI = logAPI;
exports.logPerformance = logPerformance;
exports.logSecurity = logSecurity;
exports.logHealth = logHealth;
exports.logStartup = logStartup;
exports.logShutdown = logShutdown;
const winston_1 = __importDefault(require("winston"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const logsDir = path_1.default.dirname(process.env.LOG_FILE || './logs/app.log');
if (!fs_1.default.existsSync(logsDir)) {
    fs_1.default.mkdirSync(logsDir, { recursive: true });
}
const logFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS',
}), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json(), winston_1.default.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let logMessage = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    if (stack) {
        logMessage += `\n${stack}`;
    }
    if (Object.keys(meta).length > 0) {
        logMessage += `\n${JSON.stringify(meta, null, 2)}`;
    }
    return logMessage;
}));
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.timestamp({
    format: 'HH:mm:ss',
}), winston_1.default.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let logMessage = `${timestamp} ${level}: ${message}`;
    if (stack) {
        logMessage += `\n${stack}`;
    }
    if (Object.keys(meta).length > 0) {
        logMessage += `\n${JSON.stringify(meta, null, 2)}`;
    }
    return logMessage;
}));
const transports = [];
if (process.env.NODE_ENV === 'development') {
    transports.push(new winston_1.default.transports.Console({
        format: consoleFormat,
        level: process.env.LOG_LEVEL || 'debug',
    }));
}
else {
    transports.push(new winston_1.default.transports.Console({
        format: logFormat,
        level: process.env.LOG_LEVEL || 'info',
    }));
}
transports.push(new winston_1.default.transports.File({
    filename: process.env.LOG_FILE || './logs/app.log',
    format: logFormat,
    level: process.env.LOG_LEVEL || 'info',
    maxsize: 10 * 1024 * 1024,
    maxFiles: 5,
    tailable: true,
}));
transports.push(new winston_1.default.transports.File({
    filename: path_1.default.join(logsDir, 'error.log'),
    format: logFormat,
    level: 'error',
    maxsize: 10 * 1024 * 1024,
    maxFiles: 5,
    tailable: true,
}));
exports.logger = winston_1.default.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: logFormat,
    transports,
    exitOnError: false,
    silent: process.env.NODE_ENV === 'test',
});
function logRequest(req, res, responseTime) {
    const logData = {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        statusCode: res.statusCode,
        responseTime: responseTime ? `${responseTime}ms` : undefined,
        contentLength: res.get('Content-Length'),
    };
    if (res.statusCode >= 400) {
        exports.logger.warn('HTTP Request', logData);
    }
    else {
        exports.logger.info('HTTP Request', logData);
    }
}
function logError(error, context) {
    exports.logger.error('Application Error', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        context,
    });
}
function logDatabase(operation, table, duration, error) {
    const logData = {
        operation,
        table,
        duration: duration ? `${duration}ms` : undefined,
    };
    if (error) {
        exports.logger.error('Database Error', {
            ...logData,
            error: error.message,
            stack: error.stack,
        });
    }
    else {
        exports.logger.debug('Database Operation', logData);
    }
}
function logAPI(service, operation, duration, error) {
    const logData = {
        service,
        operation,
        duration: duration ? `${duration}ms` : undefined,
    };
    if (error) {
        exports.logger.error('API Error', {
            ...logData,
            error: error.message,
            stack: error.stack,
        });
    }
    else {
        exports.logger.info('API Call', logData);
    }
}
function logPerformance(operation, startTime, metadata) {
    const duration = Date.now() - startTime;
    exports.logger.info('Performance', {
        operation,
        duration: `${duration}ms`,
        ...metadata,
    });
    if (duration > 5000) {
        exports.logger.warn('Slow Operation Detected', {
            operation,
            duration: `${duration}ms`,
            ...metadata,
        });
    }
}
function logSecurity(event, details, severity = 'warn') {
    exports.logger[severity]('Security Event', {
        event,
        timestamp: new Date().toISOString(),
        ...details,
    });
}
function logHealth(service, status, details) {
    const logLevel = status === 'healthy' ? 'info' : 'error';
    exports.logger[logLevel]('Health Check', {
        service,
        status,
        timestamp: new Date().toISOString(),
        ...details,
    });
}
function logStartup(service, version, port) {
    exports.logger.info('Service Started', {
        service,
        version,
        port,
        environment: process.env.NODE_ENV,
        nodeVersion: process.version,
        timestamp: new Date().toISOString(),
    });
}
function logShutdown(service, reason) {
    exports.logger.info('Service Shutdown', {
        service,
        reason,
        timestamp: new Date().toISOString(),
    });
}
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map