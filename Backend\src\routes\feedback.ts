import { Router } from 'express';
import { as<PERSON><PERSON><PERSON><PERSON> } from '@/middleware/errorHandler';
import { validate, feedbackSchemas } from '@/middleware/validation';
import { rateLimiter } from '@/middleware/rateLimiter';
import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';

const router = Router();

// Apply rate limiting
router.use(rateLimiter);

// Submit feedback
router.post('/',
  validate(feedbackSchemas.submit),
  asyncHandler(async (req, res) => {
    const {
      type,
      message,
      rating,
      conversationId,
      userAgent,
      metadata,
    } = req.body;

    logger.info('Processing feedback submission', {
      requestId: req.requestId,
      type,
      messageLength: message.length,
      rating,
      conversationId,
    });

    try {
      // Create feedback record
      const feedback = await prisma.feedback.create({
        data: {
          type,
          message,
          rating,
          conversationId,
          userAgent: userAgent || req.get('User-Agent'),
          ipAddress: req.ip,
          metadata: JSON.stringify({
            ...metadata,
            submittedAt: new Date().toISOString(),
            requestId: req.requestId,
          }),
        },
      });

      logger.info('Feedback submitted successfully', {
        requestId: req.requestId,
        feedbackId: feedback.id,
        type,
        rating,
      });

      res.status(201).json({
        success: true,
        data: {
          id: feedback.id,
          type: feedback.type,
          submittedAt: feedback.createdAt,
        },
        message: 'Feedback submitted successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error: any) {
      logger.error('Failed to submit feedback', {
        requestId: req.requestId,
        error: error.message,
        type,
        messageLength: message.length,
      });
      throw error;
    }
  })
);

// Get feedback statistics (admin endpoint)
router.get('/stats',
  asyncHandler(async (req, res) => {
    logger.debug('Retrieving feedback statistics', {
      requestId: req.requestId,
    });

    try {
      const [
        totalFeedback,
        feedbackByType,
        averageRating,
        recentFeedback,
      ] = await Promise.all([
        // Total feedback count
        prisma.feedback.count(),
        
        // Feedback by type
        prisma.feedback.groupBy({
          by: ['type'],
          _count: {
            id: true,
          },
        }),
        
        // Average rating
        prisma.feedback.aggregate({
          _avg: {
            rating: true,
          },
          where: {
            rating: {
              not: null,
            },
          },
        }),
        
        // Recent feedback (last 7 days)
        prisma.feedback.count({
          where: {
            createdAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            },
          },
        }),
      ]);

      const stats = {
        total: totalFeedback,
        byType: feedbackByType.reduce((acc, item) => {
          acc[item.type] = item._count.id;
          return acc;
        }, {} as Record<string, number>),
        averageRating: averageRating._avg.rating ? 
          Math.round(averageRating._avg.rating * 100) / 100 : null,
        recentCount: recentFeedback,
      };

      logger.debug('Feedback statistics retrieved', {
        requestId: req.requestId,
        total: stats.total,
        averageRating: stats.averageRating,
      });

      res.json({
        success: true,
        data: stats,
        message: 'Feedback statistics retrieved successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error: any) {
      logger.error('Failed to retrieve feedback statistics', {
        requestId: req.requestId,
        error: error.message,
      });
      throw error;
    }
  })
);

// Get recent feedback (admin endpoint)
router.get('/recent',
  asyncHandler(async (req, res) => {
    const { limit = 10, offset = 0, type } = req.query;

    logger.debug('Retrieving recent feedback', {
      requestId: req.requestId,
      limit,
      offset,
      type,
    });

    try {
      const where: any = {};
      if (type) {
        where.type = type;
      }

      const feedback = await prisma.feedback.findMany({
        where,
        orderBy: {
          createdAt: 'desc',
        },
        skip: Number(offset),
        take: Number(limit),
        select: {
          id: true,
          type: true,
          message: true,
          rating: true,
          conversationId: true,
          createdAt: true,
          // Exclude sensitive data like IP address
        },
      });

      logger.debug('Recent feedback retrieved', {
        requestId: req.requestId,
        count: feedback.length,
        type,
      });

      res.json({
        success: true,
        data: feedback,
        message: 'Recent feedback retrieved successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error: any) {
      logger.error('Failed to retrieve recent feedback', {
        requestId: req.requestId,
        error: error.message,
      });
      throw error;
    }
  })
);

export default router;
