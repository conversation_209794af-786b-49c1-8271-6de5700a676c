"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.genAI = exports.AVAILABLE_VOICES = exports.GEMINI_CONFIG = void 0;
exports.getGeminiModel = getGeminiModel;
exports.generateChatCompletion = generateChatCompletion;
exports.handleGeminiError = handleGeminiError;
exports.testGeminiConnection = testGeminiConnection;
exports.transcribeAudio = transcribeAudio;
exports.generateSpeech = generateSpeech;
const generative_ai_1 = require("@google/generative-ai");
const logger_1 = require("@/utils/logger");
const environment_1 = require("@/config/environment");
const genAI = new generative_ai_1.GoogleGenerativeAI(environment_1.config.GOOGLE_API_KEY);
exports.genAI = genAI;
exports.GEMINI_CONFIG = {
    apiKey: environment_1.config.GOOGLE_API_KEY,
    model: environment_1.config.MODEL_NAME,
    maxTokens: environment_1.config.GEMINI_MAX_TOKENS,
    temperature: environment_1.config.GEMINI_TEMPERATURE,
    systemPrompt: `You are a helpful AI assistant in a voice-enabled chat application. 
You should provide clear, concise, and helpful responses. Keep your responses conversational 
and appropriate for voice interaction. Avoid overly long responses unless specifically requested.
Be friendly, professional, and engaging in your communication style.`,
};
exports.AVAILABLE_VOICES = [
    { id: 'alloy', name: 'Alloy', language: 'en', gender: 'neutral' },
    { id: 'echo', name: 'Echo', language: 'en', gender: 'male' },
    { id: 'fable', name: 'Fable', language: 'en', gender: 'neutral' },
    { id: 'onyx', name: 'Onyx', language: 'en', gender: 'male' },
    { id: 'nova', name: 'Nova', language: 'en', gender: 'female' },
    { id: 'shimmer', name: 'Shimmer', language: 'en', gender: 'female' },
];
function getGeminiModel() {
    return genAI.getGenerativeModel({ model: exports.GEMINI_CONFIG.model });
}
async function generateChatCompletion(messages, options = {}) {
    const { maxTokens = exports.GEMINI_CONFIG.maxTokens, temperature = exports.GEMINI_CONFIG.temperature, } = options;
    try {
        const model = getGeminiModel();
        const geminiMessages = convertMessagesToGeminiFormat(messages);
        const result = await model.generateContent({
            contents: geminiMessages,
            generationConfig: {
                maxOutputTokens: maxTokens,
                temperature: temperature,
            },
        });
        const response = await result.response;
        const text = response.text();
        return {
            id: `gemini-${Date.now()}`,
            object: 'chat.completion',
            created: Math.floor(Date.now() / 1000),
            model: exports.GEMINI_CONFIG.model,
            choices: [
                {
                    index: 0,
                    message: {
                        role: 'assistant',
                        content: text,
                    },
                    finish_reason: 'stop',
                },
            ],
            usage: {
                prompt_tokens: 0,
                completion_tokens: 0,
                total_tokens: 0,
            },
        };
    }
    catch (error) {
        logger_1.logger.error('Gemini API error:', {
            error: error.message,
            stack: error.stack,
        });
        throw handleGeminiError(error);
    }
}
function convertMessagesToGeminiFormat(messages) {
    const geminiMessages = [];
    let systemPrompt = exports.GEMINI_CONFIG.systemPrompt;
    const systemMessage = messages.find(msg => msg.role === 'system');
    if (systemMessage) {
        systemPrompt = systemMessage.content;
    }
    const conversationMessages = messages.filter(msg => msg.role !== 'system');
    for (const message of conversationMessages) {
        geminiMessages.push({
            role: message.role === 'assistant' ? 'model' : 'user',
            parts: [{ text: message.content }],
        });
    }
    if (geminiMessages.length === 0 || geminiMessages[0].role === 'model') {
        geminiMessages.unshift({
            role: 'user',
            parts: [{ text: systemPrompt }],
        });
        geminiMessages.push({
            role: 'model',
            parts: [{ text: 'I understand. I\'ll be a helpful AI assistant.' }],
        });
    }
    return geminiMessages;
}
function handleGeminiError(error) {
    const errorResponse = {
        error: {
            type: 'api_error',
            message: error.message || 'Unknown Gemini API error',
            code: error.code || 'unknown_error',
        },
        statusCode: 500,
    };
    if (error.message?.includes('API key')) {
        errorResponse.error.type = 'authentication_error';
        errorResponse.statusCode = 401;
    }
    else if (error.message?.includes('quota') || error.message?.includes('rate limit')) {
        errorResponse.error.type = 'rate_limit_error';
        errorResponse.statusCode = 429;
    }
    else if (error.message?.includes('safety')) {
        errorResponse.error.type = 'safety_error';
        errorResponse.statusCode = 400;
    }
    return errorResponse;
}
async function testGeminiConnection() {
    try {
        const model = getGeminiModel();
        const result = await model.generateContent('Hello, this is a test.');
        const response = await result.response;
        const text = response.text();
        logger_1.logger.info('Gemini connection test successful', {
            responseLength: text.length,
        });
        return true;
    }
    catch (error) {
        logger_1.logger.error('Gemini connection test failed:', {
            error: error.message,
        });
        return false;
    }
}
async function transcribeAudio(audioBuffer, options = {}) {
    logger_1.logger.warn('Audio transcription not implemented - using placeholder');
    return { text: 'Audio transcription not yet implemented with Gemini' };
}
async function generateSpeech(text, options = {}) {
    logger_1.logger.warn('Speech synthesis not implemented - using placeholder');
    return Buffer.from('Speech synthesis not yet implemented');
}
//# sourceMappingURL=gemini.js.map