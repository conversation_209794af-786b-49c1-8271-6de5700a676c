import { Router } from 'express';
import { async<PERSON>and<PERSON> } from '@/middleware/errorHandler';
import { checkDatabaseConnection } from '@/config/database';
import { testOpenAIConnection } from '@/config/openai';
import { logger, logHealth } from '@/utils/logger';
import { HealthStatus } from '@/types';

const router = Router();

// Health check endpoint
router.get('/', asyncHandler(async (req, res) => {
  const startTime = Date.now();
  
  // Check database connection
  const databaseStatus = await checkDatabaseConnection();
  
  // Check OpenAI connection
  const openaiStatus = await testOpenAIConnection();
  
  // Get system information
  const memoryUsage = process.memoryUsage();
  const uptime = process.uptime();
  
  // Calculate overall health status
  let overallStatus: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';
  
  if (!databaseStatus || !openaiStatus) {
    overallStatus = 'unhealthy';
  } else if (!databaseStatus || !openaiStatus) {
    overallStatus = 'degraded';
  }
  
  const healthStatus: HealthStatus = {
    status: overallStatus,
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    services: {
      database: databaseStatus ? 'connected' : 'error',
      openai: openaiStatus ? 'available' : 'error',
      speechService: 'available', // Web Speech API is browser-based
    },
    uptime: Math.floor(uptime),
    memory: {
      used: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
      total: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
      percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100),
    },
  };
  
  // Log health check
  logHealth('api-server', overallStatus, {
    responseTime: Date.now() - startTime,
    services: healthStatus.services,
  });
  
  // Set appropriate status code
  const statusCode = overallStatus === 'healthy' ? 200 : 
                    overallStatus === 'degraded' ? 200 : 503;
  
  res.status(statusCode).json({
    success: overallStatus !== 'unhealthy',
    data: healthStatus,
    message: `Service is ${overallStatus}`,
    timestamp: new Date().toISOString(),
  });
}));

// Detailed health check endpoint
router.get('/detailed', asyncHandler(async (req, res) => {
  const startTime = Date.now();
  
  // Perform comprehensive health checks
  const checks = await Promise.allSettled([
    checkDatabaseConnection(),
    testOpenAIConnection(),
    checkDiskSpace(),
    checkMemoryUsage(),
    checkEnvironmentVariables(),
  ]);
  
  const [
    databaseResult,
    openaiResult,
    diskResult,
    memoryResult,
    envResult,
  ] = checks;
  
  const healthDetails = {
    status: 'healthy' as 'healthy' | 'unhealthy' | 'degraded',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV,
    uptime: Math.floor(process.uptime()),
    responseTime: Date.now() - startTime,
    checks: {
      database: {
        status: databaseResult.status === 'fulfilled' && databaseResult.value ? 'pass' : 'fail',
        message: databaseResult.status === 'fulfilled' ? 'Database connection successful' : 'Database connection failed',
        error: databaseResult.status === 'rejected' ? databaseResult.reason?.message : undefined,
      },
      openai: {
        status: openaiResult.status === 'fulfilled' && openaiResult.value ? 'pass' : 'fail',
        message: openaiResult.status === 'fulfilled' ? 'OpenAI API connection successful' : 'OpenAI API connection failed',
        error: openaiResult.status === 'rejected' ? openaiResult.reason?.message : undefined,
      },
      disk: {
        status: diskResult.status === 'fulfilled' && diskResult.value ? 'pass' : 'warn',
        message: diskResult.status === 'fulfilled' ? 'Disk space sufficient' : 'Disk space check failed',
        details: diskResult.status === 'fulfilled' ? diskResult.value : undefined,
      },
      memory: {
        status: memoryResult.status === 'fulfilled' && memoryResult.value ? 'pass' : 'warn',
        message: memoryResult.status === 'fulfilled' ? 'Memory usage normal' : 'Memory usage high',
        details: memoryResult.status === 'fulfilled' ? memoryResult.value : undefined,
      },
      environment: {
        status: envResult.status === 'fulfilled' && envResult.value ? 'pass' : 'fail',
        message: envResult.status === 'fulfilled' ? 'Environment variables configured' : 'Environment variables missing',
        details: envResult.status === 'fulfilled' ? envResult.value : undefined,
      },
    },
  };
  
  // Determine overall status
  const failedChecks = Object.values(healthDetails.checks).filter(check => check.status === 'fail');
  const warnChecks = Object.values(healthDetails.checks).filter(check => check.status === 'warn');
  
  if (failedChecks.length > 0) {
    healthDetails.status = 'unhealthy';
  } else if (warnChecks.length > 0) {
    healthDetails.status = 'degraded';
  }
  
  const statusCode = healthDetails.status === 'healthy' ? 200 : 
                    healthDetails.status === 'degraded' ? 200 : 503;
  
  res.status(statusCode).json({
    success: healthDetails.status !== 'unhealthy',
    data: healthDetails,
    message: `Detailed health check completed - ${healthDetails.status}`,
    timestamp: new Date().toISOString(),
  });
}));

// Readiness probe (for Kubernetes)
router.get('/ready', asyncHandler(async (req, res) => {
  const databaseReady = await checkDatabaseConnection();
  const openaiReady = await testOpenAIConnection();
  
  const isReady = databaseReady && openaiReady;
  
  res.status(isReady ? 200 : 503).json({
    success: isReady,
    data: {
      ready: isReady,
      services: {
        database: databaseReady,
        openai: openaiReady,
      },
    },
    message: isReady ? 'Service is ready' : 'Service is not ready',
    timestamp: new Date().toISOString(),
  });
}));

// Liveness probe (for Kubernetes)
router.get('/live', asyncHandler(async (req, res) => {
  // Simple liveness check - if we can respond, we're alive
  res.status(200).json({
    success: true,
    data: {
      alive: true,
      uptime: Math.floor(process.uptime()),
      timestamp: new Date().toISOString(),
    },
    message: 'Service is alive',
    timestamp: new Date().toISOString(),
  });
}));

// Helper functions
async function checkDiskSpace(): Promise<any> {
  try {
    const fs = require('fs');
    const stats = fs.statSync('.');
    
    return {
      available: true,
      message: 'Disk space check not implemented',
    };
  } catch (error) {
    return {
      available: false,
      error: error.message,
    };
  }
}

async function checkMemoryUsage(): Promise<any> {
  const memoryUsage = process.memoryUsage();
  const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
  
  return {
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
    percentage: Math.round(memoryUsagePercent),
    status: memoryUsagePercent < 90 ? 'normal' : 'high',
  };
}

async function checkEnvironmentVariables(): Promise<any> {
  const requiredVars = [
    'DATABASE_URL',
    'OPENAI_API_KEY',
    'NODE_ENV',
    'PORT',
  ];
  
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  return {
    allPresent: missing.length === 0,
    missing,
    total: requiredVars.length,
    present: requiredVars.length - missing.length,
  };
}

export default router;
