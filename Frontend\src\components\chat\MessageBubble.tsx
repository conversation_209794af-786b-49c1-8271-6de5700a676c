import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { clsx } from 'clsx';
import { 
  Play, 
  Pause, 
  Volume2, 
  Copy, 
  Check, 
  User, 
  Bot,
  Clock,
  Zap
} from 'lucide-react';
import { MessageBubbleProps } from '@/types';
import { formatDistanceToNow } from 'date-fns';

export function MessageBubble({
  message,
  onPlayAudio,
  onCopyText,
  showTimestamp = true,
  showConfidence = true,
  className,
}: MessageBubbleProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [showCopied, setShowCopied] = useState(false);
  const [audioError, setAudioError] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';

  // Handle audio playback
  const handlePlayAudio = async () => {
    if (!message.audioUrl) return;

    try {
      if (isPlaying) {
        // Pause current audio
        if (audioRef.current) {
          audioRef.current.pause();
        }
        setIsPlaying(false);
      } else {
        // Play audio
        if (onPlayAudio) {
          onPlayAudio(message.audioUrl);
        } else {
          // Fallback to local audio element
          if (audioRef.current) {
            audioRef.current.pause();
          }
          
          const audio = new Audio(message.audioUrl);
          audioRef.current = audio;
          
          audio.onplay = () => setIsPlaying(true);
          audio.onended = () => setIsPlaying(false);
          audio.onerror = () => {
            setAudioError(true);
            setIsPlaying(false);
          };
          
          await audio.play();
        }
      }
    } catch (error) {
      console.error('Failed to play audio:', error);
      setAudioError(true);
      setIsPlaying(false);
    }
  };

  // Handle copy text
  const handleCopyText = async () => {
    if (!message.content) return;

    try {
      await navigator.clipboard.writeText(message.content);
      setShowCopied(true);
      setTimeout(() => setShowCopied(false), 2000);
      
      if (onCopyText) {
        onCopyText(message.content);
      }
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch (error) {
      return 'Unknown time';
    }
  };

  // Get message styling
  const getMessageClasses = () => {
    const baseClasses = 'max-w-xs lg:max-w-md px-4 py-2 rounded-2xl shadow-sm';
    
    if (isUser) {
      return clsx(
        baseClasses,
        'bg-primary-600 text-white rounded-br-md ml-auto',
        'shadow-primary-600/20'
      );
    }
    
    if (isAssistant) {
      return clsx(
        baseClasses,
        'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-bl-md mr-auto',
        'border border-gray-200 dark:border-gray-700'
      );
    }
    
    return clsx(
      baseClasses,
      'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 mx-auto'
    );
  };

  return (
    <motion.div
      className={clsx('flex flex-col space-y-1', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Message header with avatar and metadata */}
      <div className={clsx('flex items-center space-x-2', isUser ? 'justify-end' : 'justify-start')}>
        {/* Avatar */}
        {!isUser && (
          <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
            <Bot className="w-4 h-4 text-primary-600 dark:text-primary-400" />
          </div>
        )}
        
        {/* Metadata */}
        <div className={clsx('flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400')}>
          {isUser && <User className="w-3 h-3" />}
          <span className="font-medium">
            {isUser ? 'You' : 'AI Assistant'}
          </span>
          
          {/* Confidence indicator for transcribed messages */}
          {isUser && message.transcriptConfidence && showConfidence && (
            <div className="flex items-center space-x-1">
              <Zap className="w-3 h-3" />
              <span className={clsx(
                message.transcriptConfidence >= 0.8 ? 'text-green-600 dark:text-green-400' :
                message.transcriptConfidence >= 0.6 ? 'text-yellow-600 dark:text-yellow-400' :
                'text-red-600 dark:text-red-400'
              )}>
                {Math.round(message.transcriptConfidence * 100)}%
              </span>
            </div>
          )}
          
          {/* Response time for AI messages */}
          {isAssistant && message.responseTime && (
            <div className="flex items-center space-x-1">
              <Clock className="w-3 h-3" />
              <span>{message.responseTime.toFixed(1)}s</span>
            </div>
          )}
        </div>
        
        {isUser && (
          <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-600 flex items-center justify-center">
            <User className="w-4 h-4 text-white" />
          </div>
        )}
      </div>

      {/* Message bubble */}
      <div className={clsx('flex', isUser ? 'justify-end' : 'justify-start')}>
        <div className={getMessageClasses()}>
          {/* Message content */}
          <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
            {message.content}
          </p>
          
          {/* Audio controls and actions */}
          <div className="flex items-center justify-between mt-2 space-x-2">
            {/* Audio controls */}
            {message.audioUrl && (
              <button
                onClick={handlePlayAudio}
                disabled={audioError}
                className={clsx(
                  'flex items-center space-x-1 px-2 py-1 rounded text-xs transition-colors',
                  isUser 
                    ? 'bg-primary-700 hover:bg-primary-800 text-white' 
                    : 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300',
                  audioError && 'opacity-50 cursor-not-allowed'
                )}
                title={audioError ? 'Audio unavailable' : isPlaying ? 'Pause audio' : 'Play audio'}
              >
                {isPlaying ? (
                  <Pause className="w-3 h-3" />
                ) : (
                  <Play className="w-3 h-3" />
                )}
                <Volume2 className="w-3 h-3" />
                {message.audioDuration && (
                  <span>{Math.round(message.audioDuration)}s</span>
                )}
              </button>
            )}
            
            {/* Copy button */}
            <button
              onClick={handleCopyText}
              className={clsx(
                'p-1 rounded transition-colors',
                isUser 
                  ? 'text-primary-200 hover:text-white hover:bg-primary-700' 
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
              )}
              title="Copy message"
            >
              <AnimatePresence mode="wait">
                {showCopied ? (
                  <motion.div
                    key="check"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                  >
                    <Check className="w-3 h-3" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="copy"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                  >
                    <Copy className="w-3 h-3" />
                  </motion.div>
                )}
              </AnimatePresence>
            </button>
          </div>
        </div>
      </div>

      {/* Timestamp */}
      {showTimestamp && (
        <div className={clsx('text-xs text-gray-400 dark:text-gray-500 px-1', isUser ? 'text-right' : 'text-left')}>
          {formatTimestamp(message.createdAt)}
        </div>
      )}
    </motion.div>
  );
}

// Typing indicator component
export function TypingIndicator({ className }: { className?: string }) {
  return (
    <motion.div
      className={clsx('flex justify-start', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      <div className="max-w-xs px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl rounded-bl-md shadow-sm">
        <div className="flex items-center space-x-2">
          <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
            <Bot className="w-4 h-4 text-primary-600 dark:text-primary-400" />
          </div>
          <div className="flex space-x-1">
            {[0, 1, 2].map((index) => (
              <motion.div
                key={index}
                className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: index * 0.2,
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </motion.div>
  );
}

export default MessageBubble;
