"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/simple",{

/***/ "./src/pages/simple.tsx":
/*!******************************!*\
  !*** ./src/pages/simple.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SimplePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\nfunction SimplePage() {\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [response, setResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [backendStatus, setBackendStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"checking...\");\n    // Check backend status on load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkBackendStatus();\n    }, []);\n    const checkBackendStatus = async ()=>{\n        try {\n            const res = await fetch(\"http://localhost:8000/api/health\");\n            if (res.ok) {\n                const data = await res.json();\n                setBackendStatus(\"✅ Connected (\".concat(data.data.status, \")\"));\n            } else {\n                setBackendStatus(\"❌ Backend Error\");\n            }\n        } catch (error) {\n            setBackendStatus(\"❌ Backend Offline\");\n        }\n    };\n    const sendMessage = async ()=>{\n        if (!message.trim()) return;\n        setLoading(true);\n        try {\n            const res = await fetch(\"http://localhost:8000/api/chat/message\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message\n                })\n            });\n            if (res.ok) {\n                const data = await res.json();\n                setResponse(data.data.response);\n            } else {\n                setResponse(\"Error: Failed to get response\");\n            }\n        } catch (error) {\n            setResponse(\"Error: Cannot connect to backend\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"Simple Voice AI Chat Test\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-100 p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-8\",\n                            children: \"Voice AI Chat - Simple Test\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: \"Backend Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: backendStatus\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: checkBackendStatus,\n                                    className: \"mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                    children: \"Refresh Status\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Test Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Your Message:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: message,\n                                                    onChange: (e)=>setMessage(e.target.value),\n                                                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-black\",\n                                                    rows: 3,\n                                                    placeholder: \"Type your message here...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: sendMessage,\n                                            disabled: loading || !message.trim(),\n                                            className: \"w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed\",\n                                            children: loading ? \"Sending...\" : \"Send Message\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        response && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"AI Response:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-gray-50 border border-gray-200 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-800\",\n                                                        children: response\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/\",\n                                className: \"inline-block px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600\",\n                                children: \"Go to Full Voice AI Chat\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\simple.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(SimplePage, \"xPijHnVAEqqG5HHjUgilmU5pB4E=\");\n_c = SimplePage;\nvar _c;\n$RefreshReg$(_c, \"SimplePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/simple.tsx\n"));

/***/ })

});