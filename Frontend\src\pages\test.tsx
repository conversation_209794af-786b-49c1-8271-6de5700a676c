import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { motion } from 'framer-motion';
import { CheckCircle, XCircle, Loader2, Mic, Volume2, MessageSquare } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message?: string;
}

export default function TestPage() {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Backend Connection', status: 'pending' },
    { name: 'API Health Check', status: 'pending' },
    { name: 'Chat API', status: 'pending' },
    { name: 'Voice API', status: 'pending' },
    { name: 'Microphone Permission', status: 'pending' },
  ]);

  const updateTest = (name: string, status: 'success' | 'error', message?: string) => {
    setTests(prev => prev.map(test => 
      test.name === name ? { ...test, status, message } : test
    ));
  };

  const runTests = async () => {
    try {
      // Test 1: Backend Connection
      const healthResponse = await fetch('http://localhost:8000/api/health');
      if (healthResponse.ok) {
        updateTest('Backend Connection', 'success', 'Connected to backend');
        
        const healthData = await healthResponse.json();
        updateTest('API Health Check', 'success', `Status: ${healthData.status}`);
      } else {
        updateTest('Backend Connection', 'error', 'Failed to connect to backend');
        updateTest('API Health Check', 'error', 'Backend not available');
      }

      // Test 2: Chat API
      try {
        const chatResponse = await fetch('http://localhost:8000/api/chat/message', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ message: 'Test message from frontend' })
        });
        
        if (chatResponse.ok) {
          const chatData = await chatResponse.json();
          updateTest('Chat API', 'success', `Response: ${chatData.data.response.substring(0, 50)}...`);
        } else {
          updateTest('Chat API', 'error', 'Chat API failed');
        }
      } catch (error) {
        updateTest('Chat API', 'error', 'Chat API error');
      }

      // Test 3: Voice API
      try {
        const voiceResponse = await fetch('http://localhost:8000/api/voice/voices');
        if (voiceResponse.ok) {
          const voiceData = await voiceResponse.json();
          updateTest('Voice API', 'success', `${voiceData.data.length} voices available`);
        } else {
          updateTest('Voice API', 'error', 'Voice API failed');
        }
      } catch (error) {
        updateTest('Voice API', 'error', 'Voice API error');
      }

      // Test 4: Microphone Permission
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        stream.getTracks().forEach(track => track.stop());
        updateTest('Microphone Permission', 'success', 'Microphone access granted');
      } catch (error) {
        updateTest('Microphone Permission', 'error', 'Microphone access denied');
      }

    } catch (error) {
      console.error('Test error:', error);
    }
  };

  useEffect(() => {
    runTests();
  }, []);

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Loader2 className="w-5 h-5 animate-spin text-yellow-500" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
    }
  };

  const allTestsPassed = tests.every(test => test.status === 'success');
  const hasErrors = tests.some(test => test.status === 'error');

  return (
    <>
      <Head>
        <title>Voice AI Chat - System Test</title>
        <meta name="description" content="System test page for Voice AI Chat" />
      </Head>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Voice AI Chat - System Test
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Testing all system components and connections
            </p>
          </div>

          {/* Overall Status */}
          <div className="mb-8">
            <motion.div
              className={`p-6 rounded-lg border-2 ${
                allTestsPassed 
                  ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                  : hasErrors
                  ? 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
                  : 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800'
              }`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <div className="flex items-center space-x-3">
                {allTestsPassed ? (
                  <CheckCircle className="w-8 h-8 text-green-500" />
                ) : hasErrors ? (
                  <XCircle className="w-8 h-8 text-red-500" />
                ) : (
                  <Loader2 className="w-8 h-8 text-yellow-500 animate-spin" />
                )}
                <div>
                  <h2 className="text-xl font-semibold">
                    {allTestsPassed 
                      ? 'All Tests Passed! ✅'
                      : hasErrors
                      ? 'Some Tests Failed ❌'
                      : 'Running Tests... ⏳'
                    }
                  </h2>
                  <p className="text-sm opacity-75">
                    {allTestsPassed 
                      ? 'Voice AI Chat is ready to use!'
                      : hasErrors
                      ? 'Please check the failed tests below'
                      : 'Please wait while we test all components'
                    }
                  </p>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Test Results */}
          <div className="space-y-4">
            {tests.map((test, index) => (
              <motion.div
                key={test.name}
                className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(test.status)}
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-gray-100">
                        {test.name}
                      </h3>
                      {test.message && (
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {test.message}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="text-sm font-medium">
                    {test.status === 'success' && (
                      <span className="text-green-600 dark:text-green-400">PASS</span>
                    )}
                    {test.status === 'error' && (
                      <span className="text-red-600 dark:text-red-400">FAIL</span>
                    )}
                    {test.status === 'pending' && (
                      <span className="text-yellow-600 dark:text-yellow-400">TESTING</span>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={runTests}
              className="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
            >
              <Loader2 className="w-5 h-5" />
              <span>Run Tests Again</span>
            </button>
            
            {allTestsPassed && (
              <a
                href="/"
                className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
              >
                <MessageSquare className="w-5 h-5" />
                <span>Go to Voice Chat</span>
              </a>
            )}
          </div>

          {/* System Information */}
          <div className="mt-12 bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              System Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Frontend:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">http://localhost:3000</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Backend:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">http://localhost:8000</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Framework:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">Next.js + React</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">API:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">Node.js + Express</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">AI Model:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">Gemini (Mock Mode)</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Voice:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">Web Speech API</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
