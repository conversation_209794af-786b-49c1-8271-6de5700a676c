@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

/* Base styles */
@layer base {
  html {
    @apply scroll-smooth;
  }
  
  body {
    @apply bg-white dark:bg-secondary-900 text-secondary-900 dark:text-secondary-100 font-sans antialiased;
    @apply transition-colors duration-300;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-secondary-100 dark:bg-secondary-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-secondary-300 dark:bg-secondary-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary-400 dark:bg-secondary-500;
  }
  
  /* Focus styles */
  *:focus {
    @apply outline-none;
  }
  
  *:focus-visible {
    @apply ring-2 ring-primary-500 ring-offset-2 ring-offset-white dark:ring-offset-secondary-900;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-primary-100 dark:bg-primary-900 text-primary-900 dark:text-primary-100;
  }
  
  /* Headings */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-secondary-900 dark:text-secondary-100;
  }
  
  /* Links */
  a {
    @apply text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors duration-200;
  }
  
  /* Form elements */
  input, textarea, select {
    @apply transition-colors duration-200;
  }
  
  /* Buttons */
  button {
    @apply transition-all duration-200;
  }
  
  button:disabled {
    @apply opacity-50 cursor-not-allowed;
  }
}

/* Component styles */
@layer components {
  /* Loading spinner */
  .spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-r-transparent rounded-full animate-spin;
  }
  
  /* Pulse animation for recording */
  .pulse-recording {
    animation: pulse-recording 1.5s ease-in-out infinite;
  }
  
  @keyframes pulse-recording {
    0%, 100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 0.8;
    }
  }
  
  /* Wave animation for audio visualization */
  .wave-bar {
    @apply bg-primary-500 rounded-full transition-all duration-150 ease-out;
    animation: wave 1s ease-in-out infinite;
  }
  
  .wave-bar:nth-child(2) { animation-delay: 0.1s; }
  .wave-bar:nth-child(3) { animation-delay: 0.2s; }
  .wave-bar:nth-child(4) { animation-delay: 0.3s; }
  .wave-bar:nth-child(5) { animation-delay: 0.4s; }
  
  /* Gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, theme('colors.primary.500'), theme('colors.primary.600'));
  }
  
  .gradient-secondary {
    background: linear-gradient(135deg, theme('colors.secondary.100'), theme('colors.secondary.200'));
  }
  
  .gradient-accent {
    background: linear-gradient(135deg, theme('colors.accent.500'), theme('colors.accent.600'));
  }
  
  /* Glass morphism effect */
  .glass {
    @apply bg-white/10 dark:bg-black/10 backdrop-blur-md border border-white/20 dark:border-white/10;
  }
  
  /* Chat message styles */
  .message-user {
    @apply bg-primary-600 text-white rounded-2xl rounded-br-md px-4 py-2 max-w-xs ml-auto;
  }
  
  .message-assistant {
    @apply bg-secondary-100 dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100 rounded-2xl rounded-bl-md px-4 py-2 max-w-xs mr-auto;
  }
  
  /* Audio controls */
  .audio-control {
    @apply p-2 rounded-full bg-secondary-100 dark:bg-secondary-800 hover:bg-secondary-200 dark:hover:bg-secondary-700 transition-colors duration-200;
  }
  
  /* Settings panel */
  .settings-section {
    @apply space-y-4 p-6 border-b border-secondary-200 dark:border-secondary-700 last:border-b-0;
  }
  
  .settings-item {
    @apply flex items-center justify-between py-2;
  }
  
  /* Toast notifications */
  .toast-success {
    @apply bg-success-50 dark:bg-success-900 border border-success-200 dark:border-success-700 text-success-800 dark:text-success-200;
  }
  
  .toast-error {
    @apply bg-error-50 dark:bg-error-900 border border-error-200 dark:border-error-700 text-error-800 dark:text-error-200;
  }
  
  .toast-warning {
    @apply bg-warning-50 dark:bg-warning-900 border border-warning-200 dark:border-warning-700 text-warning-800 dark:text-warning-200;
  }
  
  .toast-info {
    @apply bg-primary-50 dark:bg-primary-900 border border-primary-200 dark:border-primary-700 text-primary-800 dark:text-primary-200;
  }
}

/* Utility styles */
@layer utilities {
  /* Text utilities */
  .text-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
  
  /* Layout utilities */
  .safe-area-inset-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-inset-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  /* Responsive utilities */
  .container-narrow {
    @apply max-w-2xl mx-auto px-4;
  }
  
  .container-wide {
    @apply max-w-7xl mx-auto px-4;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .high-contrast {
    @apply contrast-125;
  }
}

/* Print styles */
@media print {
  .no-print {
    @apply hidden;
  }
  
  .print-only {
    @apply block;
  }
  
  * {
    @apply text-black bg-white;
  }
}
