const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

console.log('🧪 Testing Real Gemini API...');
console.log(`🔑 API Key: ${process.env.GOOGLE_API_KEY ? process.env.GOOGLE_API_KEY.substring(0, 20) + '...' : 'Not found'}`);
console.log(`🤖 Model: ${process.env.MODEL_NAME || 'gemini-2.5-flash'}`);

async function testGeminiAPI() {
  try {
    // Initialize Gemini AI
    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
    const model = genAI.getGenerativeModel({ 
      model: process.env.MODEL_NAME || 'gemini-2.5-flash' 
    });

    console.log('\n📤 Sending test message to Gemini...');
    const prompt = "Hello! Please respond with a brief greeting and confirm that you're working correctly.";
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    console.log('\n✅ Gemini API Test Successful!');
    console.log(`📝 Response: ${text}`);
    console.log(`🔢 Tokens Used: ${response.usageMetadata?.totalTokenCount || 'N/A'}`);
    console.log(`📊 Prompt Tokens: ${response.usageMetadata?.promptTokenCount || 'N/A'}`);
    console.log(`📊 Candidate Tokens: ${response.usageMetadata?.candidatesTokenCount || 'N/A'}`);
    
    return true;
  } catch (error) {
    console.error('\n❌ Gemini API Test Failed:');
    console.error(`Error: ${error.message}`);
    
    if (error.status) {
      console.error(`Status: ${error.status} ${error.statusText}`);
    }
    
    if (error.errorDetails) {
      console.error('Error Details:', JSON.stringify(error.errorDetails, null, 2));
    }
    
    return false;
  }
}

// Run the test
testGeminiAPI().then(success => {
  if (success) {
    console.log('\n🎉 Gemini API is ready for production use!');
  } else {
    console.log('\n💡 Please check your API key and model configuration.');
  }
});
