"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoiceService = void 0;
const openai_1 = require("@/config/openai");
const database_1 = require("@/config/database");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
const uuid_1 = require("uuid");
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
class VoiceService {
    constructor() {
        this.uploadDir = process.env.UPLOAD_DIR || './uploads';
    }
    async transcribeAudio(request) {
        const startTime = Date.now();
        try {
            logger_1.logger.info('Starting audio transcription', {
                audioSize: request.audioBuffer.length,
                language: request.language,
                model: request.model,
            });
            const result = await (0, openai_1.transcribeAudio)(request.audioBuffer, {
                language: request.language,
                model: request.model,
                temperature: request.temperature,
            });
            const responseTime = Date.now() - startTime;
            (0, logger_1.logAPI)('openai', 'whisper-transcription', responseTime);
            logger_1.logger.info('Audio transcription completed', {
                transcriptLength: result.text?.length || 0,
                responseTime,
            });
            const estimatedDuration = request.audioBuffer.length / 16000;
            const estimatedConfidence = 0.9;
            return {
                transcript: result.text || '',
                confidence: estimatedConfidence,
                language: request.language || 'en',
                duration: estimatedDuration,
            };
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            logger_1.logger.error('Audio transcription failed', {
                error: error.message,
                responseTime,
                audioSize: request.audioBuffer.length,
            });
            if (error.error?.type === 'rate_limit_error') {
                throw errorHandler_1.createError.rateLimit('OpenAI API rate limit exceeded');
            }
            throw errorHandler_1.createError.aiService(error.error?.message || 'Failed to transcribe audio', error.statusCode || 500);
        }
    }
    async synthesizeSpeech(request) {
        const startTime = Date.now();
        try {
            logger_1.logger.info('Starting speech synthesis', {
                textLength: request.text.length,
                voice: request.voice,
                speed: request.speed,
            });
            const audioBuffer = await (0, openai_1.generateSpeech)(request.text, {
                voice: request.voice,
                speed: request.speed,
                model: request.model,
            });
            const filename = `tts_${(0, uuid_1.v4)()}.mp3`;
            const filepath = path_1.default.join(this.uploadDir, filename);
            await promises_1.default.writeFile(filepath, audioBuffer);
            await database_1.prisma.audioFile.create({
                data: {
                    filename,
                    originalName: `speech_${Date.now()}.mp3`,
                    mimeType: 'audio/mpeg',
                    size: audioBuffer.length,
                    path: filepath,
                    url: `/api/audio/${filename}`,
                    status: 'PROCESSED',
                    metadata: JSON.stringify({
                        voice: request.voice,
                        speed: request.speed,
                        textLength: request.text.length,
                        generatedAt: new Date().toISOString(),
                    }),
                },
            });
            const responseTime = Date.now() - startTime;
            (0, logger_1.logAPI)('openai', 'tts-synthesis', responseTime);
            logger_1.logger.info('Speech synthesis completed', {
                filename,
                audioSize: audioBuffer.length,
                responseTime,
            });
            const estimatedDuration = request.text.length * 0.1;
            return {
                audioUrl: `/api/audio/${filename}`,
                duration: estimatedDuration,
                format: 'audio/mpeg',
                size: audioBuffer.length,
            };
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            logger_1.logger.error('Speech synthesis failed', {
                error: error.message,
                responseTime,
                textLength: request.text.length,
            });
            if (error.error?.type === 'rate_limit_error') {
                throw errorHandler_1.createError.rateLimit('OpenAI API rate limit exceeded');
            }
            throw errorHandler_1.createError.aiService(error.error?.message || 'Failed to synthesize speech', error.statusCode || 500);
        }
    }
    async saveAudioFile(audioBuffer, originalName, mimeType, metadata) {
        try {
            const filename = `upload_${(0, uuid_1.v4)()}_${originalName}`;
            const filepath = path_1.default.join(this.uploadDir, filename);
            await promises_1.default.writeFile(filepath, audioBuffer);
            await database_1.prisma.audioFile.create({
                data: {
                    filename,
                    originalName,
                    mimeType,
                    size: audioBuffer.length,
                    path: filepath,
                    url: `/api/audio/${filename}`,
                    status: 'UPLOADED',
                    metadata: JSON.stringify(metadata || {}),
                },
            });
            logger_1.logger.info('Audio file saved', {
                filename,
                originalName,
                size: audioBuffer.length,
                mimeType,
            });
            return {
                filename,
                url: `/api/audio/${filename}`,
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to save audio file', {
                error: error.message,
                originalName,
                size: audioBuffer.length,
            });
            throw errorHandler_1.createError.database('Failed to save audio file');
        }
    }
    async getAudioFile(filename) {
        try {
            const audioFile = await database_1.prisma.audioFile.findUnique({
                where: { filename },
            });
            if (!audioFile) {
                return null;
            }
            const buffer = await promises_1.default.readFile(audioFile.path);
            return {
                buffer,
                mimeType: audioFile.mimeType,
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get audio file', {
                error: error.message,
                filename,
            });
            return null;
        }
    }
    async cleanupExpiredFiles() {
        try {
            const expiredFiles = await database_1.prisma.audioFile.findMany({
                where: {
                    OR: [
                        { expiresAt: { lt: new Date() } },
                        {
                            createdAt: {
                                lt: new Date(Date.now() - 24 * 60 * 60 * 1000)
                            },
                            status: 'UPLOADED'
                        }
                    ],
                },
            });
            let deletedCount = 0;
            for (const file of expiredFiles) {
                try {
                    await promises_1.default.unlink(file.path);
                    await database_1.prisma.audioFile.delete({ where: { id: file.id } });
                    deletedCount++;
                }
                catch (error) {
                    logger_1.logger.warn('Failed to delete expired file', {
                        filename: file.filename,
                        error: error.message,
                    });
                }
            }
            if (deletedCount > 0) {
                logger_1.logger.info('Cleaned up expired audio files', { deletedCount });
            }
            return deletedCount;
        }
        catch (error) {
            logger_1.logger.error('Failed to cleanup expired files', {
                error: error.message,
            });
            return 0;
        }
    }
}
exports.VoiceService = VoiceService;
exports.default = VoiceService;
//# sourceMappingURL=voiceService.js.map