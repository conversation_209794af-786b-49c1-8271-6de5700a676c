import winston from 'winston';
import path from 'path';
import fs from 'fs';

// Ensure logs directory exists
const logsDir = path.dirname(process.env.LOG_FILE || './logs/app.log');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS',
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let logMessage = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    // Add stack trace for errors
    if (stack) {
      logMessage += `\n${stack}`;
    }
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      logMessage += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return logMessage;
  })
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss',
  }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let logMessage = `${timestamp} ${level}: ${message}`;
    
    if (stack) {
      logMessage += `\n${stack}`;
    }
    
    if (Object.keys(meta).length > 0) {
      logMessage += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return logMessage;
  })
);

// Create transports array
const transports: winston.transport[] = [];

// Console transport (always enabled in development)
if (process.env.NODE_ENV === 'development') {
  transports.push(
    new winston.transports.Console({
      format: consoleFormat,
      level: process.env.LOG_LEVEL || 'debug',
    })
  );
} else {
  transports.push(
    new winston.transports.Console({
      format: logFormat,
      level: process.env.LOG_LEVEL || 'info',
    })
  );
}

// File transport for persistent logging
transports.push(
  new winston.transports.File({
    filename: process.env.LOG_FILE || './logs/app.log',
    format: logFormat,
    level: process.env.LOG_LEVEL || 'info',
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    tailable: true,
  })
);

// Error-specific file transport
transports.push(
  new winston.transports.File({
    filename: path.join(logsDir, 'error.log'),
    format: logFormat,
    level: 'error',
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    tailable: true,
  })
);

// Create logger instance
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports,
  exitOnError: false,
  silent: process.env.NODE_ENV === 'test',
});

// Add request logging helper
export function logRequest(req: any, res: any, responseTime?: number) {
  const logData = {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent'),
    statusCode: res.statusCode,
    responseTime: responseTime ? `${responseTime}ms` : undefined,
    contentLength: res.get('Content-Length'),
  };

  if (res.statusCode >= 400) {
    logger.warn('HTTP Request', logData);
  } else {
    logger.info('HTTP Request', logData);
  }
}

// Add error logging helper
export function logError(error: Error, context?: any) {
  logger.error('Application Error', {
    message: error.message,
    stack: error.stack,
    name: error.name,
    context,
  });
}

// Add database logging helper
export function logDatabase(operation: string, table: string, duration?: number, error?: Error) {
  const logData = {
    operation,
    table,
    duration: duration ? `${duration}ms` : undefined,
  };

  if (error) {
    logger.error('Database Error', {
      ...logData,
      error: error.message,
      stack: error.stack,
    });
  } else {
    logger.debug('Database Operation', logData);
  }
}

// Add API logging helper
export function logAPI(service: string, operation: string, duration?: number, error?: Error) {
  const logData = {
    service,
    operation,
    duration: duration ? `${duration}ms` : undefined,
  };

  if (error) {
    logger.error('API Error', {
      ...logData,
      error: error.message,
      stack: error.stack,
    });
  } else {
    logger.info('API Call', logData);
  }
}

// Performance logging helper
export function logPerformance(operation: string, startTime: number, metadata?: any) {
  const duration = Date.now() - startTime;
  
  logger.info('Performance', {
    operation,
    duration: `${duration}ms`,
    ...metadata,
  });
  
  // Warn if operation takes too long
  if (duration > 5000) { // 5 seconds
    logger.warn('Slow Operation Detected', {
      operation,
      duration: `${duration}ms`,
      ...metadata,
    });
  }
}

// Security logging helper
export function logSecurity(event: string, details: any, severity: 'info' | 'warn' | 'error' = 'warn') {
  logger[severity]('Security Event', {
    event,
    timestamp: new Date().toISOString(),
    ...details,
  });
}

// Health check logging
export function logHealth(service: string, status: 'healthy' | 'unhealthy', details?: any) {
  const logLevel = status === 'healthy' ? 'info' : 'error';
  
  logger[logLevel]('Health Check', {
    service,
    status,
    timestamp: new Date().toISOString(),
    ...details,
  });
}

// Startup logging
export function logStartup(service: string, version: string, port?: number) {
  logger.info('Service Started', {
    service,
    version,
    port,
    environment: process.env.NODE_ENV,
    nodeVersion: process.version,
    timestamp: new Date().toISOString(),
  });
}

// Shutdown logging
export function logShutdown(service: string, reason?: string) {
  logger.info('Service Shutdown', {
    service,
    reason,
    timestamp: new Date().toISOString(),
  });
}

// Export default logger
export default logger;
