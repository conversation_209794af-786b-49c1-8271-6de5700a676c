import { generateChatCompletion, GEMINI_CONFIG } from '@/config/gemini';
import { prisma } from '@/config/database';
import { logger, logAPI } from '@/utils/logger';
import { createError } from '@/middleware/errorHandler';

export interface ProcessMessageRequest {
  message: string;
  conversationId?: string;
  context?: {
    previousMessages?: number;
    userPreferences?: Record<string, any>;
  };
  userId?: string;
}

export interface ProcessMessageResponse {
  conversationId: string;
  response: string;
  audioUrl?: string;
  metadata: {
    responseTime: number;
    tokensUsed: number;
    confidence?: number;
    modelUsed: string;
  };
}

export class ChatService {
  async processMessage(request: ProcessMessageRequest): Promise<ProcessMessageResponse> {
    const startTime = Date.now();
    
    try {
      logger.info('Processing chat message', {
        messageLength: request.message.length,
        conversationId: request.conversationId,
        userId: request.userId,
      });

      // Get or create conversation
      let conversation;
      if (request.conversationId) {
        conversation = await prisma.conversation.findUnique({
          where: { id: request.conversationId },
          include: {
            messages: {
              orderBy: { createdAt: 'desc' },
              take: request.context?.previousMessages || 5,
            },
          },
        });

        if (!conversation) {
          throw createError.notFound('Conversation');
        }
      } else {
        // Create new conversation
        conversation = await prisma.conversation.create({
          data: {
            userId: request.userId,
            title: this.generateConversationTitle(request.message),
            metadata: JSON.stringify({}),
          },
          include: {
            messages: true,
          },
        });
      }

      // Build conversation context
      const messages = this.buildConversationContext(
        conversation.messages || [],
        request.message
      );

      // Generate AI response
      const aiResponse = await generateChatCompletion(messages, {
        maxTokens: GEMINI_CONFIG.maxTokens,
        temperature: GEMINI_CONFIG.temperature,
        model: GEMINI_CONFIG.model,
      });

      const responseText = aiResponse.choices[0]?.message?.content || '';
      const tokensUsed = aiResponse.usage?.total_tokens || 0;

      // Save user message
      await prisma.message.create({
        data: {
          conversationId: conversation.id,
          role: 'USER',
          content: request.message,
          metadata: JSON.stringify({
            timestamp: new Date().toISOString(),
          }),
        },
      });

      // Save AI response
      await prisma.message.create({
        data: {
          conversationId: conversation.id,
          role: 'ASSISTANT',
          content: responseText,
          tokensUsed,
          responseTime: Date.now() - startTime,
          modelUsed: GEMINI_CONFIG.model,
          metadata: JSON.stringify({
            timestamp: new Date().toISOString(),
            finishReason: aiResponse.choices[0]?.finish_reason,
          }),
        },
      });

      // Update conversation timestamp
      await prisma.conversation.update({
        where: { id: conversation.id },
        data: { updatedAt: new Date() },
      });

      const responseTime = Date.now() - startTime;

      logAPI('gemini', 'chat-completion', responseTime);

      logger.info('Chat message processed successfully', {
        conversationId: conversation.id,
        responseTime,
        tokensUsed,
        responseLength: responseText.length,
      });

      return {
        conversationId: conversation.id,
        response: responseText,
        metadata: {
          responseTime,
          tokensUsed,
          modelUsed: GEMINI_CONFIG.model,
        },
      };

    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      
      logger.error('Failed to process chat message', {
        error: error.message,
        responseTime,
        messageLength: request.message.length,
      });

      if (error.error?.type === 'rate_limit_error') {
        throw createError.rateLimit('Gemini API rate limit exceeded');
      }

      if (error.error?.type === 'authentication_error') {
        throw createError.aiService('Gemini API authentication failed', 401);
      }

      throw createError.aiService(
        error.error?.message || 'Failed to process message',
        error.statusCode || 500
      );
    }
  }

  private buildConversationContext(
    previousMessages: any[],
    currentMessage: string
  ): Array<{ role: 'system' | 'user' | 'assistant'; content: string }> {
    const messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }> = [];

    // Add system prompt
    messages.push({
      role: 'system',
      content: GEMINI_CONFIG.systemPrompt,
    });

    // Add previous messages (in chronological order)
    const sortedMessages = previousMessages
      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
      .slice(-10); // Limit to last 10 messages

    for (const msg of sortedMessages) {
      messages.push({
        role: msg.role.toLowerCase() as 'user' | 'assistant',
        content: msg.content,
      });
    }

    // Add current message
    messages.push({
      role: 'user',
      content: currentMessage,
    });

    return messages;
  }

  private generateConversationTitle(firstMessage: string): string {
    // Generate a title from the first message
    const words = firstMessage.trim().split(/\s+/);
    const title = words.slice(0, 6).join(' ');
    
    if (title.length > 50) {
      return title.substring(0, 47) + '...';
    }
    
    return title || 'New Conversation';
  }
}

export default ChatService;
