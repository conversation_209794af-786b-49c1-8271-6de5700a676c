import { Request, Response } from 'express';
export declare const rateLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const voiceRateLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const uploadRateLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const chatRateLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const strictRateLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const progressiveRateLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const rateLimitInfo: (req: Request, res: Response, next: Function) => void;
export declare function isRateLimited(ip: string, endpoint: string): Promise<boolean>;
export declare function getRateLimitStatus(req: Request): {
    limit: number;
    remaining: number;
    reset: Date;
};
export declare const whitelistMiddleware: (whitelist?: string[]) => (req: Request, res: Response, next: Function) => void;
export default rateLimiter;
//# sourceMappingURL=rateLimiter.d.ts.map