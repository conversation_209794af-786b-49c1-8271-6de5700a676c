export interface ProcessMessageRequest {
    message: string;
    conversationId?: string;
    context?: {
        previousMessages?: number;
        userPreferences?: Record<string, any>;
    };
    userId?: string;
}
export interface ProcessMessageResponse {
    conversationId: string;
    response: string;
    audioUrl?: string;
    metadata: {
        responseTime: number;
        tokensUsed: number;
        confidence?: number;
        modelUsed: string;
    };
}
export declare class ChatService {
    processMessage(request: ProcessMessageRequest): Promise<ProcessMessageResponse>;
    private buildConversationContext;
    private generateConversationTitle;
}
export default ChatService;
//# sourceMappingURL=chatService.d.ts.map