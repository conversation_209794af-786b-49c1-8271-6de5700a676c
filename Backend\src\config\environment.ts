import Joi from 'joi';
import { logger } from '@/utils/logger';

// Environment variable schema
const envSchema = Joi.object({
  NODE_ENV: Joi.string()
    .valid('development', 'staging', 'production', 'test')
    .default('development'),
  
  PORT: Joi.number()
    .port()
    .default(8000),
  
  DATABASE_URL: Joi.string()
    .required()
    .description('Database connection URL'),
  
  GOOGLE_API_KEY: Joi.string()
    .required()
    .description('Google Gemini API key'),

  MODEL_NAME: Joi.string()
    .default('gemini-2.5-flash')
    .description('Gemini model to use'),

  GEMINI_MAX_TOKENS: Joi.number()
    .integer()
    .min(1)
    .max(4000)
    .default(150)
    .description('Maximum tokens for Gemini responses'),

  GEMINI_TEMPERATURE: Joi.number()
    .min(0)
    .max(2)
    .default(0.7)
    .description('Gemini temperature setting'),
  
  CORS_ORIGIN: Joi.string()
    .default('http://localhost:3000')
    .description('CORS allowed origins (comma-separated)'),
  
  CORS_CREDENTIALS: Joi.boolean()
    .default(true)
    .description('Enable CORS credentials'),
  
  RATE_LIMIT_ENABLED: Joi.boolean()
    .default(true)
    .description('Enable rate limiting'),
  
  RATE_LIMIT_WINDOW_MS: Joi.number()
    .integer()
    .min(1000)
    .default(60000)
    .description('Rate limit window in milliseconds'),
  
  RATE_LIMIT_MAX_REQUESTS: Joi.number()
    .integer()
    .min(1)
    .default(100)
    .description('Maximum requests per window'),
  
  RATE_LIMIT_VOICE_MAX_REQUESTS: Joi.number()
    .integer()
    .min(1)
    .default(20)
    .description('Maximum voice requests per window'),
  
  MAX_FILE_SIZE: Joi.number()
    .integer()
    .min(1024)
    .default(10485760) // 10MB
    .description('Maximum file upload size in bytes'),
  
  MAX_RECORDING_DURATION: Joi.number()
    .integer()
    .min(1)
    .max(300) // 5 minutes
    .default(60)
    .description('Maximum recording duration in seconds'),
  
  UPLOAD_DIR: Joi.string()
    .default('./uploads')
    .description('Directory for file uploads'),
  
  ALLOWED_AUDIO_FORMATS: Joi.string()
    .default('audio/wav,audio/mp3,audio/webm,audio/ogg')
    .description('Allowed audio MIME types (comma-separated)'),
  
  LOG_LEVEL: Joi.string()
    .valid('error', 'warn', 'info', 'debug')
    .default('info')
    .description('Logging level'),
  
  LOG_FILE: Joi.string()
    .default('./logs/app.log')
    .description('Log file path'),
  
  JWT_SECRET: Joi.string()
    .min(32)
    .description('JWT secret key (optional for MVP)'),
  
  JWT_EXPIRES_IN: Joi.string()
    .default('24h')
    .description('JWT expiration time'),
  
  BCRYPT_ROUNDS: Joi.number()
    .integer()
    .min(8)
    .max(15)
    .default(12)
    .description('Bcrypt hashing rounds'),
  
  AUDIO_SAMPLE_RATE: Joi.number()
    .integer()
    .valid(8000, 16000, 22050, 44100, 48000)
    .default(16000)
    .description('Audio sample rate for processing'),
  
  AUDIO_CHANNELS: Joi.number()
    .integer()
    .valid(1, 2)
    .default(1)
    .description('Audio channels (1=mono, 2=stereo)'),
  
  AUDIO_BIT_DEPTH: Joi.number()
    .integer()
    .valid(8, 16, 24, 32)
    .default(16)
    .description('Audio bit depth'),
  
  SENTRY_DSN: Joi.string()
    .uri()
    .description('Sentry DSN for error tracking (optional)'),
  
  REDIS_URL: Joi.string()
    .uri()
    .description('Redis connection URL (optional)'),
  
  ENABLE_API_DOCS: Joi.boolean()
    .default(false)
    .description('Enable API documentation endpoint'),
  
  ENABLE_CORS_LOGGING: Joi.boolean()
    .default(false)
    .description('Enable CORS request logging'),
  
  ENABLE_REQUEST_LOGGING: Joi.boolean()
    .default(true)
    .description('Enable request logging middleware'),
}).unknown();

export interface EnvironmentConfig {
  NODE_ENV: string;
  PORT: number;
  DATABASE_URL: string;
  GOOGLE_API_KEY: string;
  MODEL_NAME: string;
  GEMINI_MAX_TOKENS: number;
  GEMINI_TEMPERATURE: number;
  CORS_ORIGIN: string;
  CORS_CREDENTIALS: boolean;
  RATE_LIMIT_ENABLED: boolean;
  RATE_LIMIT_WINDOW_MS: number;
  RATE_LIMIT_MAX_REQUESTS: number;
  RATE_LIMIT_VOICE_MAX_REQUESTS: number;
  MAX_FILE_SIZE: number;
  MAX_RECORDING_DURATION: number;
  UPLOAD_DIR: string;
  ALLOWED_AUDIO_FORMATS: string;
  LOG_LEVEL: string;
  LOG_FILE: string;
  JWT_SECRET?: string;
  JWT_EXPIRES_IN: string;
  BCRYPT_ROUNDS: number;
  AUDIO_SAMPLE_RATE: number;
  AUDIO_CHANNELS: number;
  AUDIO_BIT_DEPTH: number;
  SENTRY_DSN?: string;
  REDIS_URL?: string;
  ENABLE_API_DOCS: boolean;
  ENABLE_CORS_LOGGING: boolean;
  ENABLE_REQUEST_LOGGING: boolean;
}

export function validateEnvironment(): EnvironmentConfig {
  const { error, value } = envSchema.validate(process.env, {
    allowUnknown: true,
    stripUnknown: true,
  });

  if (error) {
    logger.error('Environment validation failed:', error.details);
    throw new Error(`Environment validation failed: ${error.message}`);
  }

  // Log configuration (excluding sensitive data)
  const safeConfig = { ...value };
  delete safeConfig.OPENAI_API_KEY;
  delete safeConfig.JWT_SECRET;
  delete safeConfig.DATABASE_URL;
  delete safeConfig.SENTRY_DSN;
  delete safeConfig.REDIS_URL;

  logger.info('Environment configuration loaded:', safeConfig);

  return value as EnvironmentConfig;
}

// Export validated configuration
export const config = validateEnvironment();
