import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse, ApiError } from '@/types';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const API_TIMEOUT = 30000; // 30 seconds

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add request ID for tracking
    config.headers['X-Request-ID'] = generateRequestId();
    
    // Add timestamp
    config.headers['X-Request-Time'] = new Date().toISOString();
    
    // Add user agent
    if (typeof window !== 'undefined') {
      config.headers['X-User-Agent'] = navigator.userAgent;
    }
    
    // Log request in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        data: config.data,
        params: config.params,
      });
    }
    
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // Log response in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
      });
    }
    
    return response;
  },
  (error) => {
    // Log error in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
      });
    }
    
    // Transform error to consistent format
    const apiError: ApiError = {
      success: false,
      error: {
        code: error.response?.data?.error?.code || 'NETWORK_ERROR',
        message: error.response?.data?.error?.message || error.message || 'An unexpected error occurred',
        details: error.response?.data?.error?.details,
      },
      timestamp: new Date().toISOString(),
    };
    
    return Promise.reject(apiError);
  }
);

// Utility functions
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Generic API methods
export const api = {
  // GET request
  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.get<ApiResponse<T>>(url, config);
    return response.data.data as T;
  },
  
  // POST request
  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.post<ApiResponse<T>>(url, data, config);
    return response.data.data as T;
  },
  
  // PUT request
  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.put<ApiResponse<T>>(url, data, config);
    return response.data.data as T;
  },
  
  // DELETE request
  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.delete<ApiResponse<T>>(url, config);
    return response.data.data as T;
  },
  
  // PATCH request
  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    const response = await apiClient.patch<ApiResponse<T>>(url, data, config);
    return response.data.data as T;
  },
  
  // Upload file
  upload: async <T = any>(url: string, file: File | Blob, config?: AxiosRequestConfig): Promise<T> => {
    const formData = new FormData();
    formData.append('audio', file);
    
    const response = await apiClient.post<ApiResponse<T>>(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    });
    
    return response.data.data as T;
  },
  
  // Download file
  download: async (url: string, config?: AxiosRequestConfig): Promise<Blob> => {
    const response = await apiClient.get(url, {
      ...config,
      responseType: 'blob',
    });
    
    return response.data;
  },
};

// Health check
export const healthApi = {
  check: () => api.get('/api/health'),
  detailed: () => api.get('/api/health/detailed'),
  ready: () => api.get('/api/health/ready'),
  live: () => api.get('/api/health/live'),
};

// Configuration API
export const configApi = {
  getConfig: () => api.get('/api/config'),
  getVoices: () => api.get('/api/config/voices'),
  getLanguages: () => api.get('/api/config/languages'),
  getLimits: () => api.get('/api/config/limits'),
  getFeatures: () => api.get('/api/config/features'),
  getSystem: () => api.get('/api/config/system'),
};

// Chat API
export const chatApi = {
  sendMessage: (data: { message: string; conversationId?: string; context?: any }) =>
    api.post('/api/chat/message', data),
  
  getHistory: (conversationId: string, params?: { limit?: number; offset?: number }) =>
    api.get(`/api/chat/history/${conversationId}`, { params }),
  
  getConversations: (params?: { page?: number; limit?: number; sortBy?: string; sortOrder?: string }) =>
    api.get('/api/chat/conversations', { params }),
  
  updateTitle: (conversationId: string, title: string) =>
    api.put(`/api/chat/conversations/${conversationId}/title`, { title }),
  
  deleteConversation: (conversationId: string) =>
    api.delete(`/api/chat/history/${conversationId}`),
  
  clearConversations: () =>
    api.delete('/api/chat/conversations'),
  
  getStats: () =>
    api.get('/api/chat/stats'),
};

// Voice API
export const voiceApi = {
  transcribe: (audioBlob: Blob, options?: { language?: string; model?: string }) =>
    api.upload('/api/voice/transcribe', audioBlob, {
      params: options,
    }),
  
  synthesize: (data: { text: string; voice?: string; speed?: number; model?: string }) =>
    api.post('/api/voice/synthesize', data),
  
  process: (audioBlob: Blob, options?: { conversationId?: string; language?: string; voiceSettings?: any }) =>
    api.upload('/api/voice/process', audioBlob, {
      params: options,
    }),
};

// Audio API
export const audioApi = {
  upload: (audioBlob: Blob, metadata?: any) =>
    api.upload('/api/audio/upload', audioBlob, {
      params: { metadata: JSON.stringify(metadata) },
    }),
  
  download: (filename: string) =>
    api.download(`/api/audio/${filename}`),
  
  getUrl: (filename: string) =>
    `${API_BASE_URL}/api/audio/${filename}`,
};

// Feedback API
export const feedbackApi = {
  submit: (data: {
    type: 'bug' | 'feature' | 'general' | 'rating';
    message: string;
    rating?: number;
    conversationId?: string;
    metadata?: any;
  }) => api.post('/api/feedback', data),
};

// Connection utilities
export const connectionUtils = {
  // Test API connection
  testConnection: async (): Promise<boolean> => {
    try {
      await healthApi.check();
      return true;
    } catch (error) {
      return false;
    }
  },
  
  // Get connection status
  getConnectionStatus: async (): Promise<'connected' | 'disconnected' | 'error'> => {
    try {
      await healthApi.live();
      return 'connected';
    } catch (error) {
      return 'disconnected';
    }
  },
  
  // Retry with exponential backoff
  retryWithBackoff: async <T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> => {
    let lastError: any;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (i === maxRetries - 1) {
          throw error;
        }
        
        const delay = baseDelay * Math.pow(2, i);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  },
};

// Error handling utilities
export const errorUtils = {
  // Check if error is network related
  isNetworkError: (error: any): boolean => {
    return error?.error?.code === 'NETWORK_ERROR' || 
           error?.code === 'ECONNREFUSED' ||
           error?.message?.includes('Network Error');
  },
  
  // Check if error is rate limit related
  isRateLimitError: (error: any): boolean => {
    return error?.error?.code?.includes('RATE_LIMIT') ||
           error?.response?.status === 429;
  },
  
  // Check if error is authentication related
  isAuthError: (error: any): boolean => {
    return error?.error?.code === 'UNAUTHORIZED' ||
           error?.response?.status === 401;
  },
  
  // Get user-friendly error message
  getUserFriendlyMessage: (error: any): string => {
    if (errorUtils.isNetworkError(error)) {
      return 'Unable to connect to the server. Please check your internet connection.';
    }
    
    if (errorUtils.isRateLimitError(error)) {
      return 'Too many requests. Please wait a moment before trying again.';
    }
    
    if (errorUtils.isAuthError(error)) {
      return 'Authentication required. Please log in to continue.';
    }
    
    return error?.error?.message || 'An unexpected error occurred. Please try again.';
  },
};

export default api;
