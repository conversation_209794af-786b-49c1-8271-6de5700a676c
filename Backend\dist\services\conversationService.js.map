{"version": 3, "file": "conversationService.js", "sourceRoot": "", "sources": ["../../src/services/conversationService.ts"], "names": [], "mappings": ";;;AAAA,gDAA2C;AAC3C,2CAAwC;AACxC,4DAAwD;AAGxD,MAAa,mBAAmB;IAC9B,KAAK,CAAC,2BAA2B,CAC/B,cAAsB,EACtB,UAKI,EAAE;QAEN,IAAI,CAAC;YACH,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;YAE1D,MAAM,YAAY,GAAG,MAAM,iBAAM,CAAC,YAAY,CAAC,UAAU,CAAC;gBACxD,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE;gBAC7B,OAAO,EAAE;oBACP,QAAQ,EAAE;wBACR,KAAK,EAAE;4BACL,GAAG,CAAC,MAAM,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC;4BAC5C,GAAG,CAAC,KAAK,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC;yBAC3C;wBACD,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;wBAC7B,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,KAAK;qBACZ;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO;gBACL,GAAG,YAAY;gBACf,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC;gBACnD,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAC1C,GAAG,GAAG;oBACN,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC;iBAC3C,CAAC,CAAC;aACJ,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE;gBACvD,cAAc;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,0BAAW,CAAC,QAAQ,CAAC,iCAAiC,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,MAAe,EACf,UAKI,EAAE;QAEN,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG,WAAW,EAAE,SAAS,GAAG,MAAM,EAAE,GAAG,OAAO,CAAC;YACnF,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAEhC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAEvC,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC/C,iBAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;oBAC3B,KAAK;oBACL,OAAO,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE;oBAChC,IAAI;oBACJ,IAAI,EAAE,KAAK;oBACX,OAAO,EAAE;wBACP,MAAM,EAAE;4BACN,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;yBAC3B;qBACF;iBACF,CAAC;gBACF,iBAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC;aACrC,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;YAE5C,OAAO;gBACL,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAC/B,GAAG,IAAI;oBACP,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAC3C,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;iBACnC,CAAC,CAAC;gBACH,UAAU,EAAE;oBACV,IAAI;oBACJ,KAAK;oBACL,KAAK;oBACL,UAAU;oBACV,OAAO,EAAE,IAAI,GAAG,UAAU;oBAC1B,OAAO,EAAE,IAAI,GAAG,CAAC;iBAClB;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBAC/C,MAAM;gBACN,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,0BAAW,CAAC,QAAQ,CAAC,kCAAkC,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,uBAAuB,CAC3B,cAAsB,EACtB,KAAa,EACb,MAAe;QAEf,IAAI,CAAC;YACH,MAAM,KAAK,GAAQ,EAAE,EAAE,EAAE,cAAc,EAAE,CAAC;YAC1C,IAAI,MAAM,EAAE,CAAC;gBACX,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;YACxB,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,iBAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YAErE,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,0BAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YAC7C,CAAC;YAED,MAAM,mBAAmB,GAAG,MAAM,iBAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC3D,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE;gBAC7B,IAAI,EAAE;oBACJ,KAAK;oBACL,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB;aACF,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACxC,cAAc;gBACd,QAAQ,EAAE,KAAK;gBACf,MAAM;aACP,CAAC,CAAC;YAEH,OAAO;gBACL,GAAG,mBAAmB;gBACtB,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,QAAQ,CAAC;aAC3D,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;gBACxB,MAAM,KAAK,CAAC;YACd,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBAClD,cAAc;gBACd,KAAK;gBACL,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,0BAAW,CAAC,QAAQ,CAAC,qCAAqC,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,cAAsB,EAAE,MAAe;QAC9D,IAAI,CAAC;YACH,MAAM,KAAK,GAAQ,EAAE,EAAE,EAAE,cAAc,EAAE,CAAC;YAC1C,IAAI,MAAM,EAAE,CAAC;gBACX,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;YACxB,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,iBAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YAErE,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,0BAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;YAC7C,CAAC;YAGD,MAAM,iBAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC9B,KAAK,EAAE,EAAE,cAAc,EAAE;aAC1B,CAAC,CAAC;YAGH,MAAM,iBAAM,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC/B,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE;aAC9B,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAClC,cAAc;gBACd,MAAM;aACP,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;gBACxB,MAAM,KAAK,CAAC;YACd,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBAC5C,cAAc;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,0BAAW,CAAC,QAAQ,CAAC,+BAA+B,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,MAAe;QAC1C,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAGvC,MAAM,aAAa,GAAG,MAAM,iBAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;gBACvD,KAAK;gBACL,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;aACrB,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAErD,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,OAAO,CAAC,CAAC;YACX,CAAC;YAGD,MAAM,iBAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC9B,KAAK,EAAE;oBACL,cAAc,EAAE,EAAE,EAAE,EAAE,eAAe,EAAE;iBACxC;aACF,CAAC,CAAC;YAGH,MAAM,MAAM,GAAG,MAAM,iBAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YAE/D,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACxC,MAAM;gBACN,YAAY,EAAE,MAAM,CAAC,KAAK;aAC3B,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC,KAAK,CAAC;QAEtB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACjD,MAAM;gBACN,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,0BAAW,CAAC,QAAQ,CAAC,+BAA+B,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,MAAe;QACxC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAEvC,MAAM,CACJ,kBAAkB,EAClB,aAAa,EACb,mBAAmB,EACnB,0BAA0B,EAC3B,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpB,iBAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC;gBACpC,iBAAM,CAAC,OAAO,CAAC,KAAK,CAAC;oBACnB,KAAK,EAAE;wBACL,YAAY,EAAE,KAAK;qBACpB;iBACF,CAAC;gBACF,iBAAM,CAAC,YAAY,CAAC,KAAK,CAAC;oBACxB,KAAK,EAAE;wBACL,GAAG,KAAK;wBACR,SAAS,EAAE;4BACT,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;yBACpD;qBACF;iBACF,CAAC;gBACF,iBAAM,CAAC,YAAY,CAAC,SAAS,CAAC;oBAC5B,KAAK;oBACL,IAAI,EAAE;wBACJ,EAAE,EAAE,IAAI;qBACT;iBACF,CAAC;aACH,CAAC,CAAC;YAGH,MAAM,WAAW,GAAG,kBAAkB,GAAG,CAAC;gBACxC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,kBAAkB,GAAG,GAAG,CAAC,GAAG,GAAG;gBAC5D,CAAC,CAAC,CAAC,CAAC;YAEN,OAAO;gBACL,kBAAkB;gBAClB,aAAa;gBACb,mBAAmB;gBACnB,0BAA0B,EAAE,WAAW;gBACvC,KAAK,EAAE;oBACL,qBAAqB,EAAE,mBAAmB;oBAC1C,gBAAgB,EAAE,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC7D,mBAAmB,EAAE,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;iBAC/D;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBAC/C,MAAM;gBACN,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,MAAM,0BAAW,CAAC,QAAQ,CAAC,4CAA4C,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,MAAe,EAAE,OAAe,CAAC;QACnE,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAEzD,OAAO,iBAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAC1B,KAAK,EAAE;gBACL,GAAG,KAAK;gBACR,SAAS,EAAE;oBACT,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;iBACvD;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,MAAe;QAClD,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAEvC,MAAM,aAAa,GAAG,MAAM,iBAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;YACvD,KAAK;YACL,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;iBAC3B;aACF;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE;oBACR,MAAM,EAAE,MAAM;iBACf;aACF;YACD,IAAI,EAAE,CAAC;SACR,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,EAAE,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;YACvB,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK;YAC7B,YAAY,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ;SAC/C,CAAC,CAAC,CAAC,IAAI,CAAC;IACX,CAAC;IAEO,aAAa,CAAC,QAAgB;QACpC,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF;AAvVD,kDAuVC;AAED,kBAAe,mBAAmB,CAAC"}