"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.prisma = void 0;
exports.checkDatabaseConnection = checkDatabaseConnection;
exports.connectDatabase = connectDatabase;
exports.disconnectDatabase = disconnectDatabase;
exports.withTransaction = withTransaction;
exports.getDatabaseMetrics = getDatabaseMetrics;
const client_1 = require("@prisma/client");
const logger_1 = require("@/utils/logger");
let prisma;
function createPrismaClient() {
    const logLevels = [];
    if (process.env.NODE_ENV === 'development') {
        logLevels.push('query', 'info', 'warn', 'error');
    }
    else if (process.env.NODE_ENV === 'staging') {
        logLevels.push('info', 'warn', 'error');
    }
    else {
        logLevels.push('warn', 'error');
    }
    return new client_1.PrismaClient({
        log: logLevels.map(level => ({
            level,
            emit: 'event',
        })),
        errorFormat: 'pretty',
    });
}
if (process.env.NODE_ENV === 'production') {
    exports.prisma = prisma = createPrismaClient();
}
else {
    if (!global.__prisma) {
        global.__prisma = createPrismaClient();
    }
    exports.prisma = prisma = global.__prisma;
}
prisma.$on('query', (e) => {
    logger_1.logger.debug('Database Query:', {
        query: e.query,
        params: e.params,
        duration: `${e.duration}ms`,
        target: e.target,
    });
});
prisma.$on('info', (e) => {
    logger_1.logger.info('Database Info:', e.message);
});
prisma.$on('warn', (e) => {
    logger_1.logger.warn('Database Warning:', e.message);
});
prisma.$on('error', (e) => {
    logger_1.logger.error('Database Error:', e.message);
});
async function checkDatabaseConnection() {
    try {
        await prisma.$queryRaw `SELECT 1`;
        logger_1.logger.info('Database connection successful');
        return true;
    }
    catch (error) {
        logger_1.logger.error('Database connection failed:', error);
        return false;
    }
}
async function connectDatabase() {
    try {
        await prisma.$connect();
        logger_1.logger.info('Database connected successfully');
    }
    catch (error) {
        logger_1.logger.error('Failed to connect to database:', error);
        throw error;
    }
}
async function disconnectDatabase() {
    try {
        await prisma.$disconnect();
        logger_1.logger.info('Database disconnected successfully');
    }
    catch (error) {
        logger_1.logger.error('Error disconnecting from database:', error);
    }
}
async function withTransaction(callback) {
    return prisma.$transaction(async (tx) => {
        return callback(tx);
    });
}
async function getDatabaseMetrics() {
    try {
        const metrics = await prisma.$metrics.json();
        return metrics;
    }
    catch (error) {
        logger_1.logger.error('Failed to get database metrics:', error);
        return null;
    }
}
process.on('beforeExit', async () => {
    await disconnectDatabase();
});
process.on('SIGINT', async () => {
    await disconnectDatabase();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    await disconnectDatabase();
    process.exit(0);
});
exports.default = prisma;
//# sourceMappingURL=database.js.map