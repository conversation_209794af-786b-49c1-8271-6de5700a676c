"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const app_1 = require("./app");
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
const database_1 = require("@/config/database");
dotenv_1.default.config();
async function main() {
    try {
        logger_1.logger.info('Starting Voice AI Chat API Server', {
            environment: environment_1.config.NODE_ENV,
            port: environment_1.config.PORT,
            nodeVersion: process.version,
        });
        logger_1.logger.info('Connecting to database...');
        await (0, database_1.connectDatabase)();
        logger_1.logger.info('Database connected successfully');
        logger_1.logger.info('Starting HTTP server...');
        await (0, app_1.startServer)(environment_1.config.PORT);
    }
    catch (error) {
        logger_1.logger.error('Failed to start server:', {
            error: error.message,
            stack: error.stack,
        });
        process.exit(1);
    }
}
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('Unhandled Rejection at:', {
        promise,
        reason: reason?.message || reason,
        stack: reason?.stack,
    });
    process.exit(1);
});
process.on('uncaughtException', (error) => {
    logger_1.logger.error('Uncaught Exception:', {
        error: error.message,
        stack: error.stack,
    });
    process.exit(1);
});
main();
//# sourceMappingURL=index.js.map