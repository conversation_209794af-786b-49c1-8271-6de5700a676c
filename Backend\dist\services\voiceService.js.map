{"version": 3, "file": "voiceService.js", "sourceRoot": "", "sources": ["../../src/services/voiceService.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAkE;AAClE,gDAA2C;AAC3C,2CAAgD;AAChD,4DAAwD;AACxD,+BAAoC;AACpC,2DAA6B;AAC7B,gDAAwB;AAkCxB,MAAa,YAAY;IAGvB;QACE,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,WAAW,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAA0B;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAC1C,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,MAAM;gBACrC,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,KAAK,EAAE,OAAO,CAAC,KAAK;aACrB,CAAC,CAAC;YAGH,MAAM,MAAM,GAAG,MAAM,IAAA,wBAAe,EAAC,OAAO,CAAC,WAAW,EAAE;gBACxD,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,WAAW,EAAE,OAAO,CAAC,WAAW;aACjC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,IAAA,eAAM,EAAC,QAAQ,EAAE,uBAAuB,EAAE,YAAY,CAAC,CAAC;YAExD,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;gBAC3C,gBAAgB,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC;gBAC1C,YAAY;aACb,CAAC,CAAC;YAIH,MAAM,iBAAiB,GAAG,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,CAAC;YAC7D,MAAM,mBAAmB,GAAG,GAAG,CAAC;YAEhC,OAAO;gBACL,UAAU,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE;gBAC7B,UAAU,EAAE,mBAAmB;gBAC/B,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,IAAI;gBAClC,QAAQ,EAAE,iBAAiB;aAC5B,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBACzC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,YAAY;gBACZ,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,MAAM;aACtC,CAAC,CAAC;YAEH,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK,kBAAkB,EAAE,CAAC;gBAC7C,MAAM,0BAAW,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;YAChE,CAAC;YAED,MAAM,0BAAW,CAAC,SAAS,CACzB,KAAK,CAAC,KAAK,EAAE,OAAO,IAAI,4BAA4B,EACpD,KAAK,CAAC,UAAU,IAAI,GAAG,CACxB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,OAA0B;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACvC,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM;gBAC/B,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,KAAK,EAAE,OAAO,CAAC,KAAK;aACrB,CAAC,CAAC;YAGH,MAAM,WAAW,GAAG,MAAM,IAAA,uBAAc,EAAC,OAAO,CAAC,IAAI,EAAE;gBACrD,KAAK,EAAE,OAAO,CAAC,KAAY;gBAC3B,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,KAAK,EAAE,OAAO,CAAC,KAAK;aACrB,CAAC,CAAC;YAGH,MAAM,QAAQ,GAAG,OAAO,IAAA,SAAM,GAAE,MAAM,CAAC;YACvC,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAErD,MAAM,kBAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAG1C,MAAM,iBAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC5B,IAAI,EAAE;oBACJ,QAAQ;oBACR,YAAY,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,MAAM;oBACxC,QAAQ,EAAE,YAAY;oBACtB,IAAI,EAAE,WAAW,CAAC,MAAM;oBACxB,IAAI,EAAE,QAAQ;oBACd,GAAG,EAAE,cAAc,QAAQ,EAAE;oBAC7B,MAAM,EAAE,WAAW;oBACnB,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC;wBACvB,KAAK,EAAE,OAAO,CAAC,KAAK;wBACpB,KAAK,EAAE,OAAO,CAAC,KAAK;wBACpB,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM;wBAC/B,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACtC,CAAC;iBACH;aACF,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,IAAA,eAAM,EAAC,QAAQ,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;YAEhD,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACxC,QAAQ;gBACR,SAAS,EAAE,WAAW,CAAC,MAAM;gBAC7B,YAAY;aACb,CAAC,CAAC;YAGH,MAAM,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;YAEpD,OAAO;gBACL,QAAQ,EAAE,cAAc,QAAQ,EAAE;gBAClC,QAAQ,EAAE,iBAAiB;gBAC3B,MAAM,EAAE,YAAY;gBACpB,IAAI,EAAE,WAAW,CAAC,MAAM;aACzB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE5C,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;gBACtC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,YAAY;gBACZ,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,MAAM;aAChC,CAAC,CAAC;YAEH,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK,kBAAkB,EAAE,CAAC;gBAC7C,MAAM,0BAAW,CAAC,SAAS,CAAC,gCAAgC,CAAC,CAAC;YAChE,CAAC;YAED,MAAM,0BAAW,CAAC,SAAS,CACzB,KAAK,CAAC,KAAK,EAAE,OAAO,IAAI,6BAA6B,EACrD,KAAK,CAAC,UAAU,IAAI,GAAG,CACxB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,WAAmB,EACnB,YAAoB,EACpB,QAAgB,EAChB,QAA8B;QAE9B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,UAAU,IAAA,SAAM,GAAE,IAAI,YAAY,EAAE,CAAC;YACtD,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAErD,MAAM,kBAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAG1C,MAAM,iBAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC5B,IAAI,EAAE;oBACJ,QAAQ;oBACR,YAAY;oBACZ,QAAQ;oBACR,IAAI,EAAE,WAAW,CAAC,MAAM;oBACxB,IAAI,EAAE,QAAQ;oBACd,GAAG,EAAE,cAAc,QAAQ,EAAE;oBAC7B,MAAM,EAAE,UAAU;oBAClB,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,EAAE,CAAC;iBACzC;aACF,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC9B,QAAQ;gBACR,YAAY;gBACZ,IAAI,EAAE,WAAW,CAAC,MAAM;gBACxB,QAAQ;aACT,CAAC,CAAC;YAEH,OAAO;gBACL,QAAQ;gBACR,GAAG,EAAE,cAAc,QAAQ,EAAE;aAC9B,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBACxC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,YAAY;gBACZ,IAAI,EAAE,WAAW,CAAC,MAAM;aACzB,CAAC,CAAC;YACH,MAAM,0BAAW,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,QAAgB;QACjC,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,iBAAM,CAAC,SAAS,CAAC,UAAU,CAAC;gBAClD,KAAK,EAAE,EAAE,QAAQ,EAAE;aACpB,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAEjD,OAAO;gBACL,MAAM;gBACN,QAAQ,EAAE,SAAS,CAAC,QAAQ;aAC7B,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBACvC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,QAAQ;aACT,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,iBAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;gBACnD,KAAK,EAAE;oBACL,EAAE,EAAE;wBACF,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,EAAE;wBACjC;4BACE,SAAS,EAAE;gCACT,EAAE,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;6BAC/C;4BACD,MAAM,EAAE,UAAU;yBACnB;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;gBAChC,IAAI,CAAC;oBACH,MAAM,kBAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC3B,MAAM,iBAAM,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;oBAC1D,YAAY,EAAE,CAAC;gBACjB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;wBAC3C,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,KAAK,EAAE,KAAK,CAAC,OAAO;qBACrB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBACrB,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;YAClE,CAAC;YAED,OAAO,YAAY,CAAC;QAEtB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;gBAC9C,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;CACF;AAzQD,oCAyQC;AAED,kBAAe,YAAY,CAAC"}