import { useState, useEffect, useRef, useCallback } from 'react';
import { UseSpeechSynthesisOptions, UseSpeechSynthesisReturn } from '@/types';

export function useSpeechSynthesis(): UseSpeechSynthesisReturn {
  const [speaking, setSpeaking] = useState(false);
  const [supported, setSupported] = useState(false);
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);

  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);

  // Check for browser support
  useEffect(() => {
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      setSupported(true);

      // Load voices
      const loadVoices = () => {
        const availableVoices = speechSynthesis.getVoices();
        setVoices(availableVoices);
        console.log('Available voices:', availableVoices.length);
      };

      // Load voices immediately
      loadVoices();

      // Some browsers load voices asynchronously
      speechSynthesis.onvoiceschanged = loadVoices;

      return () => {
        speechSynthesis.onvoiceschanged = null;
      };
    } else {
      setSupported(false);
      console.warn('Speech synthesis is not supported in this browser');
    }
  }, []);

  const speak = useCallback((text: string, options: UseSpeechSynthesisOptions = {}) => {
    if (!supported) {
      console.error('Speech synthesis is not supported');
      return;
    }

    if (!text.trim()) {
      console.warn('No text provided for speech synthesis');
      return;
    }

    // Cancel any ongoing speech
    speechSynthesis.cancel();

    // Create new utterance
    const utterance = new SpeechSynthesisUtterance(text);

    // Apply options
    if (options.voice) {
      utterance.voice = options.voice;
    } else if (voices.length > 0) {
      // Use default voice or first available
      const defaultVoice = voices.find(voice => voice.default) || voices[0];
      utterance.voice = defaultVoice;
    }

    utterance.rate = options.rate ?? 1;
    utterance.pitch = options.pitch ?? 1;
    utterance.volume = options.volume ?? 1;

    // Event handlers
    utterance.onstart = () => {
      setSpeaking(true);
      console.log('Speech synthesis started');
    };

    utterance.onend = () => {
      setSpeaking(false);
      utteranceRef.current = null;
      console.log('Speech synthesis ended');
    };

    utterance.onerror = (event) => {
      setSpeaking(false);
      utteranceRef.current = null;
      console.error('Speech synthesis error:', event.error);
    };

    utterance.onpause = () => {
      console.log('Speech synthesis paused');
    };

    utterance.onresume = () => {
      console.log('Speech synthesis resumed');
    };

    // Store reference and start speaking
    utteranceRef.current = utterance;
    speechSynthesis.speak(utterance);

    console.log('Speaking text:', {
      text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
      voice: utterance.voice?.name,
      rate: utterance.rate,
      pitch: utterance.pitch,
      volume: utterance.volume,
    });
  }, [supported, voices]);

  const cancel = useCallback(() => {
    if (supported) {
      speechSynthesis.cancel();
      setSpeaking(false);
      utteranceRef.current = null;
      console.log('Speech synthesis cancelled');
    }
  }, [supported]);

  const pause = useCallback(() => {
    if (supported && speaking) {
      speechSynthesis.pause();
      console.log('Speech synthesis paused');
    }
  }, [supported, speaking]);

  const resume = useCallback(() => {
    if (supported && speechSynthesis.paused) {
      speechSynthesis.resume();
      console.log('Speech synthesis resumed');
    }
  }, [supported]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (supported) {
        speechSynthesis.cancel();
      }
    };
  }, [supported]);

  return {
    speak,
    cancel,
    pause,
    resume,
    speaking,
    supported,
    voices,
  };
}

// Hook for getting voice by criteria
export function useVoiceSelection() {
  const { voices } = useSpeechSynthesis();

  const getVoiceByName = useCallback((name: string): SpeechSynthesisVoice | undefined => {
    return voices.find(voice => voice.name === name);
  }, [voices]);

  const getVoicesByLanguage = useCallback((language: string): SpeechSynthesisVoice[] => {
    return voices.filter(voice => voice.lang.startsWith(language));
  }, [voices]);

  const getVoicesByGender = useCallback((gender: 'male' | 'female'): SpeechSynthesisVoice[] => {
    // Note: Most browsers don't provide gender information
    // This is a best-effort approach based on voice names
    const maleKeywords = ['male', 'man', 'boy', 'masculine'];
    const femaleKeywords = ['female', 'woman', 'girl', 'feminine'];
    
    return voices.filter(voice => {
      const name = voice.name.toLowerCase();
      if (gender === 'male') {
        return maleKeywords.some(keyword => name.includes(keyword));
      } else {
        return femaleKeywords.some(keyword => name.includes(keyword));
      }
    });
  }, [voices]);

  const getDefaultVoice = useCallback((language?: string): SpeechSynthesisVoice | undefined => {
    if (language) {
      const languageVoices = getVoicesByLanguage(language);
      return languageVoices.find(voice => voice.default) || languageVoices[0];
    }
    
    return voices.find(voice => voice.default) || voices[0];
  }, [voices, getVoicesByLanguage]);

  const getBestVoice = useCallback((
    language?: string,
    gender?: 'male' | 'female',
    name?: string
  ): SpeechSynthesisVoice | undefined => {
    // Priority: exact name match > language + gender > language > default
    
    if (name) {
      const exactMatch = getVoiceByName(name);
      if (exactMatch) return exactMatch;
    }

    if (language && gender) {
      const languageVoices = getVoicesByLanguage(language);
      const genderVoices = getVoicesByGender(gender);
      const intersection = languageVoices.filter(voice => 
        genderVoices.some(gv => gv.name === voice.name)
      );
      if (intersection.length > 0) return intersection[0];
    }

    if (language) {
      const languageVoices = getVoicesByLanguage(language);
      if (languageVoices.length > 0) return languageVoices[0];
    }

    return getDefaultVoice();
  }, [getVoiceByName, getVoicesByLanguage, getVoicesByGender, getDefaultVoice]);

  return {
    voices,
    getVoiceByName,
    getVoicesByLanguage,
    getVoicesByGender,
    getDefaultVoice,
    getBestVoice,
  };
}

// Hook for speech synthesis with queue management
export function useSpeechQueue() {
  const { speak, cancel, speaking, supported } = useSpeechSynthesis();
  const [queue, setQueue] = useState<Array<{ text: string; options?: UseSpeechSynthesisOptions }>>([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  const addToQueue = useCallback((text: string, options?: UseSpeechSynthesisOptions) => {
    setQueue(prev => [...prev, { text, options }]);
  }, []);

  const clearQueue = useCallback(() => {
    setQueue([]);
    setCurrentIndex(0);
    cancel();
  }, [cancel]);

  const skipCurrent = useCallback(() => {
    cancel();
    setCurrentIndex(prev => prev + 1);
  }, [cancel]);

  // Process queue
  useEffect(() => {
    if (!speaking && queue.length > 0 && currentIndex < queue.length) {
      const current = queue[currentIndex];
      if (current) {
        speak(current.text, current.options);
      }
    } else if (currentIndex >= queue.length && queue.length > 0) {
      // Queue finished
      setQueue([]);
      setCurrentIndex(0);
    }
  }, [speaking, queue, currentIndex, speak]);

  return {
    addToQueue,
    clearQueue,
    skipCurrent,
    queue,
    currentIndex,
    isProcessing: speaking || (queue.length > 0 && currentIndex < queue.length),
    supported,
  };
}
