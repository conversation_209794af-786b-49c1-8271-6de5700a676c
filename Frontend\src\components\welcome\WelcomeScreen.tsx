import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Mic, MessageSquare, Volume2, Globe, ArrowRight, CheckCircle } from 'lucide-react';
import { clsx } from 'clsx';

interface WelcomeScreenProps {
  onComplete: () => void;
  onSkip: () => void;
}

type WelcomeStep = 'intro' | 'permissions' | 'features' | 'ready';

export function WelcomeScreen({ onComplete, onSkip }: WelcomeScreenProps) {
  const [currentStep, setCurrentStep] = useState<WelcomeStep>('intro');
  const [micPermission, setMicPermission] = useState<'pending' | 'granted' | 'denied'>('pending');

  const requestMicrophonePermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      setMicPermission('granted');
      setCurrentStep('features');
    } catch (error) {
      setMicPermission('denied');
    }
  };

  const renderIntroStep = () => (
    <motion.div
      className="text-center space-y-8"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      <div className="space-y-4">
        <motion.div
          className="w-24 h-24 mx-auto bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center"
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <MessageSquare className="w-12 h-12 text-white" />
        </motion.div>
        
        <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
          Welcome to Voice AI Chat
        </h1>
        
        <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Experience natural conversations with AI using your voice. 
          Speak naturally and get intelligent responses instantly.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
          <Mic className="w-8 h-8 text-primary-600 dark:text-primary-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Voice Input
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Simply hold the button and speak your message naturally
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
          <MessageSquare className="w-8 h-8 text-primary-600 dark:text-primary-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            AI Conversations
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Get intelligent responses powered by advanced AI technology
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
          <Volume2 className="w-8 h-8 text-primary-600 dark:text-primary-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Voice Output
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Hear AI responses with natural-sounding speech synthesis
          </p>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <button
          onClick={() => setCurrentStep('permissions')}
          className="px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
        >
          <span>Get Started</span>
          <ArrowRight className="w-5 h-5" />
        </button>
        
        <button
          onClick={onSkip}
          className="px-8 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          Skip Tutorial
        </button>
      </div>
    </motion.div>
  );

  const renderPermissionsStep = () => (
    <motion.div
      className="text-center space-y-8 max-w-2xl mx-auto"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      <div className="space-y-4">
        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
          <Mic className="w-12 h-12 text-white" />
        </div>
        
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Microphone Permission
        </h2>
        
        <p className="text-lg text-gray-600 dark:text-gray-400">
          To use voice features, we need access to your microphone. 
          Your audio is processed securely and never stored without your permission.
        </p>
      </div>

      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <div className="flex items-center justify-center space-x-4 mb-4">
          <Mic className="w-6 h-6 text-gray-600 dark:text-gray-400" />
          <span className="text-gray-700 dark:text-gray-300">Microphone Access</span>
          {micPermission === 'granted' && (
            <CheckCircle className="w-6 h-6 text-green-500" />
          )}
        </div>
        
        {micPermission === 'pending' && (
          <button
            onClick={requestMicrophonePermission}
            className="w-full px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors"
          >
            Grant Microphone Permission
          </button>
        )}
        
        {micPermission === 'granted' && (
          <div className="text-green-600 dark:text-green-400 font-medium">
            ✓ Microphone permission granted
          </div>
        )}
        
        {micPermission === 'denied' && (
          <div className="space-y-4">
            <div className="text-red-600 dark:text-red-400 font-medium">
              ✗ Microphone permission denied
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              You can still use text chat, but voice features will be unavailable. 
              You can enable microphone access later in your browser settings.
            </p>
            <button
              onClick={() => setCurrentStep('features')}
              className="w-full px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
            >
              Continue Without Voice
            </button>
          </div>
        )}
      </div>

      <button
        onClick={onSkip}
        className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
      >
        Skip this step
      </button>
    </motion.div>
  );

  const renderFeaturesStep = () => (
    <motion.div
      className="text-center space-y-8 max-w-4xl mx-auto"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      <div className="space-y-4">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Key Features
        </h2>
        
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Here's what you can do with Voice AI Chat
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg text-left">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
              <Mic className="w-5 h-5 text-primary-600 dark:text-primary-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Push-to-Talk
            </h3>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Hold the microphone button and speak your message. Release to send.
          </p>
          <ul className="text-sm text-gray-500 dark:text-gray-500 space-y-1">
            <li>• Works with mouse, touch, or spacebar</li>
            <li>• Real-time voice visualization</li>
            <li>• Automatic speech recognition</li>
          </ul>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg text-left">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Smart Conversations
            </h3>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Have natural conversations with AI that remembers context and provides helpful responses.
          </p>
          <ul className="text-sm text-gray-500 dark:text-gray-500 space-y-1">
            <li>• Context-aware responses</li>
            <li>• Conversation history</li>
            <li>• Multiple conversation threads</li>
          </ul>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg text-left">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <Volume2 className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Voice Responses
            </h3>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Listen to AI responses with natural-sounding speech synthesis.
          </p>
          <ul className="text-sm text-gray-500 dark:text-gray-500 space-y-1">
            <li>• Multiple voice options</li>
            <li>• Adjustable speech speed</li>
            <li>• Auto-play or manual control</li>
          </ul>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg text-left">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
              <Globe className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              Multi-Language
            </h3>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Communicate in multiple languages with automatic language detection.
          </p>
          <ul className="text-sm text-gray-500 dark:text-gray-500 space-y-1">
            <li>• Support for 10+ languages</li>
            <li>• Automatic language detection</li>
            <li>• Localized interface</li>
          </ul>
        </div>
      </div>

      <button
        onClick={() => setCurrentStep('ready')}
        className="px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center space-x-2 mx-auto"
      >
        <span>Continue</span>
        <ArrowRight className="w-5 h-5" />
      </button>
    </motion.div>
  );

  const renderReadyStep = () => (
    <motion.div
      className="text-center space-y-8 max-w-2xl mx-auto"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
    >
      <div className="space-y-4">
        <motion.div
          className="w-24 h-24 mx-auto bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center"
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <CheckCircle className="w-12 h-12 text-white" />
        </motion.div>
        
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          You're All Set!
        </h2>
        
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Voice AI Chat is ready to use. Start by holding the microphone button and speaking your first message.
        </p>
      </div>

      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Quick Tips
        </h3>
        <ul className="text-left space-y-2 text-gray-600 dark:text-gray-400">
          <li className="flex items-start space-x-2">
            <span className="text-primary-600 dark:text-primary-400 mt-1">•</span>
            <span>Hold the microphone button while speaking</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-primary-600 dark:text-primary-400 mt-1">•</span>
            <span>Speak clearly and at a normal pace</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-primary-600 dark:text-primary-400 mt-1">•</span>
            <span>Use the settings menu to customize your experience</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-primary-600 dark:text-primary-400 mt-1">•</span>
            <span>Your conversations are automatically saved</span>
          </li>
        </ul>
      </div>

      <button
        onClick={onComplete}
        className="px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center space-x-2 mx-auto"
      >
        <span>Start Chatting</span>
        <ArrowRight className="w-5 h-5" />
      </button>
    </motion.div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'intro':
        return renderIntroStep();
      case 'permissions':
        return renderPermissionsStep();
      case 'features':
        return renderFeaturesStep();
      case 'ready':
        return renderReadyStep();
      default:
        return renderIntroStep();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl">
        {/* Progress indicator */}
        <div className="flex justify-center mb-8">
          <div className="flex space-x-2">
            {['intro', 'permissions', 'features', 'ready'].map((step, index) => (
              <div
                key={step}
                className={clsx(
                  'w-3 h-3 rounded-full transition-colors',
                  ['intro', 'permissions', 'features', 'ready'].indexOf(currentStep) >= index
                    ? 'bg-primary-600'
                    : 'bg-gray-300 dark:bg-gray-600'
                )}
              />
            ))}
          </div>
        </div>

        {/* Step content */}
        <AnimatePresence mode="wait">
          {renderCurrentStep()}
        </AnimatePresence>
      </div>
    </div>
  );
}

export default WelcomeScreen;
