import { Router } from 'express';
import multer from 'multer';
import { asyncHandler } from '@/middleware/errorHandler';
import { validate, voiceSchemas, validateAudioFile } from '@/middleware/validation';
import { voiceRateLimiter } from '@/middleware/rateLimiter';
import { VoiceService } from '@/services/voiceService';
import { ChatService } from '@/services/chatService';
import { logger } from '@/utils/logger';
import { config } from '@/config/environment';

const router = Router();
const voiceService = new VoiceService();
const chatService = new ChatService();

// Configure multer for audio file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: config.MAX_FILE_SIZE,
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = config.ALLOWED_AUDIO_FORMATS.split(',');
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`));
    }
  },
});

// Apply rate limiting to all voice routes
router.use(voiceRateLimiter);

// Transcribe audio to text
router.post('/transcribe',
  upload.single('audio'),
  validate(voiceSchemas.transcribe),
  asyncHandler(async (req, res) => {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'NO_AUDIO_FILE',
          message: 'No audio file provided',
        },
        timestamp: new Date().toISOString(),
      });
    }

    const { language, model, temperature } = req.body;

    logger.info('Processing audio transcription', {
      requestId: req.requestId,
      fileSize: req.file.size,
      mimeType: req.file.mimetype,
      language,
    });

    try {
      // Validate audio file
      validateAudioFile(req.file);

      // Transcribe audio
      const result = await voiceService.transcribeAudio({
        audioBuffer: req.file.buffer,
        language,
        model,
        temperature: temperature ? parseFloat(temperature) : undefined,
      });

      logger.info('Audio transcription completed', {
        requestId: req.requestId,
        transcriptLength: result.transcript.length,
        confidence: result.confidence,
      });

      res.json({
        success: true,
        data: result,
        message: 'Audio transcribed successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error: any) {
      logger.error('Audio transcription failed', {
        requestId: req.requestId,
        error: error.message,
        fileSize: req.file.size,
      });
      throw error;
    }
  })
);

// Synthesize text to speech
router.post('/synthesize',
  validate(voiceSchemas.synthesize),
  asyncHandler(async (req, res) => {
    const { text, voice, speed, model } = req.body;

    logger.info('Processing speech synthesis', {
      requestId: req.requestId,
      textLength: text.length,
      voice,
      speed,
    });

    try {
      const result = await voiceService.synthesizeSpeech({
        text,
        voice,
        speed,
        model,
      });

      logger.info('Speech synthesis completed', {
        requestId: req.requestId,
        audioUrl: result.audioUrl,
        audioSize: result.size,
      });

      res.json({
        success: true,
        data: result,
        message: 'Speech synthesized successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error: any) {
      logger.error('Speech synthesis failed', {
        requestId: req.requestId,
        error: error.message,
        textLength: text.length,
      });
      throw error;
    }
  })
);

// Process voice message (transcribe + chat + synthesize)
router.post('/process',
  upload.single('audio'),
  validate(voiceSchemas.process),
  asyncHandler(async (req, res) => {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'NO_AUDIO_FILE',
          message: 'No audio file provided',
        },
        timestamp: new Date().toISOString(),
      });
    }

    const { conversationId, language, voiceSettings } = req.body;
    const startTime = Date.now();

    logger.info('Processing complete voice interaction', {
      requestId: req.requestId,
      fileSize: req.file.size,
      conversationId,
      language,
    });

    try {
      // Validate audio file
      validateAudioFile(req.file);

      // Step 1: Transcribe audio
      logger.debug('Step 1: Transcribing audio', { requestId: req.requestId });
      const transcriptionResult = await voiceService.transcribeAudio({
        audioBuffer: req.file.buffer,
        language,
      });

      if (!transcriptionResult.transcript.trim()) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'EMPTY_TRANSCRIPTION',
            message: 'No speech detected in audio',
          },
          timestamp: new Date().toISOString(),
        });
      }

      // Step 2: Process chat message
      logger.debug('Step 2: Processing chat message', { requestId: req.requestId });
      const chatResult = await chatService.processMessage({
        message: transcriptionResult.transcript,
        conversationId,
        userId: req.user?.id,
      });

      // Step 3: Synthesize AI response
      logger.debug('Step 3: Synthesizing AI response', { requestId: req.requestId });
      const synthesisResult = await voiceService.synthesizeSpeech({
        text: chatResult.response,
        voice: voiceSettings?.voice || 'alloy',
        speed: voiceSettings?.speed || 1.0,
      });

      const totalProcessingTime = Date.now() - startTime;

      const result = {
        conversationId: chatResult.conversationId,
        userMessage: {
          transcript: transcriptionResult.transcript,
          confidence: transcriptionResult.confidence,
          audioUrl: undefined, // Could save original audio if needed
        },
        aiResponse: {
          text: chatResult.response,
          audioUrl: synthesisResult.audioUrl,
          duration: synthesisResult.duration,
        },
        metadata: {
          processingTime: totalProcessingTime,
          totalTokens: chatResult.metadata.tokensUsed,
          transcriptionConfidence: transcriptionResult.confidence,
          steps: {
            transcription: 'completed',
            chat: 'completed',
            synthesis: 'completed',
          },
        },
      };

      logger.info('Complete voice interaction processed', {
        requestId: req.requestId,
        conversationId: result.conversationId,
        processingTime: totalProcessingTime,
        transcriptLength: transcriptionResult.transcript.length,
        responseLength: chatResult.response.length,
      });

      res.json({
        success: true,
        data: result,
        message: 'Voice interaction processed successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error: any) {
      const processingTime = Date.now() - startTime;
      
      logger.error('Voice interaction processing failed', {
        requestId: req.requestId,
        error: error.message,
        processingTime,
        fileSize: req.file.size,
      });
      throw error;
    }
  })
);

// Get available voices
router.get('/voices',
  asyncHandler(async (req, res) => {
    const voices = [
      { id: 'alloy', name: 'Alloy', gender: 'neutral', language: 'en' },
      { id: 'echo', name: 'Echo', gender: 'male', language: 'en' },
      { id: 'fable', name: 'Fable', gender: 'neutral', language: 'en' },
      { id: 'onyx', name: 'Onyx', gender: 'male', language: 'en' },
      { id: 'nova', name: 'Nova', gender: 'female', language: 'en' },
      { id: 'shimmer', name: 'Shimmer', gender: 'female', language: 'en' },
    ];

    res.json({
      success: true,
      data: voices,
      message: 'Available voices retrieved successfully',
      timestamp: new Date().toISOString(),
    });
  })
);

export default router;
