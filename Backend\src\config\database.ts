import { PrismaClient } from '@prisma/client';
import { logger } from '@/utils/logger';

// Prisma client singleton
let prisma: PrismaClient;

declare global {
  var __prisma: PrismaClient | undefined;
}

// Create Prisma client with logging configuration
function createPrismaClient(): PrismaClient {
  const logLevels: Array<'query' | 'info' | 'warn' | 'error'> = [];
  
  // Configure logging based on environment
  if (process.env.NODE_ENV === 'development') {
    logLevels.push('query', 'info', 'warn', 'error');
  } else if (process.env.NODE_ENV === 'staging') {
    logLevels.push('info', 'warn', 'error');
  } else {
    logLevels.push('warn', 'error');
  }

  return new PrismaClient({
    log: logLevels.map(level => ({
      level,
      emit: 'event',
    })),
    errorFormat: 'pretty',
  });
}

// Initialize Prisma client
if (process.env.NODE_ENV === 'production') {
  prisma = createPrismaClient();
} else {
  // In development, use global variable to prevent multiple instances
  if (!global.__prisma) {
    global.__prisma = createPrismaClient();
  }
  prisma = global.__prisma;
}

// Set up event listeners for logging
prisma.$on('query', (e) => {
  logger.debug('Database Query:', {
    query: e.query,
    params: e.params,
    duration: `${e.duration}ms`,
    target: e.target,
  });
});

prisma.$on('info', (e) => {
  logger.info('Database Info:', e.message);
});

prisma.$on('warn', (e) => {
  logger.warn('Database Warning:', e.message);
});

prisma.$on('error', (e) => {
  logger.error('Database Error:', e.message);
});

// Database connection health check
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    logger.info('Database connection successful');
    return true;
  } catch (error) {
    logger.error('Database connection failed:', error);
    return false;
  }
}

// Database cleanup and connection management
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    logger.info('Database disconnected successfully');
  } catch (error) {
    logger.error('Error disconnecting from database:', error);
  }
}

// Database transaction helper
export async function withTransaction<T>(
  callback: (tx: PrismaClient) => Promise<T>
): Promise<T> {
  return prisma.$transaction(async (tx) => {
    return callback(tx as PrismaClient);
  });
}

// Database metrics and monitoring
export async function getDatabaseMetrics() {
  try {
    const metrics = await prisma.$metrics.json();
    return metrics;
  } catch (error) {
    logger.error('Failed to get database metrics:', error);
    return null;
  }
}

// Graceful shutdown handler
process.on('beforeExit', async () => {
  await disconnectDatabase();
});

process.on('SIGINT', async () => {
  await disconnectDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await disconnectDatabase();
  process.exit(0);
});

export { prisma };
export default prisma;
