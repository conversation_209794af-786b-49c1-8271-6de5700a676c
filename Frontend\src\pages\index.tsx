import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { motion, AnimatePresence } from 'framer-motion';
import { Settings, Menu, X, Mic, MessageSquare } from 'lucide-react';
import { useChatStore } from '@/store/chatStore';
import { useVoiceStore } from '@/store/voiceStore';
import { useSettingsStore } from '@/store/settingsStore';
import { useAudioRecorder } from '@/hooks/useAudioRecorder';
import { useSpeechSynthesis } from '@/hooks/useSpeechSynthesis';
import PushToTalkButton from '@/components/voice/PushToTalkButton';
import VoiceVisualizer from '@/components/voice/VoiceVisualizer';
import TranscriptionDisplay from '@/components/voice/TranscriptionDisplay';
import ChatContainer, { ConversationList } from '@/components/chat/ChatContainer';
import toast from 'react-hot-toast';

export default function HomePage() {
  const [showSidebar, setShowSidebar] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Store hooks
  const {
    messages,
    conversations,
    currentConversation,
    isLoading,
    sendMessage,
    loadConversations,
    loadConversation,
    deleteConversation,
  } = useChatStore();

  const {
    isRecording,
    isProcessing,
    audioLevel,
    transcript,
    confidence,
    error: voiceError,
    startRecording,
    stopRecording,
    processAudio,
    playAudio,
    reset: resetVoice,
  } = useVoiceStore();

  const { settings } = useSettingsStore();

  // Audio recorder hook
  const {
    startRecording: startAudioRecording,
    stopRecording: stopAudioRecording,
    recordingState,
    hasPermission,
    requestPermission,
  } = useAudioRecorder({
    onStop: async (recording) => {
      try {
        await processAudio(recording.blob);
      } catch (error) {
        console.error('Failed to process recording:', error);
      }
    },
    onError: (error) => {
      console.error('Recording error:', error);
      toast.error('Recording failed: ' + error.message);
    },
  });

  // Speech synthesis hook
  const { speak, cancel: cancelSpeech, speaking } = useSpeechSynthesis();

  // Initialize app
  useEffect(() => {
    const initialize = async () => {
      try {
        // Load conversations
        await loadConversations();
        
        // Request microphone permission
        await requestPermission();
        
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        toast.error('Failed to initialize application');
      }
    };

    initialize();
  }, [loadConversations, requestPermission]);

  // Handle voice recording
  const handleStartRecording = async () => {
    try {
      resetVoice();
      await startRecording();
      await startAudioRecording();
    } catch (error) {
      console.error('Failed to start recording:', error);
      toast.error('Failed to start recording');
    }
  };

  const handleStopRecording = async () => {
    try {
      await stopRecording();
      stopAudioRecording();
    } catch (error) {
      console.error('Failed to stop recording:', error);
      toast.error('Failed to stop recording');
    }
  };

  // Handle sending message
  const handleSendMessage = async (message: string) => {
    if (!message.trim()) return;

    try {
      await sendMessage(message, currentConversation?.id);
      
      // Auto-play AI response if enabled
      if (settings.voice.autoPlay) {
        // Find the latest AI message
        const latestMessage = messages[messages.length - 1];
        if (latestMessage?.role === 'assistant' && latestMessage.audioUrl) {
          await playAudio(latestMessage.audioUrl);
        }
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
    }
  };

  // Handle transcript submission
  useEffect(() => {
    if (transcript && !isRecording && !isProcessing) {
      handleSendMessage(transcript);
    }
  }, [transcript, isRecording, isProcessing]);

  // Handle conversation selection
  const handleSelectConversation = async (conversationId: string) => {
    try {
      await loadConversation(conversationId);
      setShowSidebar(false);
    } catch (error) {
      console.error('Failed to load conversation:', error);
      toast.error('Failed to load conversation');
    }
  };

  // Handle conversation deletion
  const handleDeleteConversation = async (conversationId: string) => {
    try {
      await deleteConversation(conversationId);
      toast.success('Conversation deleted');
    } catch (error) {
      console.error('Failed to delete conversation:', error);
      toast.error('Failed to delete conversation');
    }
  };

  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 border-4 border-primary-600 border-t-transparent rounded-full animate-spin" />
          <p className="text-gray-600 dark:text-gray-400">Initializing Voice AI Chat...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Voice AI Chat - Natural Conversations with AI</title>
        <meta name="description" content="Have natural voice conversations with AI using speech recognition and synthesis" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
        {/* Sidebar */}
        <AnimatePresence>
          {showSidebar && (
            <>
              {/* Backdrop */}
              <motion.div
                className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                onClick={() => setShowSidebar(false)}
              />
              
              {/* Sidebar content */}
              <motion.div
                className="fixed left-0 top-0 h-full w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 z-50 lg:relative lg:z-auto"
                initial={{ x: -320 }}
                animate={{ x: 0 }}
                exit={{ x: -320 }}
                transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              >
                {/* Sidebar header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    Conversations
                  </h2>
                  <button
                    onClick={() => setShowSidebar(false)}
                    className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 lg:hidden"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                {/* Conversations list */}
                <div className="flex-1 overflow-y-auto">
                  <ConversationList
                    conversations={conversations}
                    currentConversationId={currentConversation?.id}
                    onSelectConversation={handleSelectConversation}
                    onDeleteConversation={handleDeleteConversation}
                    className="p-2"
                  />
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>

        {/* Main content */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowSidebar(true)}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 lg:hidden"
                >
                  <Menu className="w-5 h-5" />
                </button>
                
                <div className="flex items-center space-x-2">
                  <MessageSquare className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                  <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    Voice AI Chat
                  </h1>
                </div>
              </div>

              <button
                onClick={() => setShowSettings(true)}
                className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                title="Settings"
              >
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </header>

          {/* Chat area */}
          <ChatContainer
            messages={messages}
            isLoading={isLoading}
            className="flex-1"
          />

          {/* Voice input area */}
          <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-6">
            <div className="max-w-4xl mx-auto">
              {/* Transcription display */}
              <AnimatePresence>
                {(transcript || isRecording) && (
                  <div className="mb-4">
                    <TranscriptionDisplay
                      transcript={transcript}
                      confidence={confidence}
                      isLive={isRecording}
                    />
                  </div>
                )}
              </AnimatePresence>

              {/* Voice visualizer */}
              <div className="flex justify-center mb-6">
                <VoiceVisualizer
                  audioLevel={audioLevel}
                  isActive={isRecording}
                  barCount={7}
                />
              </div>

              {/* Push-to-talk button */}
              <div className="flex justify-center">
                <PushToTalkButton
                  onStartRecording={handleStartRecording}
                  onStopRecording={handleStopRecording}
                  isRecording={isRecording}
                  isProcessing={isProcessing}
                  disabled={!hasPermission}
                  size="large"
                />
              </div>

              {/* Error display */}
              <AnimatePresence>
                {voiceError && (
                  <motion.div
                    className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-center"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                  >
                    <p className="text-red-700 dark:text-red-400 text-sm">
                      {voiceError}
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Settings Modal */}
        <SettingsModal
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
        />
      </div>
    </>
  );
}
