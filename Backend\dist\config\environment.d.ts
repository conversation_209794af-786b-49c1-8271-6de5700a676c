export interface EnvironmentConfig {
    NODE_ENV: string;
    PORT: number;
    DATABASE_URL: string;
    OPENAI_API_KEY: string;
    OPENAI_MODEL: string;
    OPENAI_MAX_TOKENS: number;
    OPENAI_TEMPERATURE: number;
    CORS_ORIGIN: string;
    CORS_CREDENTIALS: boolean;
    RATE_LIMIT_ENABLED: boolean;
    RATE_LIMIT_WINDOW_MS: number;
    RATE_LIMIT_MAX_REQUESTS: number;
    RATE_LIMIT_VOICE_MAX_REQUESTS: number;
    MAX_FILE_SIZE: number;
    MAX_RECORDING_DURATION: number;
    UPLOAD_DIR: string;
    ALLOWED_AUDIO_FORMATS: string;
    LOG_LEVEL: string;
    LOG_FILE: string;
    JWT_SECRET?: string;
    JWT_EXPIRES_IN: string;
    BCRYPT_ROUNDS: number;
    AUDIO_SAMPLE_RATE: number;
    AUDIO_CHANNELS: number;
    AUDIO_BIT_DEPTH: number;
    SENTRY_DSN?: string;
    REDIS_URL?: string;
    ENABLE_API_DOCS: boolean;
    ENABLE_CORS_LOGGING: boolean;
    ENABLE_REQUEST_LOGGING: boolean;
}
export declare function validateEnvironment(): EnvironmentConfig;
export declare const config: EnvironmentConfig;
//# sourceMappingURL=environment.d.ts.map