console.log('Starting server...');

const express = require('express');
const cors = require('cors');

console.log('Loading Prisma...');
const { PrismaClient } = require('@prisma/client');

const app = express();
const PORT = process.env.PORT || 8000;
const prisma = new PrismaClient();

// Middleware
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());

// Mock Gemini response function
function generateMockResponse(message) {
  const responses = [
    "I understand your message. This is a mock response from the AI assistant.",
    "Thank you for your input. I'm here to help you with any questions you might have.",
    "That's an interesting point. Let me provide you with some helpful information.",
    "I appreciate you reaching out. How can I assist you further?",
    "Your message has been received. I'm processing your request and will provide a helpful response."
  ];
  
  const randomResponse = responses[Math.floor(Math.random() * responses.length)];
  return `${randomResponse} You said: "${message}"`;
}

// Routes

// Health check
app.get('/api/health', async (req, res) => {
  try {
    // Test database connection
    await prisma.$queryRaw`SELECT 1`;
    
    res.json({
      success: true,
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'connected',
        gemini: 'available (mock mode)',
        speechService: 'available'
      },
      version: '1.0.0'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get configuration
app.get('/api/config', (req, res) => {
  res.json({
    success: true,
    data: {
      maxFileSize: 10485760, // 10MB
      maxRecordingDuration: 60, // 60 seconds
      allowedFormats: ['audio/wav', 'audio/mp3', 'audio/webm', 'audio/ogg'],
      voiceOptions: [
        { id: 'alloy', name: 'Alloy', language: 'en', gender: 'neutral' },
        { id: 'echo', name: 'Echo', language: 'en', gender: 'male' },
        { id: 'fable', name: 'Fable', language: 'en', gender: 'neutral' },
        { id: 'onyx', name: 'Onyx', language: 'en', gender: 'male' },
        { id: 'nova', name: 'Nova', language: 'en', gender: 'female' },
        { id: 'shimmer', name: 'Shimmer', language: 'en', gender: 'female' }
      ],
      supportedLanguages: [
        { code: 'en', name: 'English', nativeName: 'English' },
        { code: 'es', name: 'Spanish', nativeName: 'Español' },
        { code: 'fr', name: 'French', nativeName: 'Français' },
        { code: 'de', name: 'German', nativeName: 'Deutsch' },
        { code: 'it', name: 'Italian', nativeName: 'Italiano' },
        { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
        { code: 'ru', name: 'Russian', nativeName: 'Русский' },
        { code: 'ja', name: 'Japanese', nativeName: '日本語' },
        { code: 'ko', name: 'Korean', nativeName: '한국어' },
        { code: 'zh', name: 'Chinese', nativeName: '中文' }
      ]
    },
    timestamp: new Date().toISOString()
  });
});

// Chat message endpoint
app.post('/api/chat/message', async (req, res) => {
  try {
    const { message, conversationId } = req.body;
    
    if (!message || typeof message !== 'string') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_MESSAGE',
          message: 'Message is required and must be a string'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Get or create conversation
    let conversation;
    if (conversationId) {
      conversation = await prisma.conversation.findUnique({
        where: { id: conversationId }
      });
      
      if (!conversation) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'CONVERSATION_NOT_FOUND',
            message: 'Conversation not found'
          },
          timestamp: new Date().toISOString()
        });
      }
    } else {
      // Create new conversation
      conversation = await prisma.conversation.create({
        data: {
          title: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
          metadata: {}
        }
      });
    }

    // Generate AI response (mock)
    const aiResponse = generateMockResponse(message);

    // Save user message
    await prisma.message.create({
      data: {
        conversationId: conversation.id,
        role: 'USER',
        content: message,
        metadata: {}
      }
    });

    // Save AI response
    await prisma.message.create({
      data: {
        conversationId: conversation.id,
        role: 'ASSISTANT',
        content: aiResponse,
        metadata: {}
      }
    });

    res.json({
      success: true,
      data: {
        conversationId: conversation.id,
        response: aiResponse,
        metadata: {
          responseTime: 100,
          tokensUsed: 50,
          modelUsed: 'gemini-1.5-flash (mock)'
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Chat error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to process chat message'
      },
      timestamp: new Date().toISOString()
    });
  }
});

// Get conversation history
app.get('/api/chat/history/:conversationId', async (req, res) => {
  try {
    const { conversationId } = req.params;
    
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' }
        }
      }
    });

    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'CONVERSATION_NOT_FOUND',
          message: 'Conversation not found'
        },
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      data: {
        conversation: {
          id: conversation.id,
          title: conversation.title,
          createdAt: conversation.createdAt,
          updatedAt: conversation.updatedAt
        },
        messages: conversation.messages.map(msg => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
          createdAt: msg.createdAt
        }))
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('History error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve conversation history'
      },
      timestamp: new Date().toISOString()
    });
  }
});

// Voice transcription (mock)
app.post('/api/voice/transcribe', (req, res) => {
  res.json({
    success: true,
    data: {
      transcript: 'This is a mock transcription. Voice transcription is not yet implemented.',
      confidence: 0.95,
      language: 'en',
      duration: 2.5
    },
    message: 'Audio transcribed successfully (mock)',
    timestamp: new Date().toISOString()
  });
});

// Voice synthesis (mock)
app.post('/api/voice/synthesize', (req, res) => {
  res.json({
    success: true,
    data: {
      audioUrl: '/api/audio/mock_audio.mp3',
      duration: 3.0,
      format: 'audio/mpeg',
      size: 48000
    },
    message: 'Speech synthesized successfully (mock)',
    timestamp: new Date().toISOString()
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Voice AI Chat Backend',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/api/health',
      config: '/api/config',
      chat: '/api/chat/message',
      voice: '/api/voice/*'
    }
  });
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: 'An unexpected error occurred'
    },
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${req.method} ${req.originalUrl} not found`
    },
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Voice AI Chat Backend running on port ${PORT}`);
  console.log(`📍 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📍 API docs: http://localhost:${PORT}/`);
  console.log(`🔗 Frontend should connect to: http://localhost:${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  await prisma.$disconnect();
  process.exit(0);
});
