"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createError = exports.DatabaseError = exports.AIServiceError = exports.AudioProcessingError = exports.FileUploadError = exports.RateLimitError = exports.ForbiddenError = exports.UnauthorizedError = exports.NotFoundError = exports.ValidationError = exports.AppError = void 0;
exports.errorHandler = errorHandler;
exports.asyncHandler = asyncHandler;
exports.notFoundHandler = notFoundHandler;
exports.setupGlobalErrorHandlers = setupGlobalErrorHandlers;
const logger_1 = require("@/utils/logger");
const openai_1 = require("@/config/openai");
class AppError extends Error {
    constructor(message, statusCode = 500, code = 'INTERNAL_ERROR') {
        super(message);
        this.statusCode = statusCode;
        this.code = code;
        this.isOperational = true;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
class ValidationError extends AppError {
    constructor(message, details) {
        super(message, 400, 'VALIDATION_ERROR');
        this.details = details;
    }
}
exports.ValidationError = ValidationError;
class NotFoundError extends AppError {
    constructor(resource = 'Resource') {
        super(`${resource} not found`, 404, 'NOT_FOUND');
    }
}
exports.NotFoundError = NotFoundError;
class UnauthorizedError extends AppError {
    constructor(message = 'Unauthorized access') {
        super(message, 401, 'UNAUTHORIZED');
    }
}
exports.UnauthorizedError = UnauthorizedError;
class ForbiddenError extends AppError {
    constructor(message = 'Forbidden access') {
        super(message, 403, 'FORBIDDEN');
    }
}
exports.ForbiddenError = ForbiddenError;
class RateLimitError extends AppError {
    constructor(message = 'Rate limit exceeded') {
        super(message, 429, 'RATE_LIMIT_EXCEEDED');
    }
}
exports.RateLimitError = RateLimitError;
class FileUploadError extends AppError {
    constructor(message) {
        super(message, 400, 'FILE_UPLOAD_ERROR');
    }
}
exports.FileUploadError = FileUploadError;
class AudioProcessingError extends AppError {
    constructor(message) {
        super(message, 422, 'AUDIO_PROCESSING_ERROR');
    }
}
exports.AudioProcessingError = AudioProcessingError;
class AIServiceError extends AppError {
    constructor(message, statusCode = 503) {
        super(message, statusCode, 'AI_SERVICE_ERROR');
    }
}
exports.AIServiceError = AIServiceError;
class DatabaseError extends AppError {
    constructor(message) {
        super(message, 500, 'DATABASE_ERROR');
    }
}
exports.DatabaseError = DatabaseError;
function errorHandler(error, req, res, next) {
    (0, logger_1.logError)(error, {
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
    });
    let statusCode = 500;
    let code = 'INTERNAL_ERROR';
    let message = 'An unexpected error occurred';
    let details = undefined;
    if (error instanceof AppError) {
        statusCode = error.statusCode;
        code = error.code;
        message = error.message;
        if (error instanceof ValidationError) {
            details = error.details;
        }
    }
    else if (error.name === 'ValidationError') {
        statusCode = 400;
        code = 'VALIDATION_ERROR';
        message = 'Request validation failed';
        details = error.details?.map((detail) => ({
            field: detail.path?.join('.'),
            message: detail.message,
        }));
    }
    else if (error.name === 'MulterError') {
        statusCode = 400;
        code = 'FILE_UPLOAD_ERROR';
        switch (error.code) {
            case 'LIMIT_FILE_SIZE':
                message = 'File size exceeds the maximum allowed limit';
                break;
            case 'LIMIT_FILE_COUNT':
                message = 'Too many files uploaded';
                break;
            case 'LIMIT_UNEXPECTED_FILE':
                message = 'Unexpected file field';
                break;
            default:
                message = 'File upload error';
        }
    }
    else if (error.name === 'PrismaClientKnownRequestError') {
        const prismaError = error;
        statusCode = 400;
        code = 'DATABASE_ERROR';
        switch (prismaError.code) {
            case 'P2002':
                message = 'A record with this information already exists';
                break;
            case 'P2025':
                message = 'Record not found';
                statusCode = 404;
                code = 'NOT_FOUND';
                break;
            case 'P2003':
                message = 'Foreign key constraint violation';
                break;
            default:
                message = 'Database operation failed';
        }
    }
    else if (error.name === 'PrismaClientValidationError') {
        statusCode = 400;
        code = 'VALIDATION_ERROR';
        message = 'Invalid data provided';
    }
    else if (error.message?.includes('OpenAI')) {
        const openaiError = (0, openai_1.handleOpenAIError)(error);
        statusCode = openaiError.statusCode;
        code = openaiError.code;
        message = openaiError.message;
    }
    else if (error.name === 'SyntaxError' && error.message?.includes('JSON')) {
        statusCode = 400;
        code = 'INVALID_JSON';
        message = 'Invalid JSON in request body';
    }
    else if (error.name === 'CastError') {
        statusCode = 400;
        code = 'INVALID_ID';
        message = 'Invalid ID format';
    }
    const errorResponse = {
        success: false,
        error: {
            code,
            message,
            ...(details && { details }),
            ...(process.env.NODE_ENV === 'development' && { stack: error.stack }),
        },
        timestamp: new Date().toISOString(),
    };
    res.status(statusCode).json(errorResponse);
}
function asyncHandler(fn) {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
}
function notFoundHandler(req, res, next) {
    const error = new NotFoundError(`Route ${req.method} ${req.originalUrl} not found`);
    next(error);
}
function setupGlobalErrorHandlers() {
    process.on('uncaughtException', (error) => {
        logger_1.logger.error('Uncaught Exception:', error);
        process.exit(1);
    });
    process.on('unhandledRejection', (reason, promise) => {
        logger_1.logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
        process.exit(1);
    });
}
exports.createError = {
    validation: (message, details) => new ValidationError(message, details),
    notFound: (resource) => new NotFoundError(resource),
    unauthorized: (message) => new UnauthorizedError(message),
    forbidden: (message) => new ForbiddenError(message),
    rateLimit: (message) => new RateLimitError(message),
    fileUpload: (message) => new FileUploadError(message),
    audioProcessing: (message) => new AudioProcessingError(message),
    aiService: (message, statusCode) => new AIServiceError(message, statusCode),
    database: (message) => new DatabaseError(message),
    generic: (message, statusCode, code) => new AppError(message, statusCode, code),
};
//# sourceMappingURL=errorHandler.js.map