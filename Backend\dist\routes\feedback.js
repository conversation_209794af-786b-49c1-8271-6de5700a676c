"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const errorHandler_1 = require("@/middleware/errorHandler");
const validation_1 = require("@/middleware/validation");
const rateLimiter_1 = require("@/middleware/rateLimiter");
const database_1 = require("@/config/database");
const logger_1 = require("@/utils/logger");
const router = (0, express_1.Router)();
router.use(rateLimiter_1.rateLimiter);
router.post('/', (0, validation_1.validate)(validation_1.feedbackSchemas.submit), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { type, message, rating, conversationId, userAgent, metadata, } = req.body;
    logger_1.logger.info('Processing feedback submission', {
        requestId: req.requestId,
        type,
        messageLength: message.length,
        rating,
        conversationId,
    });
    try {
        const feedback = await database_1.prisma.feedback.create({
            data: {
                type,
                message,
                rating,
                conversationId,
                userAgent: userAgent || req.get('User-Agent'),
                ipAddress: req.ip,
                metadata: JSON.stringify({
                    ...metadata,
                    submittedAt: new Date().toISOString(),
                    requestId: req.requestId,
                }),
            },
        });
        logger_1.logger.info('Feedback submitted successfully', {
            requestId: req.requestId,
            feedbackId: feedback.id,
            type,
            rating,
        });
        res.status(201).json({
            success: true,
            data: {
                id: feedback.id,
                type: feedback.type,
                submittedAt: feedback.createdAt,
            },
            message: 'Feedback submitted successfully',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to submit feedback', {
            requestId: req.requestId,
            error: error.message,
            type,
            messageLength: message.length,
        });
        throw error;
    }
}));
router.get('/stats', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    logger_1.logger.debug('Retrieving feedback statistics', {
        requestId: req.requestId,
    });
    try {
        const [totalFeedback, feedbackByType, averageRating, recentFeedback,] = await Promise.all([
            database_1.prisma.feedback.count(),
            database_1.prisma.feedback.groupBy({
                by: ['type'],
                _count: {
                    id: true,
                },
            }),
            database_1.prisma.feedback.aggregate({
                _avg: {
                    rating: true,
                },
                where: {
                    rating: {
                        not: null,
                    },
                },
            }),
            database_1.prisma.feedback.count({
                where: {
                    createdAt: {
                        gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                    },
                },
            }),
        ]);
        const stats = {
            total: totalFeedback,
            byType: feedbackByType.reduce((acc, item) => {
                acc[item.type] = item._count.id;
                return acc;
            }, {}),
            averageRating: averageRating._avg.rating ?
                Math.round(averageRating._avg.rating * 100) / 100 : null,
            recentCount: recentFeedback,
        };
        logger_1.logger.debug('Feedback statistics retrieved', {
            requestId: req.requestId,
            total: stats.total,
            averageRating: stats.averageRating,
        });
        res.json({
            success: true,
            data: stats,
            message: 'Feedback statistics retrieved successfully',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to retrieve feedback statistics', {
            requestId: req.requestId,
            error: error.message,
        });
        throw error;
    }
}));
router.get('/recent', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { limit = 10, offset = 0, type } = req.query;
    logger_1.logger.debug('Retrieving recent feedback', {
        requestId: req.requestId,
        limit,
        offset,
        type,
    });
    try {
        const where = {};
        if (type) {
            where.type = type;
        }
        const feedback = await database_1.prisma.feedback.findMany({
            where,
            orderBy: {
                createdAt: 'desc',
            },
            skip: Number(offset),
            take: Number(limit),
            select: {
                id: true,
                type: true,
                message: true,
                rating: true,
                conversationId: true,
                createdAt: true,
            },
        });
        logger_1.logger.debug('Recent feedback retrieved', {
            requestId: req.requestId,
            count: feedback.length,
            type,
        });
        res.json({
            success: true,
            data: feedback,
            message: 'Recent feedback retrieved successfully',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to retrieve recent feedback', {
            requestId: req.requestId,
            error: error.message,
        });
        throw error;
    }
}));
exports.default = router;
//# sourceMappingURL=feedback.js.map