/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./src/pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! private-next-pages/_error */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\nprivate_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _store_settingsStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/settingsStore */ \"./src/store/settingsStore.ts\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_2__, _store_settingsStore__WEBPACK_IMPORTED_MODULE_3__]);\n([react_hot_toast__WEBPACK_IMPORTED_MODULE_2__, _store_settingsStore__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    const { loadSettings } = (0,_store_settingsStore__WEBPACK_IMPORTED_MODULE_3__.useSettingsStore)();\n    // Load settings on app start\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSettings();\n    }, [\n        loadSettings\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: \"var(--toast-bg)\",\n                        color: \"var(--toast-color)\",\n                        border: \"1px solid var(--toast-border)\"\n                    },\n                    success: {\n                        iconTheme: {\n                            primary: \"#10b981\",\n                            secondary: \"#ffffff\"\n                        }\n                    },\n                    error: {\n                        iconTheme: {\n                            primary: \"#ef4444\",\n                            secondary: \"#ffffff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Voice-enabled AI chat application with real-time speech recognition and synthesis\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: \"AI, voice chat, speech recognition, text-to-speech, conversation\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"author\",\n                        content: \"Voice Chat Team\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#3b82f6\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#3b82f6\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: \"Voice AI Chat\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: \"Have natural conversations with AI using your voice\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: \"/og-image.png\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:title\",\n                        content: \"Voice AI Chat\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:description\",\n                        content: \"Have natural conversations with AI using your voice\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:image\",\n                        content: \"/twitter-image.png\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\",\n                        className: \"jsx-c3ed4667bd3e7610\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"c3ed4667bd3e7610\",\n                        children: \".jsx-c3ed4667bd3e7610:root{--toast-bg:#ffffff;--toast-color:#374151;--toast-border:#e5e7eb}.dark.jsx-c3ed4667bd3e7610{--toast-bg:#1f2937;--toast-color:#f9fafb;--toast-border:#374151}\"\n                    }, void 0, false, void 0, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "./src/store/settingsStore.ts":
/*!************************************!*\
  !*** ./src/store/settingsStore.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSettingsStore: () => (/* binding */ useSettingsStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"zustand\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"zustand/middleware\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zustand__WEBPACK_IMPORTED_MODULE_0__, zustand_middleware__WEBPACK_IMPORTED_MODULE_1__]);\n([zustand__WEBPACK_IMPORTED_MODULE_0__, zustand_middleware__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n// Default settings\nconst defaultVoiceSettings = {\n    voice: {\n        id: \"alloy\",\n        name: \"Alloy\",\n        language: \"en\",\n        gender: \"neutral\"\n    },\n    speed: 1.0,\n    language: \"en-US\",\n    autoPlay: true\n};\nconst defaultUISettings = {\n    theme: \"system\",\n    fontSize: \"medium\",\n    showTimestamps: true,\n    showConfidence: true,\n    compactMode: false,\n    enableAnimations: true,\n    enableSounds: true\n};\nconst defaultAISettings = {\n    responseLength: \"medium\",\n    personality: \"helpful\",\n    contextMemory: 10,\n    temperature: 0.7\n};\nconst defaultSettings = {\n    voice: defaultVoiceSettings,\n    ui: defaultUISettings,\n    ai: defaultAISettings\n};\nconst useSettingsStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // Initial state\n        settings: defaultSettings,\n        isLoading: false,\n        hasUnsavedChanges: false,\n        lastSaved: undefined,\n        // Actions\n        updateVoiceSettings: (newSettings)=>{\n            set((state)=>({\n                    settings: {\n                        ...state.settings,\n                        voice: {\n                            ...state.settings.voice,\n                            ...newSettings\n                        }\n                    },\n                    hasUnsavedChanges: true\n                }));\n            // Auto-save after a delay\n            setTimeout(()=>{\n                get().saveSettings();\n            }, 1000);\n        },\n        updateUISettings: (newSettings)=>{\n            set((state)=>({\n                    settings: {\n                        ...state.settings,\n                        ui: {\n                            ...state.settings.ui,\n                            ...newSettings\n                        }\n                    },\n                    hasUnsavedChanges: true\n                }));\n            // Apply theme immediately\n            if (newSettings.theme) {\n                applyTheme(newSettings.theme);\n            }\n            // Apply font size immediately\n            if (newSettings.fontSize) {\n                applyFontSize(newSettings.fontSize);\n            }\n            // Auto-save after a delay\n            setTimeout(()=>{\n                get().saveSettings();\n            }, 1000);\n        },\n        updateAISettings: (newSettings)=>{\n            set((state)=>({\n                    settings: {\n                        ...state.settings,\n                        ai: {\n                            ...state.settings.ai,\n                            ...newSettings\n                        }\n                    },\n                    hasUnsavedChanges: true\n                }));\n            // Auto-save after a delay\n            setTimeout(()=>{\n                get().saveSettings();\n            }, 1000);\n        },\n        resetSettings: ()=>{\n            set({\n                settings: defaultSettings,\n                hasUnsavedChanges: true\n            });\n            // Apply default theme and font size\n            applyTheme(defaultSettings.ui.theme);\n            applyFontSize(defaultSettings.ui.fontSize);\n            // Save immediately\n            get().saveSettings();\n        },\n        loadSettings: ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                // Settings are automatically loaded by persist middleware\n                const state = get();\n                // Apply current settings to DOM\n                applyTheme(state.settings.ui.theme);\n                applyFontSize(state.settings.ui.fontSize);\n                set({\n                    isLoading: false,\n                    hasUnsavedChanges: false\n                });\n            } catch (error) {\n                console.error(\"Failed to load settings:\", error);\n                // Fallback to default settings\n                set({\n                    settings: defaultSettings,\n                    isLoading: false,\n                    hasUnsavedChanges: false\n                });\n            }\n        },\n        saveSettings: ()=>{\n            try {\n                // Settings are automatically saved by persist middleware\n                set({\n                    hasUnsavedChanges: false,\n                    lastSaved: new Date().toISOString()\n                });\n                console.log(\"Settings saved successfully\");\n            } catch (error) {\n                console.error(\"Failed to save settings:\", error);\n            }\n        },\n        // Additional helper methods\n        getVoiceById: (voiceId)=>{\n            // This would typically fetch from available voices\n            // For now, return a default voice structure\n            return {\n                id: voiceId,\n                name: voiceId.charAt(0).toUpperCase() + voiceId.slice(1),\n                language: \"en\",\n                gender: \"neutral\"\n            };\n        },\n        exportSettings: ()=>{\n            const state = get();\n            const exportData = {\n                settings: state.settings,\n                exportedAt: new Date().toISOString(),\n                version: \"1.0.0\"\n            };\n            const blob = new Blob([\n                JSON.stringify(exportData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const url = URL.createObjectURL(blob);\n            const a = document.createElement(\"a\");\n            a.href = url;\n            a.download = `voice-chat-settings-${new Date().toISOString().split(\"T\")[0]}.json`;\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            URL.revokeObjectURL(url);\n        },\n        importSettings: (file)=>{\n            return new Promise((resolve, reject)=>{\n                const reader = new FileReader();\n                reader.onload = (event)=>{\n                    try {\n                        const data = JSON.parse(event.target?.result);\n                        if (data.settings && data.version) {\n                            set({\n                                settings: {\n                                    ...defaultSettings,\n                                    ...data.settings\n                                },\n                                hasUnsavedChanges: true\n                            });\n                            // Apply imported settings\n                            const newSettings = get().settings;\n                            applyTheme(newSettings.ui.theme);\n                            applyFontSize(newSettings.ui.fontSize);\n                            get().saveSettings();\n                            resolve();\n                        } else {\n                            reject(new Error(\"Invalid settings file format\"));\n                        }\n                    } catch (error) {\n                        reject(new Error(\"Failed to parse settings file\"));\n                    }\n                };\n                reader.onerror = ()=>{\n                    reject(new Error(\"Failed to read settings file\"));\n                };\n                reader.readAsText(file);\n            });\n        },\n        validateSettings: (settings)=>{\n            try {\n                // Basic validation\n                if (settings.voice?.speed && (settings.voice.speed < 0.25 || settings.voice.speed > 4.0)) {\n                    return false;\n                }\n                if (settings.ai?.temperature && (settings.ai.temperature < 0 || settings.ai.temperature > 2)) {\n                    return false;\n                }\n                if (settings.ai?.contextMemory && (settings.ai.contextMemory < 1 || settings.ai.contextMemory > 50)) {\n                    return false;\n                }\n                return true;\n            } catch (error) {\n                return false;\n            }\n        }\n    }), {\n    name: \"settings-store\",\n    version: 1,\n    migrate: (persistedState, version)=>{\n        // Handle settings migration between versions\n        if (version === 0) {\n            // Migrate from version 0 to 1\n            return {\n                ...defaultSettings,\n                ...persistedState\n            };\n        }\n        return persistedState;\n    }\n}), {\n    name: \"settings-store\"\n}));\n// Helper functions for applying settings to DOM\nfunction applyTheme(theme) {\n    if (true) return;\n    const root = document.documentElement;\n    if (theme === \"dark\") {\n        root.classList.add(\"dark\");\n    } else if (theme === \"light\") {\n        root.classList.remove(\"dark\");\n    } else {\n        // System theme\n        const prefersDark = window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n        if (prefersDark) {\n            root.classList.add(\"dark\");\n        } else {\n            root.classList.remove(\"dark\");\n        }\n    }\n}\nfunction applyFontSize(fontSize) {\n    if (true) return;\n    const root = document.documentElement;\n    // Remove existing font size classes\n    root.classList.remove(\"text-sm\", \"text-base\", \"text-lg\");\n    // Apply new font size\n    switch(fontSize){\n        case \"small\":\n            root.classList.add(\"text-sm\");\n            break;\n        case \"large\":\n            root.classList.add(\"text-lg\");\n            break;\n        default:\n            root.classList.add(\"text-base\");\n            break;\n    }\n}\n// Initialize theme on load\nif (false) {}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvc3RvcmUvc2V0dGluZ3NTdG9yZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBaUM7QUFDc0I7QUFHdkQsbUJBQW1CO0FBQ25CLE1BQU1HLHVCQUFzQztJQUMxQ0MsT0FBTztRQUNMQyxJQUFJO1FBQ0pDLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxRQUFRO0lBQ1Y7SUFDQUMsT0FBTztJQUNQRixVQUFVO0lBQ1ZHLFVBQVU7QUFDWjtBQUVBLE1BQU1DLG9CQUFnQztJQUNwQ0MsT0FBTztJQUNQQyxVQUFVO0lBQ1ZDLGdCQUFnQjtJQUNoQkMsZ0JBQWdCO0lBQ2hCQyxhQUFhO0lBQ2JDLGtCQUFrQjtJQUNsQkMsY0FBYztBQUNoQjtBQUVBLE1BQU1DLG9CQUF1QztJQUMzQ0MsZ0JBQWdCO0lBQ2hCQyxhQUFhO0lBQ2JDLGVBQWU7SUFDZkMsYUFBYTtBQUNmO0FBRUEsTUFBTUMsa0JBQStCO0lBQ25DcEIsT0FBT0Q7SUFDUHNCLElBQUlkO0lBQ0plLElBQUlQO0FBQ047QUFTTyxNQUFNUSxtQkFBbUIzQiwrQ0FBTUEsR0FDcENDLDREQUFRQSxDQUNOQywyREFBT0EsQ0FDTCxDQUFDMEIsS0FBS0MsTUFBUztRQUNiLGdCQUFnQjtRQUNoQkMsVUFBVU47UUFDVk8sV0FBVztRQUNYQyxtQkFBbUI7UUFDbkJDLFdBQVdDO1FBRVgsVUFBVTtRQUNWQyxxQkFBcUIsQ0FBQ0M7WUFDcEJSLElBQUlTLENBQUFBLFFBQVU7b0JBQ1pQLFVBQVU7d0JBQ1IsR0FBR08sTUFBTVAsUUFBUTt3QkFDakIxQixPQUFPOzRCQUNMLEdBQUdpQyxNQUFNUCxRQUFRLENBQUMxQixLQUFLOzRCQUN2QixHQUFHZ0MsV0FBVzt3QkFDaEI7b0JBQ0Y7b0JBQ0FKLG1CQUFtQjtnQkFDckI7WUFFQSwwQkFBMEI7WUFDMUJNLFdBQVc7Z0JBQ1RULE1BQU1VLFlBQVk7WUFDcEIsR0FBRztRQUNMO1FBRUFDLGtCQUFrQixDQUFDSjtZQUNqQlIsSUFBSVMsQ0FBQUEsUUFBVTtvQkFDWlAsVUFBVTt3QkFDUixHQUFHTyxNQUFNUCxRQUFRO3dCQUNqQkwsSUFBSTs0QkFDRixHQUFHWSxNQUFNUCxRQUFRLENBQUNMLEVBQUU7NEJBQ3BCLEdBQUdXLFdBQVc7d0JBQ2hCO29CQUNGO29CQUNBSixtQkFBbUI7Z0JBQ3JCO1lBRUEsMEJBQTBCO1lBQzFCLElBQUlJLFlBQVl4QixLQUFLLEVBQUU7Z0JBQ3JCNkIsV0FBV0wsWUFBWXhCLEtBQUs7WUFDOUI7WUFFQSw4QkFBOEI7WUFDOUIsSUFBSXdCLFlBQVl2QixRQUFRLEVBQUU7Z0JBQ3hCNkIsY0FBY04sWUFBWXZCLFFBQVE7WUFDcEM7WUFFQSwwQkFBMEI7WUFDMUJ5QixXQUFXO2dCQUNUVCxNQUFNVSxZQUFZO1lBQ3BCLEdBQUc7UUFDTDtRQUVBSSxrQkFBa0IsQ0FBQ1A7WUFDakJSLElBQUlTLENBQUFBLFFBQVU7b0JBQ1pQLFVBQVU7d0JBQ1IsR0FBR08sTUFBTVAsUUFBUTt3QkFDakJKLElBQUk7NEJBQ0YsR0FBR1csTUFBTVAsUUFBUSxDQUFDSixFQUFFOzRCQUNwQixHQUFHVSxXQUFXO3dCQUNoQjtvQkFDRjtvQkFDQUosbUJBQW1CO2dCQUNyQjtZQUVBLDBCQUEwQjtZQUMxQk0sV0FBVztnQkFDVFQsTUFBTVUsWUFBWTtZQUNwQixHQUFHO1FBQ0w7UUFFQUssZUFBZTtZQUNiaEIsSUFBSTtnQkFDRkUsVUFBVU47Z0JBQ1ZRLG1CQUFtQjtZQUNyQjtZQUVBLG9DQUFvQztZQUNwQ1MsV0FBV2pCLGdCQUFnQkMsRUFBRSxDQUFDYixLQUFLO1lBQ25DOEIsY0FBY2xCLGdCQUFnQkMsRUFBRSxDQUFDWixRQUFRO1lBRXpDLG1CQUFtQjtZQUNuQmdCLE1BQU1VLFlBQVk7UUFDcEI7UUFFQU0sY0FBYztZQUNaakIsSUFBSTtnQkFBRUcsV0FBVztZQUFLO1lBRXRCLElBQUk7Z0JBQ0YsMERBQTBEO2dCQUMxRCxNQUFNTSxRQUFRUjtnQkFFZCxnQ0FBZ0M7Z0JBQ2hDWSxXQUFXSixNQUFNUCxRQUFRLENBQUNMLEVBQUUsQ0FBQ2IsS0FBSztnQkFDbEM4QixjQUFjTCxNQUFNUCxRQUFRLENBQUNMLEVBQUUsQ0FBQ1osUUFBUTtnQkFFeENlLElBQUk7b0JBQ0ZHLFdBQVc7b0JBQ1hDLG1CQUFtQjtnQkFDckI7WUFFRixFQUFFLE9BQU9jLE9BQU87Z0JBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO2dCQUUxQywrQkFBK0I7Z0JBQy9CbEIsSUFBSTtvQkFDRkUsVUFBVU47b0JBQ1ZPLFdBQVc7b0JBQ1hDLG1CQUFtQjtnQkFDckI7WUFDRjtRQUNGO1FBRUFPLGNBQWM7WUFDWixJQUFJO2dCQUNGLHlEQUF5RDtnQkFDekRYLElBQUk7b0JBQ0ZJLG1CQUFtQjtvQkFDbkJDLFdBQVcsSUFBSWUsT0FBT0MsV0FBVztnQkFDbkM7Z0JBRUFGLFFBQVFHLEdBQUcsQ0FBQztZQUVkLEVBQUUsT0FBT0osT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7WUFDNUM7UUFDRjtRQUVBLDRCQUE0QjtRQUM1QkssY0FBYyxDQUFDQztZQUNiLG1EQUFtRDtZQUNuRCw0Q0FBNEM7WUFDNUMsT0FBTztnQkFDTC9DLElBQUkrQztnQkFDSjlDLE1BQU04QyxRQUFRQyxNQUFNLENBQUMsR0FBR0MsV0FBVyxLQUFLRixRQUFRRyxLQUFLLENBQUM7Z0JBQ3REaEQsVUFBVTtnQkFDVkMsUUFBUTtZQUNWO1FBQ0Y7UUFFQWdELGdCQUFnQjtZQUNkLE1BQU1uQixRQUFRUjtZQUNkLE1BQU00QixhQUFhO2dCQUNqQjNCLFVBQVVPLE1BQU1QLFFBQVE7Z0JBQ3hCNEIsWUFBWSxJQUFJVixPQUFPQyxXQUFXO2dCQUNsQ1UsU0FBUztZQUNYO1lBRUEsTUFBTUMsT0FBTyxJQUFJQyxLQUFLO2dCQUFDQyxLQUFLQyxTQUFTLENBQUNOLFlBQVksTUFBTTthQUFHLEVBQUU7Z0JBQzNETyxNQUFNO1lBQ1I7WUFFQSxNQUFNQyxNQUFNQyxJQUFJQyxlQUFlLENBQUNQO1lBQ2hDLE1BQU1RLElBQUlDLFNBQVNDLGFBQWEsQ0FBQztZQUNqQ0YsRUFBRUcsSUFBSSxHQUFHTjtZQUNURyxFQUFFSSxRQUFRLEdBQUcsQ0FBQyxvQkFBb0IsRUFBRSxJQUFJeEIsT0FBT0MsV0FBVyxHQUFHd0IsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsS0FBSyxDQUFDO1lBQ2pGSixTQUFTSyxJQUFJLENBQUNDLFdBQVcsQ0FBQ1A7WUFDMUJBLEVBQUVRLEtBQUs7WUFDUFAsU0FBU0ssSUFBSSxDQUFDRyxXQUFXLENBQUNUO1lBQzFCRixJQUFJWSxlQUFlLENBQUNiO1FBQ3RCO1FBRUFjLGdCQUFnQixDQUFDQztZQUNmLE9BQU8sSUFBSUMsUUFBYyxDQUFDQyxTQUFTQztnQkFDakMsTUFBTUMsU0FBUyxJQUFJQztnQkFFbkJELE9BQU9FLE1BQU0sR0FBRyxDQUFDQztvQkFDZixJQUFJO3dCQUNGLE1BQU1DLE9BQU8xQixLQUFLMkIsS0FBSyxDQUFDRixNQUFNRyxNQUFNLEVBQUVDO3dCQUV0QyxJQUFJSCxLQUFLMUQsUUFBUSxJQUFJMEQsS0FBSzdCLE9BQU8sRUFBRTs0QkFDakMvQixJQUFJO2dDQUNGRSxVQUFVO29DQUNSLEdBQUdOLGVBQWU7b0NBQ2xCLEdBQUdnRSxLQUFLMUQsUUFBUTtnQ0FDbEI7Z0NBQ0FFLG1CQUFtQjs0QkFDckI7NEJBRUEsMEJBQTBCOzRCQUMxQixNQUFNSSxjQUFjUCxNQUFNQyxRQUFROzRCQUNsQ1csV0FBV0wsWUFBWVgsRUFBRSxDQUFDYixLQUFLOzRCQUMvQjhCLGNBQWNOLFlBQVlYLEVBQUUsQ0FBQ1osUUFBUTs0QkFFckNnQixNQUFNVSxZQUFZOzRCQUNsQjJDO3dCQUNGLE9BQU87NEJBQ0xDLE9BQU8sSUFBSVMsTUFBTTt3QkFDbkI7b0JBQ0YsRUFBRSxPQUFPOUMsT0FBTzt3QkFDZHFDLE9BQU8sSUFBSVMsTUFBTTtvQkFDbkI7Z0JBQ0Y7Z0JBRUFSLE9BQU9TLE9BQU8sR0FBRztvQkFDZlYsT0FBTyxJQUFJUyxNQUFNO2dCQUNuQjtnQkFFQVIsT0FBT1UsVUFBVSxDQUFDZDtZQUNwQjtRQUNGO1FBRUFlLGtCQUFrQixDQUFDakU7WUFDakIsSUFBSTtnQkFDRixtQkFBbUI7Z0JBQ25CLElBQUlBLFNBQVMxQixLQUFLLEVBQUVLLFNBQVVxQixDQUFBQSxTQUFTMUIsS0FBSyxDQUFDSyxLQUFLLEdBQUcsUUFBUXFCLFNBQVMxQixLQUFLLENBQUNLLEtBQUssR0FBRyxHQUFFLEdBQUk7b0JBQ3hGLE9BQU87Z0JBQ1Q7Z0JBRUEsSUFBSXFCLFNBQVNKLEVBQUUsRUFBRUgsZUFBZ0JPLENBQUFBLFNBQVNKLEVBQUUsQ0FBQ0gsV0FBVyxHQUFHLEtBQUtPLFNBQVNKLEVBQUUsQ0FBQ0gsV0FBVyxHQUFHLElBQUk7b0JBQzVGLE9BQU87Z0JBQ1Q7Z0JBRUEsSUFBSU8sU0FBU0osRUFBRSxFQUFFSixpQkFBa0JRLENBQUFBLFNBQVNKLEVBQUUsQ0FBQ0osYUFBYSxHQUFHLEtBQUtRLFNBQVNKLEVBQUUsQ0FBQ0osYUFBYSxHQUFHLEVBQUMsR0FBSTtvQkFDbkcsT0FBTztnQkFDVDtnQkFFQSxPQUFPO1lBQ1QsRUFBRSxPQUFPd0IsT0FBTztnQkFDZCxPQUFPO1lBQ1Q7UUFDRjtJQUNGLElBQ0E7SUFDRXhDLE1BQU07SUFDTnFELFNBQVM7SUFDVHFDLFNBQVMsQ0FBQ0MsZ0JBQXFCdEM7UUFDN0IsNkNBQTZDO1FBQzdDLElBQUlBLFlBQVksR0FBRztZQUNqQiw4QkFBOEI7WUFDOUIsT0FBTztnQkFDTCxHQUFHbkMsZUFBZTtnQkFDbEIsR0FBR3lFLGNBQWM7WUFDbkI7UUFDRjtRQUNBLE9BQU9BO0lBQ1Q7QUFDRixJQUVGO0lBQ0UzRixNQUFNO0FBQ1IsSUFFRjtBQUVGLGdEQUFnRDtBQUNoRCxTQUFTbUMsV0FBVzdCLEtBQTBCO0lBQzVDLElBQUksSUFBa0IsRUFBYTtJQUVuQyxNQUFNc0YsT0FBTzdCLFNBQVM4QixlQUFlO0lBRXJDLElBQUl2RixVQUFVLFFBQVE7UUFDcEJzRixLQUFLRSxTQUFTLENBQUNDLEdBQUcsQ0FBQztJQUNyQixPQUFPLElBQUl6RixVQUFVLFNBQVM7UUFDNUJzRixLQUFLRSxTQUFTLENBQUNFLE1BQU0sQ0FBQztJQUN4QixPQUFPO1FBQ0wsZUFBZTtRQUNmLE1BQU1DLGNBQWNDLE9BQU9DLFVBQVUsQ0FBQyxnQ0FBZ0NDLE9BQU87UUFDN0UsSUFBSUgsYUFBYTtZQUNmTCxLQUFLRSxTQUFTLENBQUNDLEdBQUcsQ0FBQztRQUNyQixPQUFPO1lBQ0xILEtBQUtFLFNBQVMsQ0FBQ0UsTUFBTSxDQUFDO1FBQ3hCO0lBQ0Y7QUFDRjtBQUVBLFNBQVM1RCxjQUFjN0IsUUFBZ0M7SUFDckQsSUFBSSxJQUFrQixFQUFhO0lBRW5DLE1BQU1xRixPQUFPN0IsU0FBUzhCLGVBQWU7SUFFckMsb0NBQW9DO0lBQ3BDRCxLQUFLRSxTQUFTLENBQUNFLE1BQU0sQ0FBQyxXQUFXLGFBQWE7SUFFOUMsc0JBQXNCO0lBQ3RCLE9BQVF6RjtRQUNOLEtBQUs7WUFDSHFGLEtBQUtFLFNBQVMsQ0FBQ0MsR0FBRyxDQUFDO1lBQ25CO1FBQ0YsS0FBSztZQUNISCxLQUFLRSxTQUFTLENBQUNDLEdBQUcsQ0FBQztZQUNuQjtRQUNGO1lBQ0VILEtBQUtFLFNBQVMsQ0FBQ0MsR0FBRyxDQUFDO1lBQ25CO0lBQ0o7QUFDRjtBQUVBLDJCQUEyQjtBQUMzQixJQUFJLEtBQWtCLEVBQWEsRUFTbEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92b2ljZS1jaGF0LWZyb250ZW5kLy4vc3JjL3N0b3JlL3NldHRpbmdzU3RvcmUudHM/ODQyNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGUgfSBmcm9tICd6dXN0YW5kJztcbmltcG9ydCB7IGRldnRvb2xzLCBwZXJzaXN0IH0gZnJvbSAnenVzdGFuZC9taWRkbGV3YXJlJztcbmltcG9ydCB7IEFwcFNldHRpbmdzLCBWb2ljZVNldHRpbmdzLCBVSVNldHRpbmdzLCBTZXR0aW5nc1N0b3JlIH0gZnJvbSAnQC90eXBlcyc7XG5cbi8vIERlZmF1bHQgc2V0dGluZ3NcbmNvbnN0IGRlZmF1bHRWb2ljZVNldHRpbmdzOiBWb2ljZVNldHRpbmdzID0ge1xuICB2b2ljZToge1xuICAgIGlkOiAnYWxsb3knLFxuICAgIG5hbWU6ICdBbGxveScsXG4gICAgbGFuZ3VhZ2U6ICdlbicsXG4gICAgZ2VuZGVyOiAnbmV1dHJhbCcsXG4gIH0sXG4gIHNwZWVkOiAxLjAsXG4gIGxhbmd1YWdlOiAnZW4tVVMnLFxuICBhdXRvUGxheTogdHJ1ZSxcbn07XG5cbmNvbnN0IGRlZmF1bHRVSVNldHRpbmdzOiBVSVNldHRpbmdzID0ge1xuICB0aGVtZTogJ3N5c3RlbScsXG4gIGZvbnRTaXplOiAnbWVkaXVtJyxcbiAgc2hvd1RpbWVzdGFtcHM6IHRydWUsXG4gIHNob3dDb25maWRlbmNlOiB0cnVlLFxuICBjb21wYWN0TW9kZTogZmFsc2UsXG4gIGVuYWJsZUFuaW1hdGlvbnM6IHRydWUsXG4gIGVuYWJsZVNvdW5kczogdHJ1ZSxcbn07XG5cbmNvbnN0IGRlZmF1bHRBSVNldHRpbmdzOiBBcHBTZXR0aW5nc1snYWknXSA9IHtcbiAgcmVzcG9uc2VMZW5ndGg6ICdtZWRpdW0nLFxuICBwZXJzb25hbGl0eTogJ2hlbHBmdWwnLFxuICBjb250ZXh0TWVtb3J5OiAxMCxcbiAgdGVtcGVyYXR1cmU6IDAuNyxcbn07XG5cbmNvbnN0IGRlZmF1bHRTZXR0aW5nczogQXBwU2V0dGluZ3MgPSB7XG4gIHZvaWNlOiBkZWZhdWx0Vm9pY2VTZXR0aW5ncyxcbiAgdWk6IGRlZmF1bHRVSVNldHRpbmdzLFxuICBhaTogZGVmYXVsdEFJU2V0dGluZ3MsXG59O1xuXG5pbnRlcmZhY2UgU2V0dGluZ3NTdGF0ZSBleHRlbmRzIFNldHRpbmdzU3RvcmUge1xuICAvLyBBZGRpdGlvbmFsIHN0YXRlXG4gIGlzTG9hZGluZzogYm9vbGVhbjtcbiAgaGFzVW5zYXZlZENoYW5nZXM6IGJvb2xlYW47XG4gIGxhc3RTYXZlZD86IHN0cmluZztcbn1cblxuZXhwb3J0IGNvbnN0IHVzZVNldHRpbmdzU3RvcmUgPSBjcmVhdGU8U2V0dGluZ3NTdGF0ZT4oKShcbiAgZGV2dG9vbHMoXG4gICAgcGVyc2lzdChcbiAgICAgIChzZXQsIGdldCkgPT4gKHtcbiAgICAgICAgLy8gSW5pdGlhbCBzdGF0ZVxuICAgICAgICBzZXR0aW5nczogZGVmYXVsdFNldHRpbmdzLFxuICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICBoYXNVbnNhdmVkQ2hhbmdlczogZmFsc2UsXG4gICAgICAgIGxhc3RTYXZlZDogdW5kZWZpbmVkLFxuXG4gICAgICAgIC8vIEFjdGlvbnNcbiAgICAgICAgdXBkYXRlVm9pY2VTZXR0aW5nczogKG5ld1NldHRpbmdzOiBQYXJ0aWFsPFZvaWNlU2V0dGluZ3M+KSA9PiB7XG4gICAgICAgICAgc2V0KHN0YXRlID0+ICh7XG4gICAgICAgICAgICBzZXR0aW5nczoge1xuICAgICAgICAgICAgICAuLi5zdGF0ZS5zZXR0aW5ncyxcbiAgICAgICAgICAgICAgdm9pY2U6IHtcbiAgICAgICAgICAgICAgICAuLi5zdGF0ZS5zZXR0aW5ncy52b2ljZSxcbiAgICAgICAgICAgICAgICAuLi5uZXdTZXR0aW5ncyxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBoYXNVbnNhdmVkQ2hhbmdlczogdHJ1ZSxcbiAgICAgICAgICB9KSk7XG5cbiAgICAgICAgICAvLyBBdXRvLXNhdmUgYWZ0ZXIgYSBkZWxheVxuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgZ2V0KCkuc2F2ZVNldHRpbmdzKCk7XG4gICAgICAgICAgfSwgMTAwMCk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgdXBkYXRlVUlTZXR0aW5nczogKG5ld1NldHRpbmdzOiBQYXJ0aWFsPFVJU2V0dGluZ3M+KSA9PiB7XG4gICAgICAgICAgc2V0KHN0YXRlID0+ICh7XG4gICAgICAgICAgICBzZXR0aW5nczoge1xuICAgICAgICAgICAgICAuLi5zdGF0ZS5zZXR0aW5ncyxcbiAgICAgICAgICAgICAgdWk6IHtcbiAgICAgICAgICAgICAgICAuLi5zdGF0ZS5zZXR0aW5ncy51aSxcbiAgICAgICAgICAgICAgICAuLi5uZXdTZXR0aW5ncyxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBoYXNVbnNhdmVkQ2hhbmdlczogdHJ1ZSxcbiAgICAgICAgICB9KSk7XG5cbiAgICAgICAgICAvLyBBcHBseSB0aGVtZSBpbW1lZGlhdGVseVxuICAgICAgICAgIGlmIChuZXdTZXR0aW5ncy50aGVtZSkge1xuICAgICAgICAgICAgYXBwbHlUaGVtZShuZXdTZXR0aW5ncy50aGVtZSk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gQXBwbHkgZm9udCBzaXplIGltbWVkaWF0ZWx5XG4gICAgICAgICAgaWYgKG5ld1NldHRpbmdzLmZvbnRTaXplKSB7XG4gICAgICAgICAgICBhcHBseUZvbnRTaXplKG5ld1NldHRpbmdzLmZvbnRTaXplKTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBBdXRvLXNhdmUgYWZ0ZXIgYSBkZWxheVxuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgZ2V0KCkuc2F2ZVNldHRpbmdzKCk7XG4gICAgICAgICAgfSwgMTAwMCk7XG4gICAgICAgIH0sXG5cbiAgICAgICAgdXBkYXRlQUlTZXR0aW5nczogKG5ld1NldHRpbmdzOiBQYXJ0aWFsPEFwcFNldHRpbmdzWydhaSddPikgPT4ge1xuICAgICAgICAgIHNldChzdGF0ZSA9PiAoe1xuICAgICAgICAgICAgc2V0dGluZ3M6IHtcbiAgICAgICAgICAgICAgLi4uc3RhdGUuc2V0dGluZ3MsXG4gICAgICAgICAgICAgIGFpOiB7XG4gICAgICAgICAgICAgICAgLi4uc3RhdGUuc2V0dGluZ3MuYWksXG4gICAgICAgICAgICAgICAgLi4ubmV3U2V0dGluZ3MsXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgaGFzVW5zYXZlZENoYW5nZXM6IHRydWUsXG4gICAgICAgICAgfSkpO1xuXG4gICAgICAgICAgLy8gQXV0by1zYXZlIGFmdGVyIGEgZGVsYXlcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgIGdldCgpLnNhdmVTZXR0aW5ncygpO1xuICAgICAgICAgIH0sIDEwMDApO1xuICAgICAgICB9LFxuXG4gICAgICAgIHJlc2V0U2V0dGluZ3M6ICgpID0+IHtcbiAgICAgICAgICBzZXQoe1xuICAgICAgICAgICAgc2V0dGluZ3M6IGRlZmF1bHRTZXR0aW5ncyxcbiAgICAgICAgICAgIGhhc1Vuc2F2ZWRDaGFuZ2VzOiB0cnVlLFxuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgLy8gQXBwbHkgZGVmYXVsdCB0aGVtZSBhbmQgZm9udCBzaXplXG4gICAgICAgICAgYXBwbHlUaGVtZShkZWZhdWx0U2V0dGluZ3MudWkudGhlbWUpO1xuICAgICAgICAgIGFwcGx5Rm9udFNpemUoZGVmYXVsdFNldHRpbmdzLnVpLmZvbnRTaXplKTtcblxuICAgICAgICAgIC8vIFNhdmUgaW1tZWRpYXRlbHlcbiAgICAgICAgICBnZXQoKS5zYXZlU2V0dGluZ3MoKTtcbiAgICAgICAgfSxcblxuICAgICAgICBsb2FkU2V0dGluZ3M6ICgpID0+IHtcbiAgICAgICAgICBzZXQoeyBpc0xvYWRpbmc6IHRydWUgfSk7XG5cbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gU2V0dGluZ3MgYXJlIGF1dG9tYXRpY2FsbHkgbG9hZGVkIGJ5IHBlcnNpc3QgbWlkZGxld2FyZVxuICAgICAgICAgICAgY29uc3Qgc3RhdGUgPSBnZXQoKTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgLy8gQXBwbHkgY3VycmVudCBzZXR0aW5ncyB0byBET01cbiAgICAgICAgICAgIGFwcGx5VGhlbWUoc3RhdGUuc2V0dGluZ3MudWkudGhlbWUpO1xuICAgICAgICAgICAgYXBwbHlGb250U2l6ZShzdGF0ZS5zZXR0aW5ncy51aS5mb250U2l6ZSk7XG5cbiAgICAgICAgICAgIHNldCh7IFxuICAgICAgICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICAgICAgICBoYXNVbnNhdmVkQ2hhbmdlczogZmFsc2UsXG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBzZXR0aW5nczonLCBlcnJvcik7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIEZhbGxiYWNrIHRvIGRlZmF1bHQgc2V0dGluZ3NcbiAgICAgICAgICAgIHNldCh7XG4gICAgICAgICAgICAgIHNldHRpbmdzOiBkZWZhdWx0U2V0dGluZ3MsXG4gICAgICAgICAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICAgICAgICAgIGhhc1Vuc2F2ZWRDaGFuZ2VzOiBmYWxzZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH1cbiAgICAgICAgfSxcblxuICAgICAgICBzYXZlU2V0dGluZ3M6ICgpID0+IHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gU2V0dGluZ3MgYXJlIGF1dG9tYXRpY2FsbHkgc2F2ZWQgYnkgcGVyc2lzdCBtaWRkbGV3YXJlXG4gICAgICAgICAgICBzZXQoe1xuICAgICAgICAgICAgICBoYXNVbnNhdmVkQ2hhbmdlczogZmFsc2UsXG4gICAgICAgICAgICAgIGxhc3RTYXZlZDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdTZXR0aW5ncyBzYXZlZCBzdWNjZXNzZnVsbHknKTtcblxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2F2ZSBzZXR0aW5nczonLCBlcnJvcik7XG4gICAgICAgICAgfVxuICAgICAgICB9LFxuXG4gICAgICAgIC8vIEFkZGl0aW9uYWwgaGVscGVyIG1ldGhvZHNcbiAgICAgICAgZ2V0Vm9pY2VCeUlkOiAodm9pY2VJZDogc3RyaW5nKSA9PiB7XG4gICAgICAgICAgLy8gVGhpcyB3b3VsZCB0eXBpY2FsbHkgZmV0Y2ggZnJvbSBhdmFpbGFibGUgdm9pY2VzXG4gICAgICAgICAgLy8gRm9yIG5vdywgcmV0dXJuIGEgZGVmYXVsdCB2b2ljZSBzdHJ1Y3R1cmVcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgaWQ6IHZvaWNlSWQsXG4gICAgICAgICAgICBuYW1lOiB2b2ljZUlkLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgdm9pY2VJZC5zbGljZSgxKSxcbiAgICAgICAgICAgIGxhbmd1YWdlOiAnZW4nLFxuICAgICAgICAgICAgZ2VuZGVyOiAnbmV1dHJhbCcgYXMgY29uc3QsXG4gICAgICAgICAgfTtcbiAgICAgICAgfSxcblxuICAgICAgICBleHBvcnRTZXR0aW5nczogKCkgPT4ge1xuICAgICAgICAgIGNvbnN0IHN0YXRlID0gZ2V0KCk7XG4gICAgICAgICAgY29uc3QgZXhwb3J0RGF0YSA9IHtcbiAgICAgICAgICAgIHNldHRpbmdzOiBzdGF0ZS5zZXR0aW5ncyxcbiAgICAgICAgICAgIGV4cG9ydGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgIHZlcnNpb246ICcxLjAuMCcsXG4gICAgICAgICAgfTtcblxuICAgICAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbSlNPTi5zdHJpbmdpZnkoZXhwb3J0RGF0YSwgbnVsbCwgMildLCB7XG4gICAgICAgICAgICB0eXBlOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpO1xuICAgICAgICAgIGNvbnN0IGEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7XG4gICAgICAgICAgYS5ocmVmID0gdXJsO1xuICAgICAgICAgIGEuZG93bmxvYWQgPSBgdm9pY2UtY2hhdC1zZXR0aW5ncy0ke25ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdfS5qc29uYDtcbiAgICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGEpO1xuICAgICAgICAgIGEuY2xpY2soKTtcbiAgICAgICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGEpO1xuICAgICAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwodXJsKTtcbiAgICAgICAgfSxcblxuICAgICAgICBpbXBvcnRTZXR0aW5nczogKGZpbGU6IEZpbGUpID0+IHtcbiAgICAgICAgICByZXR1cm4gbmV3IFByb21pc2U8dm9pZD4oKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgcmVhZGVyID0gbmV3IEZpbGVSZWFkZXIoKTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgcmVhZGVyLm9ubG9hZCA9IChldmVudCkgPT4ge1xuICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBKU09OLnBhcnNlKGV2ZW50LnRhcmdldD8ucmVzdWx0IGFzIHN0cmluZyk7XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgaWYgKGRhdGEuc2V0dGluZ3MgJiYgZGF0YS52ZXJzaW9uKSB7XG4gICAgICAgICAgICAgICAgICBzZXQoe1xuICAgICAgICAgICAgICAgICAgICBzZXR0aW5nczoge1xuICAgICAgICAgICAgICAgICAgICAgIC4uLmRlZmF1bHRTZXR0aW5ncyxcbiAgICAgICAgICAgICAgICAgICAgICAuLi5kYXRhLnNldHRpbmdzLFxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICBoYXNVbnNhdmVkQ2hhbmdlczogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAgICAgICAvLyBBcHBseSBpbXBvcnRlZCBzZXR0aW5nc1xuICAgICAgICAgICAgICAgICAgY29uc3QgbmV3U2V0dGluZ3MgPSBnZXQoKS5zZXR0aW5ncztcbiAgICAgICAgICAgICAgICAgIGFwcGx5VGhlbWUobmV3U2V0dGluZ3MudWkudGhlbWUpO1xuICAgICAgICAgICAgICAgICAgYXBwbHlGb250U2l6ZShuZXdTZXR0aW5ncy51aS5mb250U2l6ZSk7XG5cbiAgICAgICAgICAgICAgICAgIGdldCgpLnNhdmVTZXR0aW5ncygpO1xuICAgICAgICAgICAgICAgICAgcmVzb2x2ZSgpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICByZWplY3QobmV3IEVycm9yKCdJbnZhbGlkIHNldHRpbmdzIGZpbGUgZm9ybWF0JykpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICByZWplY3QobmV3IEVycm9yKCdGYWlsZWQgdG8gcGFyc2Ugc2V0dGluZ3MgZmlsZScpKTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcblxuICAgICAgICAgICAgcmVhZGVyLm9uZXJyb3IgPSAoKSA9PiB7XG4gICAgICAgICAgICAgIHJlamVjdChuZXcgRXJyb3IoJ0ZhaWxlZCB0byByZWFkIHNldHRpbmdzIGZpbGUnKSk7XG4gICAgICAgICAgICB9O1xuXG4gICAgICAgICAgICByZWFkZXIucmVhZEFzVGV4dChmaWxlKTtcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSxcblxuICAgICAgICB2YWxpZGF0ZVNldHRpbmdzOiAoc2V0dGluZ3M6IFBhcnRpYWw8QXBwU2V0dGluZ3M+KTogYm9vbGVhbiA9PiB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIEJhc2ljIHZhbGlkYXRpb25cbiAgICAgICAgICAgIGlmIChzZXR0aW5ncy52b2ljZT8uc3BlZWQgJiYgKHNldHRpbmdzLnZvaWNlLnNwZWVkIDwgMC4yNSB8fCBzZXR0aW5ncy52b2ljZS5zcGVlZCA+IDQuMCkpIHtcbiAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAoc2V0dGluZ3MuYWk/LnRlbXBlcmF0dXJlICYmIChzZXR0aW5ncy5haS50ZW1wZXJhdHVyZSA8IDAgfHwgc2V0dGluZ3MuYWkudGVtcGVyYXR1cmUgPiAyKSkge1xuICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlmIChzZXR0aW5ncy5haT8uY29udGV4dE1lbW9yeSAmJiAoc2V0dGluZ3MuYWkuY29udGV4dE1lbW9yeSA8IDEgfHwgc2V0dGluZ3MuYWkuY29udGV4dE1lbW9yeSA+IDUwKSkge1xuICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgfSksXG4gICAgICB7XG4gICAgICAgIG5hbWU6ICdzZXR0aW5ncy1zdG9yZScsXG4gICAgICAgIHZlcnNpb246IDEsXG4gICAgICAgIG1pZ3JhdGU6IChwZXJzaXN0ZWRTdGF0ZTogYW55LCB2ZXJzaW9uOiBudW1iZXIpID0+IHtcbiAgICAgICAgICAvLyBIYW5kbGUgc2V0dGluZ3MgbWlncmF0aW9uIGJldHdlZW4gdmVyc2lvbnNcbiAgICAgICAgICBpZiAodmVyc2lvbiA9PT0gMCkge1xuICAgICAgICAgICAgLy8gTWlncmF0ZSBmcm9tIHZlcnNpb24gMCB0byAxXG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAuLi5kZWZhdWx0U2V0dGluZ3MsXG4gICAgICAgICAgICAgIC4uLnBlcnNpc3RlZFN0YXRlLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICB9XG4gICAgICAgICAgcmV0dXJuIHBlcnNpc3RlZFN0YXRlO1xuICAgICAgICB9LFxuICAgICAgfVxuICAgICksXG4gICAge1xuICAgICAgbmFtZTogJ3NldHRpbmdzLXN0b3JlJyxcbiAgICB9XG4gIClcbik7XG5cbi8vIEhlbHBlciBmdW5jdGlvbnMgZm9yIGFwcGx5aW5nIHNldHRpbmdzIHRvIERPTVxuZnVuY3Rpb24gYXBwbHlUaGVtZSh0aGVtZTogVUlTZXR0aW5nc1sndGhlbWUnXSkge1xuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybjtcblxuICBjb25zdCByb290ID0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50O1xuICBcbiAgaWYgKHRoZW1lID09PSAnZGFyaycpIHtcbiAgICByb290LmNsYXNzTGlzdC5hZGQoJ2RhcmsnKTtcbiAgfSBlbHNlIGlmICh0aGVtZSA9PT0gJ2xpZ2h0Jykge1xuICAgIHJvb3QuY2xhc3NMaXN0LnJlbW92ZSgnZGFyaycpO1xuICB9IGVsc2Uge1xuICAgIC8vIFN5c3RlbSB0aGVtZVxuICAgIGNvbnN0IHByZWZlcnNEYXJrID0gd2luZG93Lm1hdGNoTWVkaWEoJyhwcmVmZXJzLWNvbG9yLXNjaGVtZTogZGFyayknKS5tYXRjaGVzO1xuICAgIGlmIChwcmVmZXJzRGFyaykge1xuICAgICAgcm9vdC5jbGFzc0xpc3QuYWRkKCdkYXJrJyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJvb3QuY2xhc3NMaXN0LnJlbW92ZSgnZGFyaycpO1xuICAgIH1cbiAgfVxufVxuXG5mdW5jdGlvbiBhcHBseUZvbnRTaXplKGZvbnRTaXplOiBVSVNldHRpbmdzWydmb250U2l6ZSddKSB7XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuO1xuXG4gIGNvbnN0IHJvb3QgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQ7XG4gIFxuICAvLyBSZW1vdmUgZXhpc3RpbmcgZm9udCBzaXplIGNsYXNzZXNcbiAgcm9vdC5jbGFzc0xpc3QucmVtb3ZlKCd0ZXh0LXNtJywgJ3RleHQtYmFzZScsICd0ZXh0LWxnJyk7XG4gIFxuICAvLyBBcHBseSBuZXcgZm9udCBzaXplXG4gIHN3aXRjaCAoZm9udFNpemUpIHtcbiAgICBjYXNlICdzbWFsbCc6XG4gICAgICByb290LmNsYXNzTGlzdC5hZGQoJ3RleHQtc20nKTtcbiAgICAgIGJyZWFrO1xuICAgIGNhc2UgJ2xhcmdlJzpcbiAgICAgIHJvb3QuY2xhc3NMaXN0LmFkZCgndGV4dC1sZycpO1xuICAgICAgYnJlYWs7XG4gICAgZGVmYXVsdDpcbiAgICAgIHJvb3QuY2xhc3NMaXN0LmFkZCgndGV4dC1iYXNlJyk7XG4gICAgICBicmVhaztcbiAgfVxufVxuXG4vLyBJbml0aWFsaXplIHRoZW1lIG9uIGxvYWRcbmlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAvLyBMaXN0ZW4gZm9yIHN5c3RlbSB0aGVtZSBjaGFuZ2VzXG4gIGNvbnN0IG1lZGlhUXVlcnkgPSB3aW5kb3cubWF0Y2hNZWRpYSgnKHByZWZlcnMtY29sb3Itc2NoZW1lOiBkYXJrKScpO1xuICBtZWRpYVF1ZXJ5LmFkZEV2ZW50TGlzdGVuZXIoJ2NoYW5nZScsIChlKSA9PiB7XG4gICAgY29uc3Qgc2V0dGluZ3MgPSB1c2VTZXR0aW5nc1N0b3JlLmdldFN0YXRlKCkuc2V0dGluZ3M7XG4gICAgaWYgKHNldHRpbmdzLnVpLnRoZW1lID09PSAnc3lzdGVtJykge1xuICAgICAgYXBwbHlUaGVtZSgnc3lzdGVtJyk7XG4gICAgfVxuICB9KTtcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGUiLCJkZXZ0b29scyIsInBlcnNpc3QiLCJkZWZhdWx0Vm9pY2VTZXR0aW5ncyIsInZvaWNlIiwiaWQiLCJuYW1lIiwibGFuZ3VhZ2UiLCJnZW5kZXIiLCJzcGVlZCIsImF1dG9QbGF5IiwiZGVmYXVsdFVJU2V0dGluZ3MiLCJ0aGVtZSIsImZvbnRTaXplIiwic2hvd1RpbWVzdGFtcHMiLCJzaG93Q29uZmlkZW5jZSIsImNvbXBhY3RNb2RlIiwiZW5hYmxlQW5pbWF0aW9ucyIsImVuYWJsZVNvdW5kcyIsImRlZmF1bHRBSVNldHRpbmdzIiwicmVzcG9uc2VMZW5ndGgiLCJwZXJzb25hbGl0eSIsImNvbnRleHRNZW1vcnkiLCJ0ZW1wZXJhdHVyZSIsImRlZmF1bHRTZXR0aW5ncyIsInVpIiwiYWkiLCJ1c2VTZXR0aW5nc1N0b3JlIiwic2V0IiwiZ2V0Iiwic2V0dGluZ3MiLCJpc0xvYWRpbmciLCJoYXNVbnNhdmVkQ2hhbmdlcyIsImxhc3RTYXZlZCIsInVuZGVmaW5lZCIsInVwZGF0ZVZvaWNlU2V0dGluZ3MiLCJuZXdTZXR0aW5ncyIsInN0YXRlIiwic2V0VGltZW91dCIsInNhdmVTZXR0aW5ncyIsInVwZGF0ZVVJU2V0dGluZ3MiLCJhcHBseVRoZW1lIiwiYXBwbHlGb250U2l6ZSIsInVwZGF0ZUFJU2V0dGluZ3MiLCJyZXNldFNldHRpbmdzIiwibG9hZFNldHRpbmdzIiwiZXJyb3IiLCJjb25zb2xlIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwibG9nIiwiZ2V0Vm9pY2VCeUlkIiwidm9pY2VJZCIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJleHBvcnRTZXR0aW5ncyIsImV4cG9ydERhdGEiLCJleHBvcnRlZEF0IiwidmVyc2lvbiIsImJsb2IiLCJCbG9iIiwiSlNPTiIsInN0cmluZ2lmeSIsInR5cGUiLCJ1cmwiLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJhIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiaHJlZiIsImRvd25sb2FkIiwic3BsaXQiLCJib2R5IiwiYXBwZW5kQ2hpbGQiLCJjbGljayIsInJlbW92ZUNoaWxkIiwicmV2b2tlT2JqZWN0VVJMIiwiaW1wb3J0U2V0dGluZ3MiLCJmaWxlIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyZWplY3QiLCJyZWFkZXIiLCJGaWxlUmVhZGVyIiwib25sb2FkIiwiZXZlbnQiLCJkYXRhIiwicGFyc2UiLCJ0YXJnZXQiLCJyZXN1bHQiLCJFcnJvciIsIm9uZXJyb3IiLCJyZWFkQXNUZXh0IiwidmFsaWRhdGVTZXR0aW5ncyIsIm1pZ3JhdGUiLCJwZXJzaXN0ZWRTdGF0ZSIsInJvb3QiLCJkb2N1bWVudEVsZW1lbnQiLCJjbGFzc0xpc3QiLCJhZGQiLCJyZW1vdmUiLCJwcmVmZXJzRGFyayIsIndpbmRvdyIsIm1hdGNoTWVkaWEiLCJtYXRjaGVzIiwibWVkaWFRdWVyeSIsImFkZEV2ZW50TGlzdGVuZXIiLCJlIiwiZ2V0U3RhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/store/settingsStore.ts\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("styled-jsx/style");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "zustand":
/*!**************************!*\
  !*** external "zustand" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("zustand");;

/***/ }),

/***/ "zustand/middleware":
/*!*************************************!*\
  !*** external "zustand/middleware" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("zustand/middleware");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();