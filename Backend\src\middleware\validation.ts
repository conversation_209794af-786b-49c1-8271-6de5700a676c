import Jo<PERSON> from 'joi';
import { Request, Response, NextFunction } from 'express';
import { ValidationError } from './errorHandler';
import { config } from '@/config/environment';

// Validation middleware factory
export function validate(schema: {
  body?: Joi.ObjectSchema;
  params?: Joi.ObjectSchema;
  query?: Joi.ObjectSchema;
  headers?: Joi.ObjectSchema;
}) {
  return (req: Request, res: Response, next: NextFunction) => {
    const errors: any[] = [];

    // Validate request body
    if (schema.body) {
      const { error } = schema.body.validate(req.body, {
        abortEarly: false,
        stripUnknown: true,
      });
      if (error) {
        errors.push(...error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          type: 'body',
        })));
      }
    }

    // Validate request parameters
    if (schema.params) {
      const { error } = schema.params.validate(req.params, {
        abortEarly: false,
        stripUnknown: true,
      });
      if (error) {
        errors.push(...error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          type: 'params',
        })));
      }
    }

    // Validate query parameters
    if (schema.query) {
      const { error } = schema.query.validate(req.query, {
        abortEarly: false,
        stripUnknown: true,
      });
      if (error) {
        errors.push(...error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          type: 'query',
        })));
      }
    }

    // Validate headers
    if (schema.headers) {
      const { error } = schema.headers.validate(req.headers, {
        abortEarly: false,
        stripUnknown: true,
      });
      if (error) {
        errors.push(...error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          type: 'headers',
        })));
      }
    }

    if (errors.length > 0) {
      throw new ValidationError('Request validation failed', errors);
    }

    next();
  };
}

// Common validation schemas
export const commonSchemas = {
  // UUID validation
  uuid: Joi.string().uuid().required(),
  
  // Optional UUID
  optionalUuid: Joi.string().uuid().optional(),
  
  // Pagination
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    offset: Joi.number().integer().min(0).optional(),
  }),
  
  // Sorting
  sort: Joi.object({
    sortBy: Joi.string().valid('createdAt', 'updatedAt', 'title').default('createdAt'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  }),
};

// Chat message validation schemas
export const chatSchemas = {
  sendMessage: {
    body: Joi.object({
      message: Joi.string().min(1).max(1000).required()
        .messages({
          'string.empty': 'Message cannot be empty',
          'string.max': 'Message cannot exceed 1000 characters',
        }),
      conversationId: Joi.string().uuid().optional(),
      context: Joi.object({
        previousMessages: Joi.number().integer().min(0).max(50).default(5),
        userPreferences: Joi.object().optional(),
      }).optional(),
    }),
  },
  
  getHistory: {
    params: Joi.object({
      conversationId: commonSchemas.uuid,
    }),
    query: Joi.object({
      limit: Joi.number().integer().min(1).max(100).default(50),
      offset: Joi.number().integer().min(0).default(0),
      before: Joi.date().iso().optional(),
      after: Joi.date().iso().optional(),
    }),
  },
  
  deleteConversation: {
    params: Joi.object({
      conversationId: commonSchemas.uuid,
    }),
  },
};

// Voice processing validation schemas
export const voiceSchemas = {
  transcribe: {
    body: Joi.object({
      language: Joi.string().valid('en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh').default('en'),
      model: Joi.string().valid('whisper-1').default('whisper-1'),
      temperature: Joi.number().min(0).max(1).default(0),
    }),
  },
  
  synthesize: {
    body: Joi.object({
      text: Joi.string().min(1).max(4000).required()
        .messages({
          'string.empty': 'Text cannot be empty',
          'string.max': 'Text cannot exceed 4000 characters',
        }),
      voice: Joi.string().valid('alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer').default('alloy'),
      speed: Joi.number().min(0.25).max(4.0).default(1.0),
      model: Joi.string().valid('tts-1', 'tts-1-hd').default('tts-1'),
    }),
  },
  
  process: {
    body: Joi.object({
      conversationId: Joi.string().uuid().optional(),
      language: Joi.string().valid('en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh').default('en'),
      voiceSettings: Joi.object({
        voice: Joi.string().valid('alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer').default('alloy'),
        speed: Joi.number().min(0.25).max(4.0).default(1.0),
      }).optional(),
    }),
  },
};

// File upload validation schemas
export const fileSchemas = {
  audioUpload: {
    body: Joi.object({
      metadata: Joi.object().optional(),
    }),
  },
};

// Feedback validation schemas
export const feedbackSchemas = {
  submit: {
    body: Joi.object({
      type: Joi.string().valid('bug', 'feature', 'general', 'rating').required(),
      message: Joi.string().min(1).max(2000).required(),
      rating: Joi.number().integer().min(1).max(5).optional(),
      conversationId: Joi.string().uuid().optional(),
      userAgent: Joi.string().max(500).optional(),
      metadata: Joi.object().optional(),
    }),
  },
};

// Audio file validation
export function validateAudioFile(file: Express.Multer.File): void {
  const allowedFormats = config.ALLOWED_AUDIO_FORMATS.split(',');
  const maxSize = config.MAX_FILE_SIZE;

  // Check file size
  if (file.size > maxSize) {
    throw new ValidationError(`File size exceeds maximum allowed size of ${maxSize} bytes`);
  }

  // Check MIME type
  if (!allowedFormats.includes(file.mimetype)) {
    throw new ValidationError(`Invalid file format. Allowed formats: ${allowedFormats.join(', ')}`);
  }

  // Check file extension
  const allowedExtensions = ['.wav', '.mp3', '.webm', '.ogg', '.m4a'];
  const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
  
  if (!allowedExtensions.includes(fileExtension)) {
    throw new ValidationError(`Invalid file extension. Allowed extensions: ${allowedExtensions.join(', ')}`);
  }
}

// Audio duration validation (would need audio processing library)
export function validateAudioDuration(duration: number): void {
  const maxDuration = config.MAX_RECORDING_DURATION;
  
  if (duration > maxDuration) {
    throw new ValidationError(`Audio duration exceeds maximum allowed duration of ${maxDuration} seconds`);
  }
  
  if (duration < 0.1) {
    throw new ValidationError('Audio duration is too short (minimum 0.1 seconds)');
  }
}

// Request size validation middleware
export function validateRequestSize(maxSize: number = 10 * 1024 * 1024) { // 10MB default
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = parseInt(req.get('Content-Length') || '0');
    
    if (contentLength > maxSize) {
      throw new ValidationError(`Request size exceeds maximum allowed size of ${maxSize} bytes`);
    }
    
    next();
  };
}

// Content type validation middleware
export function validateContentType(allowedTypes: string[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentType = req.get('Content-Type');
    
    if (!contentType || !allowedTypes.some(type => contentType.includes(type))) {
      throw new ValidationError(`Invalid content type. Allowed types: ${allowedTypes.join(', ')}`);
    }
    
    next();
  };
}

// Custom validation helpers
export const validators = {
  // Validate conversation ID exists
  conversationExists: async (conversationId: string): Promise<boolean> => {
    // This would check database for conversation existence
    // Implementation depends on your database service
    return true; // Placeholder
  },
  
  // Validate user permissions
  hasPermission: async (userId: string, resource: string, action: string): Promise<boolean> => {
    // This would check user permissions
    // Implementation depends on your auth system
    return true; // Placeholder
  },
  
  // Validate API key format
  isValidApiKey: (apiKey: string): boolean => {
    return /^sk-[a-zA-Z0-9]{48}$/.test(apiKey);
  },
  
  // Validate URL format
  isValidUrl: (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },
  
  // Validate email format
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },
};

// Sanitization helpers
export const sanitizers = {
  // Remove HTML tags
  stripHtml: (text: string): string => {
    return text.replace(/<[^>]*>/g, '');
  },
  
  // Normalize whitespace
  normalizeWhitespace: (text: string): string => {
    return text.replace(/\s+/g, ' ').trim();
  },
  
  // Remove special characters
  removeSpecialChars: (text: string): string => {
    return text.replace(/[^\w\s]/gi, '');
  },
  
  // Truncate text
  truncate: (text: string, maxLength: number): string => {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  },
};
