{"name": "@types/pg", "version": "8.15.4", "description": "TypeScript definitions for pg", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pg", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "pspeter3", "url": "https://github.com/pspeter3"}], "main": "", "types": "index.d.ts", "exports": {".": {"import": "./index.d.mts", "require": "./index.d.ts"}, "./lib/*": "./lib/*.d.ts", "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/pg"}, "scripts": {}, "dependencies": {"@types/node": "*", "pg-protocol": "*", "pg-types": "^2.2.0"}, "peerDependencies": {}, "typesPublisherContentHash": "1df9604d3c0f5bd0713592a33b19b2d88034938fa9af44ab97f2112ff5efabe2", "typeScriptVersion": "5.1"}