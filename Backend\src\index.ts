import dotenv from 'dotenv';
import { startServer } from './app';
import { config } from '@/config/environment';
import { logger } from '@/utils/logger';
import { connectDatabase } from '@/config/database';

// Load environment variables
dotenv.config();

async function main() {
  try {
    // Log startup information
    logger.info('Starting Voice AI Chat API Server', {
      environment: config.NODE_ENV,
      port: config.PORT,
      nodeVersion: process.version,
    });

    // Connect to database
    logger.info('Connecting to database...');
    await connectDatabase();
    logger.info('Database connected successfully');

    // Start the server
    logger.info('Starting HTTP server...');
    await startServer(config.PORT);

  } catch (error: any) {
    logger.error('Failed to start server:', {
      error: error.message,
      stack: error.stack,
    });
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  logger.error('Unhandled Rejection at:', {
    promise,
    reason: reason?.message || reason,
    stack: reason?.stack,
  });
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error: Error) => {
  logger.error('Uncaught Exception:', {
    error: error.message,
    stack: error.stack,
  });
  process.exit(1);
});

// Start the application
main();
