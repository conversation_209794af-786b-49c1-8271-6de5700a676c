"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "__barrel_optimize__?names=MessageSquare,Mic,Settings,Square!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!**************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=MessageSquare,Mic,Settings,Square!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \**************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageSquare: function() { return /* reexport safe */ _icons_message_square_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; },\n/* harmony export */   Mic: function() { return /* reexport safe */ _icons_mic_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; },\n/* harmony export */   Settings: function() { return /* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]; },\n/* harmony export */   Square: function() { return /* reexport safe */ _icons_square_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]; }\n/* harmony export */ });\n/* harmony import */ var _icons_message_square_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/message-square.js */ \"./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _icons_mic_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/mic.js */ \"./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/settings.js */ \"./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _icons_square_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/square.js */ \"./node_modules/lucide-react/dist/esm/icons/square.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1NZXNzYWdlU3F1YXJlLE1pYyxTZXR0aW5ncyxTcXVhcmUhPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUNvRTtBQUNyQjtBQUNVIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzdiYjkiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1lc3NhZ2VTcXVhcmUgfSBmcm9tIFwiLi9pY29ucy9tZXNzYWdlLXNxdWFyZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1pYyB9IGZyb20gXCIuL2ljb25zL21pYy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNldHRpbmdzIH0gZnJvbSBcIi4vaWNvbnMvc2V0dGluZ3MuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTcXVhcmUgfSBmcm9tIFwiLi9pY29ucy9zcXVhcmUuanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=MessageSquare,Mic,Settings,Square!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n"));

/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/square.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/square.js ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Square; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Square = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Square\", [\n    [\n        \"rect\",\n        {\n            width: \"18\",\n            height: \"18\",\n            x: \"3\",\n            y: \"3\",\n            rx: \"2\",\n            key: \"afitv7\"\n        }\n    ]\n]);\n //# sourceMappingURL=square.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3NxdWFyZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQWFNLE1BQUFBLFNBQVNDLGdFQUFnQkEsQ0FBQyxVQUFVO0lBQ3hDO1FBQUM7UUFBUTtZQUFFQyxPQUFPO1lBQU1DLFFBQVE7WUFBTUMsR0FBRztZQUFLQyxHQUFHO1lBQUtDLElBQUk7WUFBS0MsS0FBSztRQUFBO0tBQVU7Q0FDL0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9pY29ucy9zcXVhcmUudHM/YWNmNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIFNxdWFyZVxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y21WamRDQjNhV1IwYUQwaU1UZ2lJR2hsYVdkb2REMGlNVGdpSUhnOUlqTWlJSGs5SWpNaUlISjRQU0l5SWlBdlBnbzhMM04yWno0SykgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvc3F1YXJlXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgU3F1YXJlID0gY3JlYXRlTHVjaWRlSWNvbignU3F1YXJlJywgW1xuICBbJ3JlY3QnLCB7IHdpZHRoOiAnMTgnLCBoZWlnaHQ6ICcxOCcsIHg6ICczJywgeTogJzMnLCByeDogJzInLCBrZXk6ICdhZml0djcnIH1dLFxuXSk7XG5cbmV4cG9ydCBkZWZhdWx0IFNxdWFyZTtcbiJdLCJuYW1lcyI6WyJTcXVhcmUiLCJjcmVhdGVMdWNpZGVJY29uIiwid2lkdGgiLCJoZWlnaHQiLCJ4IiwieSIsInJ4Iiwia2V5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/lucide-react/dist/esm/icons/square.js\n"));

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Mic_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Mic,Settings,Square!=!lucide-react */ \"__barrel_optimize__?names=MessageSquare,Mic,Settings,Square!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction HomePage() {\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            role: \"assistant\",\n            content: \"Hello! I'm your AI assistant. How can I help you today?\"\n        }\n    ]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 語音錄音功能\n    const handleVoiceRecording = async ()=>{\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\n            alert(\"您的瀏覽器不支援語音功能。\");\n            return;\n        }\n        if (isRecording) {\n            // 如果正在錄音，這裡應該停止錄音\n            setIsRecording(false);\n            return;\n        }\n        try {\n            setIsRecording(true);\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            const mediaRecorder = new MediaRecorder(stream);\n            const chunks = [];\n            mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0) {\n                    chunks.push(event.data);\n                }\n            };\n            mediaRecorder.onstop = async ()=>{\n                const audioBlob = new Blob(chunks, {\n                    type: \"audio/wav\"\n                });\n                // 嘗試發送到後端進行語音轉文字\n                try {\n                    const formData = new FormData();\n                    formData.append(\"audio\", audioBlob, \"recording.wav\");\n                    const response = await fetch(\"http://localhost:8000/api/voice/transcribe\", {\n                        method: \"POST\",\n                        body: formData\n                    });\n                    if (response.ok) {\n                        const data = await response.json();\n                        const transcript = data.data.transcript || \"語音轉錄完成\";\n                        setMessage(transcript);\n                    } else {\n                        setMessage(\"語音已錄製完成 (後端轉錄失敗，請檢查後端服務)\");\n                    }\n                } catch (error) {\n                    console.error(\"語音處理錯誤:\", error);\n                    setMessage(\"語音已錄製完成 (模擬轉錄結果 - 請手動輸入您的問題)\");\n                }\n                // 停止所有音軌\n                stream.getTracks().forEach((track)=>track.stop());\n                setIsRecording(false);\n            };\n            // 開始錄音\n            mediaRecorder.start();\n            // 5秒後自動停止錄音\n            setTimeout(()=>{\n                if (mediaRecorder.state === \"recording\") {\n                    mediaRecorder.stop();\n                }\n            }, 5000);\n        } catch (error) {\n            console.error(\"麥克風錯誤:\", error);\n            alert(\"無法啟動麥克風。請確保您已授權麥克風權限。\");\n            setIsRecording(false);\n        }\n    };\n    const sendMessage = async ()=>{\n        if (!message.trim()) return;\n        const userMessage = {\n            role: \"user\",\n            content: message\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setMessage(\"\");\n        setLoading(true);\n        try {\n            const res = await fetch(\"http://localhost:8000/api/chat/message\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message\n                })\n            });\n            if (res.ok) {\n                const data = await res.json();\n                const aiMessage = {\n                    role: \"assistant\",\n                    content: data.data.response\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        aiMessage\n                    ]);\n            } else {\n                const errorMessage = {\n                    role: \"assistant\",\n                    content: \"Sorry, I encountered an error. Please try again.\"\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }\n        } catch (error) {\n            const errorMessage = {\n                role: \"assistant\",\n                content: \"Cannot connect to the server. Please check your connection.\"\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Voice AI Chat\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"AI-powered voice chat application\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"bg-white border-b border-gray-200 px-4 py-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__.MessageSquare, {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Voice AI Chat\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/simple\",\n                                            className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200\",\n                                            children: \"Simple Test\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 text-gray-600 hover:text-gray-900 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Settings, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto space-y-4\",\n                                    children: [\n                                        messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex \".concat(msg.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg \".concat(msg.role === \"user\" ? \"bg-blue-600 text-white\" : \"bg-white text-gray-900 border border-gray-200\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: msg.content\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this)),\n                                        loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-start\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white border border-gray-200 rounded-lg px-4 py-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"AI is thinking...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border-t border-gray-200 p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: [\n                                        isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center space-x-2 px-4 py-2 bg-red-100 text-red-700 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-red-500 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"正在錄音... (5秒後自動停止)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-1 h-16\",\n                                                children: [\n                                                    ...Array(7)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 rounded-full transition-all duration-300 \".concat(isRecording ? \"bg-red-500 animate-pulse\" : \"bg-gray-300\"),\n                                                        style: {\n                                                            height: isRecording ? \"\".concat(30 + Math.random() * 30, \"px\") : \"\".concat(20 + Math.random() * 20, \"px\")\n                                                        }\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleVoiceRecording,\n                                                disabled: loading,\n                                                className: \"w-16 h-16 rounded-full flex items-center justify-center text-white transition-all duration-300 \".concat(isRecording ? \"bg-red-600 hover:bg-red-700 animate-pulse\" : \"bg-blue-600 hover:bg-blue-700\", \" disabled:bg-gray-400 disabled:cursor-not-allowed\"),\n                                                children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Square, {\n                                                    className: \"w-8 h-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Mic_Settings_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Mic, {\n                                                    className: \"w-8 h-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: isRecording ? \"正在錄音中... 點擊停止或等待自動停止\" : \"點擊麥克風開始語音輸入 (錄音5秒)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: message,\n                                                    onChange: (e)=>setMessage(e.target.value),\n                                                    onKeyPress: handleKeyPress,\n                                                    placeholder: \"Type your message or use voice...\",\n                                                    className: \"flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none\",\n                                                    style: {\n                                                        color: \"#000000\"\n                                                    },\n                                                    rows: 2\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: sendMessage,\n                                                    disabled: loading || !message.trim(),\n                                                    className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\",\n                                                    children: \"Send\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\nodejs_work\\\\AI_CustomerService\\\\Frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(HomePage, \"hJ9jfGuBYf0phepkrt1+aFccOpg=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ })

});