// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String?  @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  conversations Conversation[]
  preferences   UserPreferences?

  @@map("users")
}

model UserPreferences {
  id     String @id @default(cuid())
  userId String @unique

  // Voice Settings
  voiceSettings Json @default("{}")
  // UI Preferences
  uiPreferences Json @default("{}")
  // AI Behavior Settings
  aiSettings Json @default("{}")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_preferences")
}

model Conversation {
  id     String  @id @default(cuid())
  userId String?
  title  String?

  // Metadata
  metadata Json @default("{}")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  user     User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  messages Message[]

  @@map("conversations")
}

model Message {
  id             String @id @default(cuid())
  conversationId String

  // Message Content
  role    MessageRole
  content String

  // Audio Information
  audioUrl      String?
  audioDuration Float?
  audioFormat   String?
  audioSize     Int?

  // Processing Metadata
  metadata Json @default("{}")

  // Transcription Data (for user messages)
  transcript         String?
  transcriptConfidence Float?
  transcriptLanguage String?

  // AI Response Data (for assistant messages)
  tokensUsed    Int?
  responseTime  Float?
  modelUsed     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@map("messages")
}

model AudioFile {
  id       String @id @default(cuid())
  filename String @unique
  
  // File Information
  originalName String
  mimeType     String
  size         Int
  duration     Float?
  
  // Storage Information
  path      String
  url       String?
  
  // Processing Status
  status    AudioFileStatus @default(UPLOADED)

  // Metadata
  metadata Json @default("{}")
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  expiresAt DateTime?

  @@map("audio_files")
}

model Feedback {
  id             String       @id @default(cuid())
  type           FeedbackType
  message        String
  rating         Int?
  conversationId String?

  // User Information
  userAgent String?
  ipAddress String?

  // Metadata
  metadata Json @default("{}")

  createdAt DateTime @default(now())

  @@map("feedback")
}

model ApiUsage {
  id        String   @id @default(cuid())
  endpoint  String
  method    String
  
  // Request Information
  ipAddress    String?
  userAgent    String?
  requestSize  Int?
  responseSize Int?
  responseTime Float?
  statusCode   Int
  
  // Usage Tracking
  tokensUsed Int?
  
  createdAt DateTime @default(now())

  @@map("api_usage")
}

// Enums
enum MessageRole {
  USER
  ASSISTANT
  SYSTEM
}

enum AudioFileStatus {
  UPLOADED
  PROCESSING
  PROCESSED
  FAILED
  EXPIRED
}

enum FeedbackType {
  BUG
  FEATURE
  GENERAL
  RATING
}
