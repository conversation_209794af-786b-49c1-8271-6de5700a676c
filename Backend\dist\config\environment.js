"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
exports.validateEnvironment = validateEnvironment;
const joi_1 = __importDefault(require("joi"));
const logger_1 = require("@/utils/logger");
const envSchema = joi_1.default.object({
    NODE_ENV: joi_1.default.string()
        .valid('development', 'staging', 'production', 'test')
        .default('development'),
    PORT: joi_1.default.number()
        .port()
        .default(8000),
    DATABASE_URL: joi_1.default.string()
        .required()
        .description('Database connection URL'),
    GOOGLE_API_KEY: joi_1.default.string()
        .required()
        .description('Google Gemini API key'),
    MODEL_NAME: joi_1.default.string()
        .default('gemini-2.5-flash')
        .description('Gemini model to use'),
    GEMINI_MAX_TOKENS: joi_1.default.number()
        .integer()
        .min(1)
        .max(4000)
        .default(150)
        .description('Maximum tokens for Gemini responses'),
    GEMINI_TEMPERATURE: joi_1.default.number()
        .min(0)
        .max(2)
        .default(0.7)
        .description('Gemini temperature setting'),
    CORS_ORIGIN: joi_1.default.string()
        .default('http://localhost:3000')
        .description('CORS allowed origins (comma-separated)'),
    CORS_CREDENTIALS: joi_1.default.boolean()
        .default(true)
        .description('Enable CORS credentials'),
    RATE_LIMIT_ENABLED: joi_1.default.boolean()
        .default(true)
        .description('Enable rate limiting'),
    RATE_LIMIT_WINDOW_MS: joi_1.default.number()
        .integer()
        .min(1000)
        .default(60000)
        .description('Rate limit window in milliseconds'),
    RATE_LIMIT_MAX_REQUESTS: joi_1.default.number()
        .integer()
        .min(1)
        .default(100)
        .description('Maximum requests per window'),
    RATE_LIMIT_VOICE_MAX_REQUESTS: joi_1.default.number()
        .integer()
        .min(1)
        .default(20)
        .description('Maximum voice requests per window'),
    MAX_FILE_SIZE: joi_1.default.number()
        .integer()
        .min(1024)
        .default(10485760)
        .description('Maximum file upload size in bytes'),
    MAX_RECORDING_DURATION: joi_1.default.number()
        .integer()
        .min(1)
        .max(300)
        .default(60)
        .description('Maximum recording duration in seconds'),
    UPLOAD_DIR: joi_1.default.string()
        .default('./uploads')
        .description('Directory for file uploads'),
    ALLOWED_AUDIO_FORMATS: joi_1.default.string()
        .default('audio/wav,audio/mp3,audio/webm,audio/ogg')
        .description('Allowed audio MIME types (comma-separated)'),
    LOG_LEVEL: joi_1.default.string()
        .valid('error', 'warn', 'info', 'debug')
        .default('info')
        .description('Logging level'),
    LOG_FILE: joi_1.default.string()
        .default('./logs/app.log')
        .description('Log file path'),
    JWT_SECRET: joi_1.default.string()
        .min(32)
        .description('JWT secret key (optional for MVP)'),
    JWT_EXPIRES_IN: joi_1.default.string()
        .default('24h')
        .description('JWT expiration time'),
    BCRYPT_ROUNDS: joi_1.default.number()
        .integer()
        .min(8)
        .max(15)
        .default(12)
        .description('Bcrypt hashing rounds'),
    AUDIO_SAMPLE_RATE: joi_1.default.number()
        .integer()
        .valid(8000, 16000, 22050, 44100, 48000)
        .default(16000)
        .description('Audio sample rate for processing'),
    AUDIO_CHANNELS: joi_1.default.number()
        .integer()
        .valid(1, 2)
        .default(1)
        .description('Audio channels (1=mono, 2=stereo)'),
    AUDIO_BIT_DEPTH: joi_1.default.number()
        .integer()
        .valid(8, 16, 24, 32)
        .default(16)
        .description('Audio bit depth'),
    SENTRY_DSN: joi_1.default.string()
        .uri()
        .description('Sentry DSN for error tracking (optional)'),
    REDIS_URL: joi_1.default.string()
        .uri()
        .description('Redis connection URL (optional)'),
    ENABLE_API_DOCS: joi_1.default.boolean()
        .default(false)
        .description('Enable API documentation endpoint'),
    ENABLE_CORS_LOGGING: joi_1.default.boolean()
        .default(false)
        .description('Enable CORS request logging'),
    ENABLE_REQUEST_LOGGING: joi_1.default.boolean()
        .default(true)
        .description('Enable request logging middleware'),
}).unknown();
function validateEnvironment() {
    const { error, value } = envSchema.validate(process.env, {
        allowUnknown: true,
        stripUnknown: true,
    });
    if (error) {
        logger_1.logger.error('Environment validation failed:', error.details);
        throw new Error(`Environment validation failed: ${error.message}`);
    }
    const safeConfig = { ...value };
    delete safeConfig.OPENAI_API_KEY;
    delete safeConfig.JWT_SECRET;
    delete safeConfig.DATABASE_URL;
    delete safeConfig.SENTRY_DSN;
    delete safeConfig.REDIS_URL;
    logger_1.logger.info('Environment configuration loaded:', safeConfig);
    return value;
}
exports.config = validateEnvironment();
//# sourceMappingURL=environment.js.map