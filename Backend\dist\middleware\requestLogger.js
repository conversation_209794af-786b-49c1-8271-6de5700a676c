"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestLogger = requestLogger;
exports.filterSensitiveData = filterSensitiveData;
exports.requestBodyLogger = requestBodyLogger;
exports.apiUsageTracker = apiUsageTracker;
exports.performanceMonitor = performanceMonitor;
exports.errorTracker = errorTracker;
exports.healthCheckFilter = healthCheckFilter;
exports.corsLogger = corsLogger;
const logger_1 = require("@/utils/logger");
const uuid_1 = require("uuid");
function requestLogger(req, res, next) {
    req.requestId = (0, uuid_1.v4)();
    req.startTime = Date.now();
    res.set('X-Request-ID', req.requestId);
    const requestInfo = {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        referer: req.get('Referer'),
        contentType: req.get('Content-Type'),
        contentLength: req.get('Content-Length'),
        acceptLanguage: req.get('Accept-Language'),
        timestamp: new Date().toISOString(),
    };
    logger_1.logger.debug('Request Started', requestInfo);
    const originalJson = res.json;
    res.json = function (body) {
        const responseTime = req.startTime ? Date.now() - req.startTime : 0;
        const responseInfo = {
            requestId: req.requestId,
            statusCode: res.statusCode,
            responseTime: `${responseTime}ms`,
            contentLength: JSON.stringify(body).length,
        };
        if (res.statusCode >= 500) {
            logger_1.logger.error('Request Completed with Server Error', {
                ...requestInfo,
                ...responseInfo,
                responseBody: process.env.NODE_ENV === 'development' ? body : undefined,
            });
        }
        else if (res.statusCode >= 400) {
            logger_1.logger.warn('Request Completed with Client Error', {
                ...requestInfo,
                ...responseInfo,
                responseBody: process.env.NODE_ENV === 'development' ? body : undefined,
            });
        }
        else {
            logger_1.logger.info('Request Completed Successfully', {
                ...requestInfo,
                ...responseInfo,
            });
        }
        return originalJson.call(this, body);
    };
    const originalSend = res.send;
    res.send = function (body) {
        if (!res.headersSent) {
            const responseTime = req.startTime ? Date.now() - req.startTime : 0;
            const responseInfo = {
                requestId: req.requestId,
                statusCode: res.statusCode,
                responseTime: `${responseTime}ms`,
                contentLength: typeof body === 'string' ? body.length : Buffer.byteLength(body),
            };
            logger_1.logger.info('Request Completed', {
                ...requestInfo,
                ...responseInfo,
            });
        }
        return originalSend.call(this, body);
    };
    res.on('finish', () => {
        const responseTime = req.startTime ? Date.now() - req.startTime : 0;
        if (responseTime > 5000) {
            logger_1.logger.warn('Slow Request Detected', {
                requestId: req.requestId,
                method: req.method,
                url: req.originalUrl,
                responseTime: `${responseTime}ms`,
                statusCode: res.statusCode,
            });
        }
        if (res.statusCode === 401) {
            (0, logger_1.logSecurity)('UNAUTHORIZED_ACCESS_ATTEMPT', {
                requestId: req.requestId,
                ip: req.ip,
                url: req.originalUrl,
                userAgent: req.get('User-Agent'),
            });
        }
        else if (res.statusCode === 403) {
            (0, logger_1.logSecurity)('FORBIDDEN_ACCESS_ATTEMPT', {
                requestId: req.requestId,
                ip: req.ip,
                url: req.originalUrl,
                userAgent: req.get('User-Agent'),
            });
        }
        else if (res.statusCode === 429) {
            (0, logger_1.logSecurity)('RATE_LIMIT_EXCEEDED', {
                requestId: req.requestId,
                ip: req.ip,
                url: req.originalUrl,
                userAgent: req.get('User-Agent'),
            });
        }
    });
    res.on('close', () => {
        if (!res.writableFinished) {
            logger_1.logger.warn('Client Disconnected', {
                requestId: req.requestId,
                method: req.method,
                url: req.originalUrl,
                ip: req.ip,
            });
        }
    });
    next();
}
function filterSensitiveData(data) {
    if (!data || typeof data !== 'object') {
        return data;
    }
    const sensitiveFields = [
        'password',
        'token',
        'apiKey',
        'secret',
        'authorization',
        'cookie',
        'session',
    ];
    const filtered = { ...data };
    for (const field of sensitiveFields) {
        if (field in filtered) {
            filtered[field] = '[REDACTED]';
        }
    }
    for (const key in filtered) {
        if (typeof filtered[key] === 'object' && filtered[key] !== null) {
            filtered[key] = filterSensitiveData(filtered[key]);
        }
    }
    return filtered;
}
function requestBodyLogger(req, res, next) {
    if (process.env.NODE_ENV === 'development' && req.body) {
        logger_1.logger.debug('Request Body', {
            requestId: req.requestId,
            body: filterSensitiveData(req.body),
        });
    }
    next();
}
function apiUsageTracker(req, res, next) {
    const usageData = {
        endpoint: req.route?.path || req.path,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date(),
    };
    logger_1.logger.debug('API Usage', usageData);
    next();
}
function performanceMonitor(req, res, next) {
    const startTime = process.hrtime.bigint();
    res.on('finish', () => {
        const endTime = process.hrtime.bigint();
        const duration = Number(endTime - startTime) / 1000000;
        logger_1.logger.debug('Performance Metrics', {
            requestId: req.requestId,
            method: req.method,
            url: req.originalUrl,
            duration: `${duration.toFixed(2)}ms`,
            statusCode: res.statusCode,
            memoryUsage: process.memoryUsage(),
        });
        if (duration > 10000) {
            logger_1.logger.error('Performance Alert: Very Slow Request', {
                requestId: req.requestId,
                method: req.method,
                url: req.originalUrl,
                duration: `${duration.toFixed(2)}ms`,
            });
        }
    });
    next();
}
function errorTracker(error, req, res, next) {
    logger_1.logger.error('Request Error', {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        error: {
            name: error.name,
            message: error.message,
            stack: error.stack,
        },
    });
    next(error);
}
function healthCheckFilter(req, res, next) {
    if (req.path === '/api/health' || req.path === '/health') {
        req.skipLogging = true;
    }
    next();
}
function corsLogger(req, res, next) {
    if (process.env.ENABLE_CORS_LOGGING === 'true') {
        const origin = req.get('Origin');
        const method = req.method;
        if (origin && method === 'OPTIONS') {
            logger_1.logger.debug('CORS Preflight Request', {
                requestId: req.requestId,
                origin,
                method: req.get('Access-Control-Request-Method'),
                headers: req.get('Access-Control-Request-Headers'),
            });
        }
        else if (origin) {
            logger_1.logger.debug('CORS Request', {
                requestId: req.requestId,
                origin,
                method,
            });
        }
    }
    next();
}
exports.default = requestLogger;
//# sourceMappingURL=requestLogger.js.map