import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Volume2, Mic, Globe, Palette, Bell, Shield, Info } from 'lucide-react';
import { clsx } from 'clsx';
import { useSettingsStore } from '@/store/settingsStore';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

type SettingsTab = 'voice' | 'language' | 'appearance' | 'notifications' | 'privacy' | 'about';

export function SettingsModal({ isOpen, onClose }: SettingsModalProps) {
  const [activeTab, setActiveTab] = useState<SettingsTab>('voice');
  const { settings, updateSettings } = useSettingsStore();

  const tabs = [
    { id: 'voice' as const, label: 'Voice & Audio', icon: Volume2 },
    { id: 'language' as const, label: 'Language', icon: Globe },
    { id: 'appearance' as const, label: 'Appearance', icon: Palette },
    { id: 'notifications' as const, label: 'Notifications', icon: Bell },
    { id: 'privacy' as const, label: 'Privacy', icon: Shield },
    { id: 'about' as const, label: 'About', icon: Info },
  ];

  const handleVoiceSettingChange = (key: string, value: any) => {
    updateSettings({
      voice: {
        ...settings.voice,
        [key]: value,
      },
    });
  };

  const handleUISettingChange = (key: string, value: any) => {
    updateSettings({
      ui: {
        ...settings.ui,
        [key]: value,
      },
    });
  };

  const handleAISettingChange = (key: string, value: any) => {
    updateSettings({
      ai: {
        ...settings.ai,
        [key]: value,
      },
    });
  };

  const renderVoiceSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Voice Settings
        </h3>
        
        {/* Voice Selection */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Voice
            </label>
            <select
              value={settings.voice.selectedVoice}
              onChange={(e) => handleVoiceSettingChange('selectedVoice', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="alloy">Alloy (Neutral)</option>
              <option value="echo">Echo (Male)</option>
              <option value="fable">Fable (Neutral)</option>
              <option value="onyx">Onyx (Male)</option>
              <option value="nova">Nova (Female)</option>
              <option value="shimmer">Shimmer (Female)</option>
            </select>
          </div>

          {/* Speech Speed */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Speech Speed: {settings.voice.speechSpeed}x
            </label>
            <input
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              value={settings.voice.speechSpeed}
              onChange={(e) => handleVoiceSettingChange('speechSpeed', parseFloat(e.target.value))}
              className="w-full"
            />
          </div>

          {/* Volume */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Volume: {Math.round(settings.voice.volume * 100)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={settings.voice.volume}
              onChange={(e) => handleVoiceSettingChange('volume', parseFloat(e.target.value))}
              className="w-full"
            />
          </div>

          {/* Auto-play AI responses */}
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Auto-play AI responses
            </label>
            <input
              type="checkbox"
              checked={settings.voice.autoPlay}
              onChange={(e) => handleVoiceSettingChange('autoPlay', e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
          </div>

          {/* Push-to-talk mode */}
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Push-to-talk mode
            </label>
            <input
              type="checkbox"
              checked={settings.voice.pushToTalk}
              onChange={(e) => handleVoiceSettingChange('pushToTalk', e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Audio Settings
        </h3>
        
        {/* Noise Reduction */}
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Noise Reduction
          </label>
          <input
            type="checkbox"
            checked={settings.voice.noiseReduction}
            onChange={(e) => handleVoiceSettingChange('noiseReduction', e.target.checked)}
            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
        </div>

        {/* Echo Cancellation */}
        <div className="flex items-center justify-between mt-4">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Echo Cancellation
          </label>
          <input
            type="checkbox"
            checked={settings.voice.echoCancellation}
            onChange={(e) => handleVoiceSettingChange('echoCancellation', e.target.checked)}
            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
        </div>
      </div>
    </div>
  );

  const renderLanguageSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Language Settings
        </h3>
        
        <div className="space-y-4">
          {/* Interface Language */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Interface Language
            </label>
            <select
              value={settings.ui.language}
              onChange={(e) => handleUISettingChange('language', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="en">English</option>
              <option value="zh">中文</option>
              <option value="es">Español</option>
              <option value="fr">Français</option>
              <option value="de">Deutsch</option>
              <option value="ja">日本語</option>
              <option value="ko">한국어</option>
            </select>
          </div>

          {/* Speech Recognition Language */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Speech Recognition Language
            </label>
            <select
              value={settings.voice.language}
              onChange={(e) => handleVoiceSettingChange('language', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="en-US">English (US)</option>
              <option value="en-GB">English (UK)</option>
              <option value="zh-CN">中文 (简体)</option>
              <option value="zh-TW">中文 (繁體)</option>
              <option value="es-ES">Español</option>
              <option value="fr-FR">Français</option>
              <option value="de-DE">Deutsch</option>
              <option value="ja-JP">日本語</option>
              <option value="ko-KR">한국어</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Appearance Settings
        </h3>
        
        <div className="space-y-4">
          {/* Theme */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Theme
            </label>
            <select
              value={settings.ui.theme}
              onChange={(e) => handleUISettingChange('theme', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="system">System</option>
            </select>
          </div>

          {/* Font Size */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Font Size
            </label>
            <select
              value={settings.ui.fontSize}
              onChange={(e) => handleUISettingChange('fontSize', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
            </select>
          </div>

          {/* Compact Mode */}
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Compact Mode
            </label>
            <input
              type="checkbox"
              checked={settings.ui.compactMode}
              onChange={(e) => handleUISettingChange('compactMode', e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
          </div>

          {/* Show Timestamps */}
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Show Message Timestamps
            </label>
            <input
              type="checkbox"
              checked={settings.ui.showTimestamps}
              onChange={(e) => handleUISettingChange('showTimestamps', e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderAboutSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          About Voice AI Chat
        </h3>
        
        <div className="space-y-4">
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              Version Information
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Version: 1.0.0<br />
              Build: 2025.01.02<br />
              Environment: Development
            </p>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              Features
            </h4>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Voice-to-text transcription</li>
              <li>• AI-powered conversations</li>
              <li>• Text-to-speech synthesis</li>
              <li>• Multi-language support</li>
              <li>• Conversation history</li>
              <li>• Customizable settings</li>
            </ul>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              Technology Stack
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Frontend: React, Next.js, TypeScript<br />
              Backend: Node.js, Express, PostgreSQL<br />
              AI: Google Gemini API<br />
              Voice: Web Speech API
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'voice':
        return renderVoiceSettings();
      case 'language':
        return renderLanguageSettings();
      case 'appearance':
        return renderAppearanceSettings();
      case 'about':
        return renderAboutSettings();
      default:
        return <div>Coming soon...</div>;
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
          
          {/* Modal */}
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  Settings
                </h2>
                <button
                  onClick={onClose}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="flex h-[calc(90vh-120px)]">
                {/* Sidebar */}
                <div className="w-64 bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700">
                  <nav className="p-4 space-y-2">
                    {tabs.map((tab) => {
                      const Icon = tab.icon;
                      return (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={clsx(
                            'w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors',
                            activeTab === tab.id
                              ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
                              : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                          )}
                        >
                          <Icon className="w-5 h-5" />
                          <span className="text-sm font-medium">{tab.label}</span>
                        </button>
                      );
                    })}
                  </nav>
                </div>

                {/* Content */}
                <div className="flex-1 overflow-y-auto p-6">
                  {renderTabContent()}
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}

export default SettingsModal;
