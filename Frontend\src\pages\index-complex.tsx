import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { motion, AnimatePresence } from 'framer-motion';
import { Settings, Menu, X, Mic, MessageSquare } from 'lucide-react';
import { useChatStore } from '@/store/chatStore';
import { useVoiceStore } from '@/store/voiceStore';
import { useSettingsStore } from '@/store/settingsStore';
import { useAudioRecorder } from '@/hooks/useAudioRecorder';
import { useSpeechSynthesis } from '@/hooks/useSpeechSynthesis';
import PushToTalkButton from '@/components/voice/PushToTalkButton';
import VoiceVisualizer from '@/components/voice/VoiceVisualizer';
import TranscriptionDisplay from '@/components/voice/TranscriptionDisplay';
import ChatContainer, { ConversationList } from '@/components/chat/ChatContainer';
import SettingsModal from '@/components/settings/SettingsModal';
import toast from 'react-hot-toast';

export default function HomePage() {
  const [showSidebar, setShowSidebar] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Store hooks
  const {
    messages,
    conversations,
    currentConversation,
    isLoading,
    sendMessage,
    loadConversations,
    loadConversation,
    deleteConversation,
  } = useChatStore();

  const {
    isRecording,
    isProcessing,
    audioLevel,
    transcript,
    confidence,
    error: voiceError,
    startRecording,
    stopRecording,
    processAudio,
    playAudio,
    reset: resetVoice,
  } = useVoiceStore();

  const { settings } = useSettingsStore();

  // Audio recorder hook
  const {
    isRecording: recorderIsRecording,
    audioLevel: recorderAudioLevel,
    startRecording: startAudioRecording,
    stopRecording: stopAudioRecording,
    error: recorderError,
  } = useAudioRecorder();

  // Speech synthesis hook
  const {
    speak,
    stop: stopSpeaking,
    isSpeaking,
    voices,
    error: speechError,
  } = useSpeechSynthesis();

  // Initialize the app
  useEffect(() => {
    const initializeApp = async () => {
      try {
        await loadConversations();
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        toast.error('Failed to load conversations');
        setIsInitialized(true); // Still show the app
      }
    };

    initializeApp();
  }, [loadConversations]);

  // Handle voice recording
  const handleStartRecording = async () => {
    try {
      resetVoice();
      await startRecording();
      await startAudioRecording();
    } catch (error) {
      console.error('Failed to start recording:', error);
      toast.error('Failed to start recording');
    }
  };

  const handleStopRecording = async () => {
    try {
      const audioBlob = await stopAudioRecording();
      await stopRecording();
      
      if (audioBlob) {
        await processAudio(audioBlob);
      }
    } catch (error) {
      console.error('Failed to stop recording:', error);
      toast.error('Failed to process recording');
    }
  };

  // Handle sending messages
  const handleSendMessage = async (message: string) => {
    try {
      const response = await sendMessage(message);
      
      // Auto-play AI response if enabled
      if (settings.voice.autoPlay && response) {
        await speak(response, {
          voice: settings.voice.selectedVoice,
          rate: settings.voice.speechSpeed,
          volume: settings.voice.volume,
        });
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
    }
  };

  // Handle transcript submission
  useEffect(() => {
    if (transcript && !isProcessing && !isRecording) {
      handleSendMessage(transcript);
    }
  }, [transcript, isProcessing, isRecording]);

  // Show loading screen if not initialized
  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading Voice AI Chat...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Voice AI Chat</title>
        <meta name="description" content="AI-powered voice chat application" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowSidebar(!showSidebar)}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors lg:hidden"
              >
                <Menu className="w-5 h-5" />
              </button>
              
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                  <MessageSquare className="w-5 h-5 text-white" />
                </div>
                <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  Voice AI Chat
                </h1>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowSettings(true)}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
              >
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </div>
        </header>

        <div className="flex-1 flex overflow-hidden">
          {/* Sidebar */}
          <AnimatePresence>
            {showSidebar && (
              <motion.div
                className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col"
                initial={{ x: -320 }}
                animate={{ x: 0 }}
                exit={{ x: -320 }}
                transition={{ type: 'spring', damping: 25, stiffness: 300 }}
              >
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      Conversations
                    </h2>
                    <button
                      onClick={() => setShowSidebar(false)}
                      className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 lg:hidden"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                <div className="flex-1 overflow-y-auto">
                  <ConversationList
                    conversations={conversations}
                    currentConversation={currentConversation}
                    onSelectConversation={loadConversation}
                    onDeleteConversation={deleteConversation}
                  />
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Chat Area */}
            <div className="flex-1 overflow-hidden">
              <ChatContainer
                messages={messages}
                isLoading={isLoading}
                onSendMessage={handleSendMessage}
              />
            </div>

            {/* Voice Controls */}
            <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-6">
              <div className="max-w-4xl mx-auto">
                {/* Transcription Display */}
                {(transcript || isRecording || isProcessing) && (
                  <div className="mb-6">
                    <TranscriptionDisplay
                      transcript={transcript}
                      confidence={confidence}
                      isRecording={isRecording}
                      isProcessing={isProcessing}
                    />
                  </div>
                )}

                {/* Voice Visualizer */}
                <div className="mb-6">
                  <VoiceVisualizer
                    audioLevel={audioLevel || recorderAudioLevel}
                    isRecording={isRecording || recorderIsRecording}
                    isProcessing={isProcessing}
                  />
                </div>

                {/* Push to Talk Button */}
                <div className="flex justify-center">
                  <PushToTalkButton
                    isRecording={isRecording || recorderIsRecording}
                    isProcessing={isProcessing}
                    onStartRecording={handleStartRecording}
                    onStopRecording={handleStopRecording}
                    disabled={isLoading}
                  />
                </div>

                {/* Error Display */}
                {(voiceError || recorderError || speechError) && (
                  <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                    <p className="text-sm text-red-600 dark:text-red-400">
                      {voiceError || recorderError || speechError}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Settings Modal */}
        <SettingsModal
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
        />
      </div>
    </>
  );
}
