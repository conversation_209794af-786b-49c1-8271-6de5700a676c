import OpenAI from 'openai';
export declare const openai: OpenAI;
export declare const OPENAI_CONFIG: {
    readonly model: string;
    readonly maxTokens: number;
    readonly temperature: number;
    readonly topP: 1;
    readonly frequencyPenalty: 0;
    readonly presencePenalty: 0;
    readonly systemPrompt: "You are a helpful AI assistant in a voice chat application. \n    Keep your responses conversational, concise, and natural for spoken interaction. \n    Avoid using markdown formatting, special characters, or overly technical language. \n    Respond as if you're having a natural conversation with the user.";
};
export declare const VOICE_CONFIG: {
    readonly model: "tts-1";
    readonly voice: "alloy";
    readonly response_format: "mp3";
    readonly speed: 1;
};
export declare const AVAILABLE_VOICES: readonly [{
    readonly id: "alloy";
    readonly name: "Alloy";
    readonly gender: "neutral";
    readonly language: "en";
}, {
    readonly id: "echo";
    readonly name: "<PERSON>";
    readonly gender: "male";
    readonly language: "en";
}, {
    readonly id: "fable";
    readonly name: "Fable";
    readonly gender: "neutral";
    readonly language: "en";
}, {
    readonly id: "onyx";
    readonly name: "Onyx";
    readonly gender: "male";
    readonly language: "en";
}, {
    readonly id: "nova";
    readonly name: "Nova";
    readonly gender: "female";
    readonly language: "en";
}, {
    readonly id: "shimmer";
    readonly name: "Shimmer";
    readonly gender: "female";
    readonly language: "en";
}];
export declare const SPEECH_CONFIG: {
    readonly model: "whisper-1";
    readonly language: "en";
    readonly response_format: "json";
    readonly temperature: 0;
};
export declare function testOpenAIConnection(): Promise<boolean>;
export declare function generateChatCompletion(messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
}>, options?: {
    maxTokens?: number;
    temperature?: number;
    model?: string;
}): Promise<OpenAI.Chat.Completions.ChatCompletion>;
export declare function generateSpeech(text: string, options?: {
    voice?: typeof AVAILABLE_VOICES[number]['id'];
    speed?: number;
    model?: string;
}): Promise<Buffer>;
export declare function transcribeAudio(audioBuffer: Buffer, options?: {
    language?: string;
    model?: string;
    temperature?: number;
}): Promise<OpenAI.Audio.Transcriptions.Transcription>;
export declare function getOpenAIUsage(): Promise<any>;
export declare function validateModel(model: string): Promise<boolean>;
export declare function handleOpenAIError(error: any): {
    code: string;
    message: string;
    statusCode: number;
};
//# sourceMappingURL=openai.d.ts.map