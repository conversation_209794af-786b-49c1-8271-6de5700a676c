import { useState, useRef, useCallback, useEffect } from 'react';
import { UseAudioRecorderOptions, UseAudioRecorderReturn, RecordingState, AudioRecording } from '@/types';

export function useAudioRecorder(options: UseAudioRecorderOptions = {}): UseAudioRecorderReturn {
  const {
    mimeType = 'audio/webm',
    audioBitsPerSecond = 128000,
    maxDuration = 60,
    onDataAvailable,
    onStop,
    onError,
  } = options;

  const [recordingState, setRecordingState] = useState<RecordingState>({
    isRecording: false,
    isProcessing: false,
    duration: 0,
    audioLevel: 0,
  });

  const [audioLevel, setAudioLevel] = useState(0);
  const [hasPermission, setHasPermission] = useState(false);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const startTimeRef = useRef<number | null>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const chunksRef = useRef<Blob[]>([]);

  // Check for browser support
  const isSupported = typeof navigator !== 'undefined' && 
                     'mediaDevices' in navigator && 
                     'getUserMedia' in navigator.mediaDevices &&
                     typeof MediaRecorder !== 'undefined';

  // Request microphone permission
  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!isSupported) {
      console.error('Audio recording is not supported in this browser');
      return false;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Test successful, clean up and set permission
      stream.getTracks().forEach(track => track.stop());
      setHasPermission(true);
      return true;
    } catch (error: any) {
      console.error('Failed to get microphone permission:', error);
      setHasPermission(false);
      
      let errorMessage = 'Failed to access microphone';
      if (error.name === 'NotAllowedError') {
        errorMessage = 'Microphone permission denied';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'No microphone found';
      }
      
      setRecordingState(prev => ({ ...prev, error: errorMessage }));
      onError?.(new Error(errorMessage));
      return false;
    }
  }, [isSupported, onError]);

  // Start audio level monitoring
  const startAudioLevelMonitoring = useCallback(() => {
    if (!audioContextRef.current || !analyserRef.current) return;

    const analyser = analyserRef.current;
    const dataArray = new Uint8Array(analyser.frequencyBinCount);

    const updateAudioLevel = () => {
      if (!recordingState.isRecording) return;

      analyser.getByteFrequencyData(dataArray);
      
      // Calculate average volume
      const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
      const normalizedLevel = Math.min(average / 128, 1);
      
      setAudioLevel(normalizedLevel);
      setRecordingState(prev => ({ ...prev, audioLevel: normalizedLevel }));
      
      animationFrameRef.current = requestAnimationFrame(updateAudioLevel);
    };

    updateAudioLevel();
  }, [recordingState.isRecording]);

  // Start duration tracking
  const startDurationTracking = useCallback(() => {
    startTimeRef.current = Date.now();
    
    durationIntervalRef.current = setInterval(() => {
      if (startTimeRef.current) {
        const duration = (Date.now() - startTimeRef.current) / 1000;
        setRecordingState(prev => ({ ...prev, duration }));
        
        // Auto-stop at max duration
        if (duration >= maxDuration) {
          stopRecording();
        }
      }
    }, 100);
  }, [maxDuration]);

  // Stop duration tracking
  const stopDurationTracking = useCallback(() => {
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }
    startTimeRef.current = null;
  }, []);

  // Stop audio level monitoring
  const stopAudioLevelMonitoring = useCallback(() => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }
    setAudioLevel(0);
  }, []);

  // Start recording
  const startRecording = useCallback(async (): Promise<void> => {
    if (!isSupported) {
      throw new Error('Audio recording is not supported');
    }

    if (recordingState.isRecording) {
      console.warn('Already recording');
      return;
    }

    try {
      setRecordingState(prev => ({ ...prev, error: undefined }));

      // Get media stream
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000,
        },
      });

      streamRef.current = stream;

      // Set up audio context for level monitoring
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const analyser = audioContext.createAnalyser();
      const source = audioContext.createMediaStreamSource(stream);
      
      analyser.fftSize = 256;
      analyser.smoothingTimeConstant = 0.8;
      source.connect(analyser);

      audioContextRef.current = audioContext;
      analyserRef.current = analyser;

      // Set up media recorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: MediaRecorder.isTypeSupported(mimeType) ? mimeType : 'audio/webm',
        audioBitsPerSecond,
      });

      chunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
          onDataAvailable?.(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(chunksRef.current, { type: mediaRecorder.mimeType });
        const duration = recordingState.duration;
        
        const recording: AudioRecording = {
          blob: audioBlob,
          duration,
          size: audioBlob.size,
          mimeType: mediaRecorder.mimeType,
          url: URL.createObjectURL(audioBlob),
        };

        setRecordingState(prev => ({
          ...prev,
          isRecording: false,
          isProcessing: false,
        }));

        onStop?.(recording);
        cleanup();
      };

      mediaRecorder.onerror = (event) => {
        const error = new Error(`MediaRecorder error: ${(event as any).error}`);
        console.error('MediaRecorder error:', error);
        
        setRecordingState(prev => ({
          ...prev,
          isRecording: false,
          isProcessing: false,
          error: error.message,
        }));

        onError?.(error);
        cleanup();
      };

      mediaRecorderRef.current = mediaRecorder;

      // Start recording
      mediaRecorder.start(100); // Collect data every 100ms

      setRecordingState(prev => ({
        ...prev,
        isRecording: true,
        duration: 0,
        audioLevel: 0,
      }));

      setHasPermission(true);

      // Start monitoring
      startAudioLevelMonitoring();
      startDurationTracking();

      console.log('Recording started');

    } catch (error: any) {
      console.error('Failed to start recording:', error);
      
      let errorMessage = 'Failed to start recording';
      if (error.name === 'NotAllowedError') {
        errorMessage = 'Microphone permission denied';
        setHasPermission(false);
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'No microphone found';
      } else if (error.name === 'NotSupportedError') {
        errorMessage = 'Audio recording not supported';
      }

      setRecordingState(prev => ({
        ...prev,
        isRecording: false,
        error: errorMessage,
      }));

      onError?.(new Error(errorMessage));
      cleanup();
    }
  }, [
    isSupported,
    recordingState.isRecording,
    recordingState.duration,
    mimeType,
    audioBitsPerSecond,
    onDataAvailable,
    onStop,
    onError,
    startAudioLevelMonitoring,
    startDurationTracking,
  ]);

  // Stop recording
  const stopRecording = useCallback(() => {
    if (!recordingState.isRecording || !mediaRecorderRef.current) {
      console.warn('Not currently recording');
      return;
    }

    try {
      setRecordingState(prev => ({ ...prev, isProcessing: true }));
      mediaRecorderRef.current.stop();
      console.log('Recording stopped');
    } catch (error: any) {
      console.error('Failed to stop recording:', error);
      onError?.(error);
      cleanup();
    }
  }, [recordingState.isRecording, onError]);

  // Pause recording
  const pauseRecording = useCallback(() => {
    if (!recordingState.isRecording || !mediaRecorderRef.current) {
      console.warn('Not currently recording');
      return;
    }

    try {
      mediaRecorderRef.current.pause();
      stopDurationTracking();
      stopAudioLevelMonitoring();
      console.log('Recording paused');
    } catch (error: any) {
      console.error('Failed to pause recording:', error);
      onError?.(error);
    }
  }, [recordingState.isRecording, onError, stopDurationTracking, stopAudioLevelMonitoring]);

  // Resume recording
  const resumeRecording = useCallback(() => {
    if (!mediaRecorderRef.current) {
      console.warn('No active recording to resume');
      return;
    }

    try {
      mediaRecorderRef.current.resume();
      startDurationTracking();
      startAudioLevelMonitoring();
      console.log('Recording resumed');
    } catch (error: any) {
      console.error('Failed to resume recording:', error);
      onError?.(error);
    }
  }, [onError, startDurationTracking, startAudioLevelMonitoring]);

  // Cleanup function
  const cleanup = useCallback(() => {
    stopDurationTracking();
    stopAudioLevelMonitoring();

    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    analyserRef.current = null;
    mediaRecorderRef.current = null;
    chunksRef.current = [];
  }, [stopDurationTracking, stopAudioLevelMonitoring]);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    recordingState,
    audioLevel,
    hasPermission,
    requestPermission,
  };
}
