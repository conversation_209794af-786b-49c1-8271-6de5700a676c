import React, { useCallback, useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, Loader2, AlertCircle } from 'lucide-react';
import { clsx } from 'clsx';
import { PushToTalkButtonProps } from '@/types';

export function PushToTalkButton({
  onStartRecording,
  onStopRecording,
  isRecording,
  isProcessing,
  disabled = false,
  size = 'large',
  className,
}: PushToTalkButtonProps) {
  const [isPressed, setIsPressed] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const pressTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Size variants
  const sizeClasses = {
    small: 'w-12 h-12 text-lg',
    medium: 'w-16 h-16 text-xl',
    large: 'w-20 h-20 text-2xl',
  };

  // Handle mouse events
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    if (disabled || isProcessing) return;

    setIsPressed(true);
    onStartRecording();
  }, [disabled, isProcessing, onStartRecording]);

  const handleMouseUp = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    if (disabled || isProcessing) return;

    setIsPressed(false);
    if (isRecording) {
      onStopRecording();
    }
  }, [disabled, isProcessing, isRecording, onStopRecording]);

  const handleMouseLeave = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    if (isPressed && isRecording) {
      setIsPressed(false);
      onStopRecording();
    }
  }, [isPressed, isRecording, onStopRecording]);

  // Handle touch events for mobile
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    if (disabled || isProcessing) return;

    setIsPressed(true);
    onStartRecording();
  }, [disabled, isProcessing, onStartRecording]);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    if (disabled || isProcessing) return;

    setIsPressed(false);
    if (isRecording) {
      onStopRecording();
    }
  }, [disabled, isProcessing, isRecording, onStopRecording]);

  // Handle keyboard events
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === ' ' || e.key === 'Enter') {
      e.preventDefault();
      if (disabled || isProcessing || isPressed) return;

      setIsPressed(true);
      onStartRecording();
    }
  }, [disabled, isProcessing, isPressed, onStartRecording]);

  const handleKeyUp = useCallback((e: React.KeyboardEvent) => {
    if (e.key === ' ' || e.key === 'Enter') {
      e.preventDefault();
      if (disabled || isProcessing) return;

      setIsPressed(false);
      if (isRecording) {
        onStopRecording();
      }
    }
  }, [disabled, isProcessing, isRecording, onStopRecording]);

  // Prevent context menu on long press
  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
  }, []);

  // Show tooltip on hover
  const handleMouseEnter = useCallback(() => {
    if (!disabled) {
      setShowTooltip(true);
    }
  }, [disabled]);

  const handleMouseLeaveTooltip = useCallback(() => {
    setShowTooltip(false);
  }, []);

  // Get button state and styling
  const getButtonState = () => {
    if (disabled) return 'disabled';
    if (isProcessing) return 'processing';
    if (isRecording) return 'recording';
    if (isPressed) return 'pressed';
    return 'idle';
  };

  const buttonState = getButtonState();

  // Button styling based on state
  const getButtonClasses = () => {
    const baseClasses = clsx(
      'relative rounded-full border-4 transition-all duration-200 ease-out',
      'focus:outline-none focus:ring-4 focus:ring-primary-500/50',
      'select-none touch-manipulation',
      sizeClasses[size]
    );

    const stateClasses = {
      idle: 'bg-primary-600 hover:bg-primary-700 border-primary-600 hover:border-primary-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105',
      pressed: 'bg-primary-700 border-primary-700 text-white shadow-inner transform scale-95',
      recording: 'bg-red-600 border-red-600 text-white shadow-lg animate-pulse',
      processing: 'bg-yellow-600 border-yellow-600 text-white shadow-lg',
      disabled: 'bg-gray-400 border-gray-400 text-gray-200 cursor-not-allowed opacity-50',
    };

    return clsx(baseClasses, stateClasses[buttonState], className);
  };

  // Get icon based on state
  const getIcon = () => {
    if (isProcessing) {
      return <Loader2 className="animate-spin" />;
    }
    if (disabled) {
      return <MicOff />;
    }
    if (isRecording) {
      return <Mic className="animate-pulse" />;
    }
    return <Mic />;
  };

  // Get tooltip text
  const getTooltipText = () => {
    if (disabled) return 'Microphone unavailable';
    if (isProcessing) return 'Processing audio...';
    if (isRecording) return 'Release to send';
    return 'Hold to record';
  };

  return (
    <div className="relative inline-block">
      {/* Main button */}
      <motion.button
        ref={buttonRef}
        className={getButtonClasses()}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeaveTooltip}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        onKeyDown={handleKeyDown}
        onKeyUp={handleKeyUp}
        onContextMenu={handleContextMenu}
        disabled={disabled}
        aria-label={getTooltipText()}
        aria-pressed={isRecording}
        type="button"
        whileTap={{ scale: 0.95 }}
        whileHover={{ scale: disabled ? 1 : 1.05 }}
      >
        {/* Background pulse effect for recording */}
        <AnimatePresence>
          {isRecording && (
            <motion.div
              className="absolute inset-0 rounded-full bg-red-400"
              initial={{ scale: 1, opacity: 0.5 }}
              animate={{ scale: 1.5, opacity: 0 }}
              exit={{ scale: 1, opacity: 0 }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: 'easeOut',
              }}
            />
          )}
        </AnimatePresence>

        {/* Icon */}
        <div className="relative z-10 flex items-center justify-center">
          {getIcon()}
        </div>

        {/* Recording indicator */}
        {isRecording && (
          <motion.div
            className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full border-2 border-white"
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1, repeat: Infinity }}
          />
        )}
      </motion.button>

      {/* Tooltip */}
      <AnimatePresence>
        {showTooltip && (
          <motion.div
            className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg whitespace-nowrap z-50"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.2 }}
          >
            {getTooltipText()}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900" />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Instructions text */}
      <motion.div
        className="absolute top-full left-1/2 transform -translate-x-1/2 mt-4 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {isRecording ? (
            <span className="text-red-600 dark:text-red-400 font-medium">
              Release to send
            </span>
          ) : isProcessing ? (
            <span className="text-yellow-600 dark:text-yellow-400 font-medium">
              Processing...
            </span>
          ) : disabled ? (
            <span className="text-gray-500 dark:text-gray-500">
              Microphone unavailable
            </span>
          ) : (
            'Hold to record your message'
          )}
        </p>
        
        {!disabled && !isProcessing && (
          <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
            Press and hold, or use spacebar
          </p>
        )}
      </motion.div>
    </div>
  );
}

export default PushToTalkButton;
