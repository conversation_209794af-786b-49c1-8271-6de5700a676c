import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import path from 'path';

// Import middleware
import { errorHandler } from '@/middleware/errorHandler';
import { requestLogger } from '@/middleware/requestLogger';
import { rateLimiter } from '@/middleware/rateLimiter';

// Import routes
import chatRoutes from '@/routes/chat';
import voiceRoutes from '@/routes/voice';
import healthRoutes from '@/routes/health';
import configRoutes from '@/routes/config';
import feedbackRoutes from '@/routes/feedback';
import audioRoutes from '@/routes/audio';

// Import utilities
import { logger } from '@/utils/logger';
import { validateEnvironment } from '@/config/environment';

// Load environment variables
dotenv.config();

// Validate environment configuration
validateEnvironment();

const app = express();
const PORT = process.env.PORT || 8000;

// Security middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
}));

// CORS configuration
const corsOptions = {
  origin: process.env.CORS_ORIGIN?.split(',') || 'http://localhost:3000',
  credentials: process.env.CORS_CREDENTIALS === 'true',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  maxAge: 86400, // 24 hours
};

app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
if (process.env.ENABLE_REQUEST_LOGGING === 'true') {
  app.use(requestLogger);
}

// Rate limiting middleware
if (process.env.RATE_LIMIT_ENABLED === 'true') {
  app.use(rateLimiter);
}

// Static file serving for audio files
app.use('/api/audio', express.static(path.join(__dirname, '../uploads')));

// API Routes
app.use('/api/health', healthRoutes);
app.use('/api/config', configRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/voice', voiceRoutes);
app.use('/api/feedback', feedbackRoutes);
app.use('/api/audio', audioRoutes);

// API Documentation (development only)
if (process.env.NODE_ENV === 'development' && process.env.ENABLE_API_DOCS === 'true') {
  app.get('/api-docs', (req, res) => {
    res.json({
      title: 'Voice-Enabled AI Chat API',
      version: '1.0.0',
      description: 'API documentation for the Voice-Enabled AI Chat Application',
      endpoints: {
        health: 'GET /api/health',
        config: 'GET /api/config',
        chat: {
          message: 'POST /api/chat/message',
          history: 'GET /api/chat/history/:conversationId',
          delete: 'DELETE /api/chat/history/:conversationId'
        },
        voice: {
          transcribe: 'POST /api/voice/transcribe',
          synthesize: 'POST /api/voice/synthesize',
          process: 'POST /api/voice/process'
        },
        feedback: 'POST /api/feedback',
        audio: {
          upload: 'POST /api/audio/upload',
          get: 'GET /api/audio/:filename'
        }
      },
      documentation: 'See PRD/API_Documentation.md for detailed API documentation'
    });
  });
}

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Voice-Enabled AI Chat API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    documentation: process.env.NODE_ENV === 'development' ? '/api-docs' : undefined
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${req.method} ${req.originalUrl} not found`,
    },
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Graceful shutdown handling
const server = app.listen(PORT, () => {
  logger.info(`🚀 Server running on port ${PORT}`);
  logger.info(`📝 Environment: ${process.env.NODE_ENV}`);
  logger.info(`🔗 CORS Origin: ${process.env.CORS_ORIGIN}`);
  
  if (process.env.NODE_ENV === 'development') {
    logger.info(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
  }
});

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

export default app;
