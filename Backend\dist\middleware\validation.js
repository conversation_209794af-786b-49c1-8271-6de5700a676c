"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sanitizers = exports.validators = exports.feedbackSchemas = exports.fileSchemas = exports.voiceSchemas = exports.chatSchemas = exports.commonSchemas = void 0;
exports.validate = validate;
exports.validateAudioFile = validateAudioFile;
exports.validateAudioDuration = validateAudioDuration;
exports.validateRequestSize = validateRequestSize;
exports.validateContentType = validateContentType;
const joi_1 = __importDefault(require("joi"));
const errorHandler_1 = require("./errorHandler");
const environment_1 = require("@/config/environment");
function validate(schema) {
    return (req, res, next) => {
        const errors = [];
        if (schema.body) {
            const { error } = schema.body.validate(req.body, {
                abortEarly: false,
                stripUnknown: true,
            });
            if (error) {
                errors.push(...error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message,
                    type: 'body',
                })));
            }
        }
        if (schema.params) {
            const { error } = schema.params.validate(req.params, {
                abortEarly: false,
                stripUnknown: true,
            });
            if (error) {
                errors.push(...error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message,
                    type: 'params',
                })));
            }
        }
        if (schema.query) {
            const { error } = schema.query.validate(req.query, {
                abortEarly: false,
                stripUnknown: true,
            });
            if (error) {
                errors.push(...error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message,
                    type: 'query',
                })));
            }
        }
        if (schema.headers) {
            const { error } = schema.headers.validate(req.headers, {
                abortEarly: false,
                stripUnknown: true,
            });
            if (error) {
                errors.push(...error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message,
                    type: 'headers',
                })));
            }
        }
        if (errors.length > 0) {
            throw new errorHandler_1.ValidationError('Request validation failed', errors);
        }
        next();
    };
}
exports.commonSchemas = {
    uuid: joi_1.default.string().uuid().required(),
    optionalUuid: joi_1.default.string().uuid().optional(),
    pagination: joi_1.default.object({
        page: joi_1.default.number().integer().min(1).default(1),
        limit: joi_1.default.number().integer().min(1).max(100).default(20),
        offset: joi_1.default.number().integer().min(0).optional(),
    }),
    sort: joi_1.default.object({
        sortBy: joi_1.default.string().valid('createdAt', 'updatedAt', 'title').default('createdAt'),
        sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc'),
    }),
};
exports.chatSchemas = {
    sendMessage: {
        body: joi_1.default.object({
            message: joi_1.default.string().min(1).max(1000).required()
                .messages({
                'string.empty': 'Message cannot be empty',
                'string.max': 'Message cannot exceed 1000 characters',
            }),
            conversationId: joi_1.default.string().uuid().optional(),
            context: joi_1.default.object({
                previousMessages: joi_1.default.number().integer().min(0).max(50).default(5),
                userPreferences: joi_1.default.object().optional(),
            }).optional(),
        }),
    },
    getHistory: {
        params: joi_1.default.object({
            conversationId: exports.commonSchemas.uuid,
        }),
        query: joi_1.default.object({
            limit: joi_1.default.number().integer().min(1).max(100).default(50),
            offset: joi_1.default.number().integer().min(0).default(0),
            before: joi_1.default.date().iso().optional(),
            after: joi_1.default.date().iso().optional(),
        }),
    },
    deleteConversation: {
        params: joi_1.default.object({
            conversationId: exports.commonSchemas.uuid,
        }),
    },
};
exports.voiceSchemas = {
    transcribe: {
        body: joi_1.default.object({
            language: joi_1.default.string().valid('en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh').default('en'),
            model: joi_1.default.string().valid('whisper-1').default('whisper-1'),
            temperature: joi_1.default.number().min(0).max(1).default(0),
        }),
    },
    synthesize: {
        body: joi_1.default.object({
            text: joi_1.default.string().min(1).max(4000).required()
                .messages({
                'string.empty': 'Text cannot be empty',
                'string.max': 'Text cannot exceed 4000 characters',
            }),
            voice: joi_1.default.string().valid('alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer').default('alloy'),
            speed: joi_1.default.number().min(0.25).max(4.0).default(1.0),
            model: joi_1.default.string().valid('tts-1', 'tts-1-hd').default('tts-1'),
        }),
    },
    process: {
        body: joi_1.default.object({
            conversationId: joi_1.default.string().uuid().optional(),
            language: joi_1.default.string().valid('en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh').default('en'),
            voiceSettings: joi_1.default.object({
                voice: joi_1.default.string().valid('alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer').default('alloy'),
                speed: joi_1.default.number().min(0.25).max(4.0).default(1.0),
            }).optional(),
        }),
    },
};
exports.fileSchemas = {
    audioUpload: {
        body: joi_1.default.object({
            metadata: joi_1.default.object().optional(),
        }),
    },
};
exports.feedbackSchemas = {
    submit: {
        body: joi_1.default.object({
            type: joi_1.default.string().valid('bug', 'feature', 'general', 'rating').required(),
            message: joi_1.default.string().min(1).max(2000).required(),
            rating: joi_1.default.number().integer().min(1).max(5).optional(),
            conversationId: joi_1.default.string().uuid().optional(),
            userAgent: joi_1.default.string().max(500).optional(),
            metadata: joi_1.default.object().optional(),
        }),
    },
};
function validateAudioFile(file) {
    const allowedFormats = environment_1.config.ALLOWED_AUDIO_FORMATS.split(',');
    const maxSize = environment_1.config.MAX_FILE_SIZE;
    if (file.size > maxSize) {
        throw new errorHandler_1.ValidationError(`File size exceeds maximum allowed size of ${maxSize} bytes`);
    }
    if (!allowedFormats.includes(file.mimetype)) {
        throw new errorHandler_1.ValidationError(`Invalid file format. Allowed formats: ${allowedFormats.join(', ')}`);
    }
    const allowedExtensions = ['.wav', '.mp3', '.webm', '.ogg', '.m4a'];
    const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
    if (!allowedExtensions.includes(fileExtension)) {
        throw new errorHandler_1.ValidationError(`Invalid file extension. Allowed extensions: ${allowedExtensions.join(', ')}`);
    }
}
function validateAudioDuration(duration) {
    const maxDuration = environment_1.config.MAX_RECORDING_DURATION;
    if (duration > maxDuration) {
        throw new errorHandler_1.ValidationError(`Audio duration exceeds maximum allowed duration of ${maxDuration} seconds`);
    }
    if (duration < 0.1) {
        throw new errorHandler_1.ValidationError('Audio duration is too short (minimum 0.1 seconds)');
    }
}
function validateRequestSize(maxSize = 10 * 1024 * 1024) {
    return (req, res, next) => {
        const contentLength = parseInt(req.get('Content-Length') || '0');
        if (contentLength > maxSize) {
            throw new errorHandler_1.ValidationError(`Request size exceeds maximum allowed size of ${maxSize} bytes`);
        }
        next();
    };
}
function validateContentType(allowedTypes) {
    return (req, res, next) => {
        const contentType = req.get('Content-Type');
        if (!contentType || !allowedTypes.some(type => contentType.includes(type))) {
            throw new errorHandler_1.ValidationError(`Invalid content type. Allowed types: ${allowedTypes.join(', ')}`);
        }
        next();
    };
}
exports.validators = {
    conversationExists: async (conversationId) => {
        return true;
    },
    hasPermission: async (userId, resource, action) => {
        return true;
    },
    isValidApiKey: (apiKey) => {
        return /^sk-[a-zA-Z0-9]{48}$/.test(apiKey);
    },
    isValidUrl: (url) => {
        try {
            new URL(url);
            return true;
        }
        catch {
            return false;
        }
    },
    isValidEmail: (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
};
exports.sanitizers = {
    stripHtml: (text) => {
        return text.replace(/<[^>]*>/g, '');
    },
    normalizeWhitespace: (text) => {
        return text.replace(/\s+/g, ' ').trim();
    },
    removeSpecialChars: (text) => {
        return text.replace(/[^\w\s]/gi, '');
    },
    truncate: (text, maxLength) => {
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    },
};
//# sourceMappingURL=validation.js.map