import OpenAI from 'openai';
import { logger } from '@/utils/logger';
import { config } from './environment';

// Initialize OpenAI client
export const openai = new OpenAI({
  apiKey: config.OPENAI_API_KEY,
  timeout: 30000, // 30 seconds timeout
  maxRetries: 3,
});

// OpenAI configuration constants
export const OPENAI_CONFIG = {
  model: config.OPENAI_MODEL,
  maxTokens: config.OPENAI_MAX_TOKENS,
  temperature: config.OPENAI_TEMPERATURE,
  topP: 1,
  frequencyPenalty: 0,
  presencePenalty: 0,
  systemPrompt: `You are a helpful AI assistant in a voice chat application. 
    Keep your responses conversational, concise, and natural for spoken interaction. 
    Avoid using markdown formatting, special characters, or overly technical language. 
    Respond as if you're having a natural conversation with the user.`,
} as const;

// Voice synthesis configuration
export const VOICE_CONFIG = {
  model: 'tts-1',
  voice: 'alloy' as const,
  response_format: 'mp3' as const,
  speed: 1.0,
} as const;

// Available voice options for text-to-speech
export const AVAILABLE_VOICES = [
  { id: 'alloy', name: '<PERSON><PERSON>', gender: 'neutral', language: 'en' },
  { id: 'echo', name: 'Echo', gender: 'male', language: 'en' },
  { id: 'fable', name: 'Fable', gender: 'neutral', language: 'en' },
  { id: 'onyx', name: 'Onyx', gender: 'male', language: 'en' },
  { id: 'nova', name: 'Nova', gender: 'female', language: 'en' },
  { id: 'shimmer', name: 'Shimmer', gender: 'female', language: 'en' },
] as const;

// Speech-to-text configuration
export const SPEECH_CONFIG = {
  model: 'whisper-1',
  language: 'en',
  response_format: 'json' as const,
  temperature: 0,
} as const;

// Test OpenAI connection
export async function testOpenAIConnection(): Promise<boolean> {
  try {
    const response = await openai.chat.completions.create({
      model: OPENAI_CONFIG.model,
      messages: [{ role: 'user', content: 'Hello' }],
      max_tokens: 5,
      temperature: 0,
    });

    if (response.choices && response.choices.length > 0) {
      logger.info('OpenAI connection test successful');
      return true;
    }
    
    logger.warn('OpenAI connection test returned empty response');
    return false;
  } catch (error) {
    logger.error('OpenAI connection test failed:', error);
    return false;
  }
}

// Generate chat completion
export async function generateChatCompletion(
  messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>,
  options?: {
    maxTokens?: number;
    temperature?: number;
    model?: string;
  }
): Promise<OpenAI.Chat.Completions.ChatCompletion> {
  try {
    const response = await openai.chat.completions.create({
      model: options?.model || OPENAI_CONFIG.model,
      messages,
      max_tokens: options?.maxTokens || OPENAI_CONFIG.maxTokens,
      temperature: options?.temperature || OPENAI_CONFIG.temperature,
      top_p: OPENAI_CONFIG.topP,
      frequency_penalty: OPENAI_CONFIG.frequencyPenalty,
      presence_penalty: OPENAI_CONFIG.presencePenalty,
    });

    logger.debug('OpenAI chat completion generated:', {
      model: response.model,
      usage: response.usage,
      finishReason: response.choices[0]?.finish_reason,
    });

    return response;
  } catch (error) {
    logger.error('Failed to generate chat completion:', error);
    throw error;
  }
}

// Generate speech from text
export async function generateSpeech(
  text: string,
  options?: {
    voice?: typeof AVAILABLE_VOICES[number]['id'];
    speed?: number;
    model?: string;
  }
): Promise<Buffer> {
  try {
    const response = await openai.audio.speech.create({
      model: options?.model || VOICE_CONFIG.model,
      voice: options?.voice || VOICE_CONFIG.voice,
      input: text,
      response_format: VOICE_CONFIG.response_format,
      speed: options?.speed || VOICE_CONFIG.speed,
    });

    const buffer = Buffer.from(await response.arrayBuffer());
    
    logger.debug('OpenAI speech generated:', {
      textLength: text.length,
      audioSize: buffer.length,
      voice: options?.voice || VOICE_CONFIG.voice,
    });

    return buffer;
  } catch (error) {
    logger.error('Failed to generate speech:', error);
    throw error;
  }
}

// Transcribe audio to text
export async function transcribeAudio(
  audioBuffer: Buffer,
  options?: {
    language?: string;
    model?: string;
    temperature?: number;
  }
): Promise<OpenAI.Audio.Transcriptions.Transcription> {
  try {
    // Create a File-like object from buffer
    const audioFile = new File([audioBuffer], 'audio.webm', {
      type: 'audio/webm',
    });

    const response = await openai.audio.transcriptions.create({
      file: audioFile,
      model: options?.model || SPEECH_CONFIG.model,
      language: options?.language || SPEECH_CONFIG.language,
      response_format: SPEECH_CONFIG.response_format,
      temperature: options?.temperature || SPEECH_CONFIG.temperature,
    });

    logger.debug('OpenAI transcription completed:', {
      audioSize: audioBuffer.length,
      transcriptLength: response.text?.length || 0,
      language: options?.language || SPEECH_CONFIG.language,
    });

    return response;
  } catch (error) {
    logger.error('Failed to transcribe audio:', error);
    throw error;
  }
}

// Get OpenAI usage statistics
export async function getOpenAIUsage(): Promise<any> {
  try {
    // Note: OpenAI doesn't provide a direct usage API in the current SDK
    // This would need to be implemented using their billing API or dashboard
    logger.info('OpenAI usage statistics not available via SDK');
    return null;
  } catch (error) {
    logger.error('Failed to get OpenAI usage:', error);
    return null;
  }
}

// Validate OpenAI model availability
export async function validateModel(model: string): Promise<boolean> {
  try {
    const models = await openai.models.list();
    const availableModels = models.data.map(m => m.id);
    
    const isAvailable = availableModels.includes(model);
    
    if (!isAvailable) {
      logger.warn(`Model ${model} is not available. Available models:`, availableModels);
    }
    
    return isAvailable;
  } catch (error) {
    logger.error('Failed to validate model:', error);
    return false;
  }
}

// Error handling helper for OpenAI API errors
export function handleOpenAIError(error: any): {
  code: string;
  message: string;
  statusCode: number;
} {
  if (error?.error?.type) {
    switch (error.error.type) {
      case 'invalid_request_error':
        return {
          code: 'INVALID_REQUEST',
          message: error.error.message || 'Invalid request to OpenAI API',
          statusCode: 400,
        };
      case 'authentication_error':
        return {
          code: 'AUTHENTICATION_ERROR',
          message: 'OpenAI API authentication failed',
          statusCode: 401,
        };
      case 'permission_error':
        return {
          code: 'PERMISSION_ERROR',
          message: 'Insufficient permissions for OpenAI API',
          statusCode: 403,
        };
      case 'rate_limit_error':
        return {
          code: 'RATE_LIMIT_ERROR',
          message: 'OpenAI API rate limit exceeded',
          statusCode: 429,
        };
      case 'server_error':
        return {
          code: 'SERVER_ERROR',
          message: 'OpenAI API server error',
          statusCode: 500,
        };
      default:
        return {
          code: 'UNKNOWN_ERROR',
          message: error.error.message || 'Unknown OpenAI API error',
          statusCode: 500,
        };
    }
  }

  return {
    code: 'NETWORK_ERROR',
    message: 'Failed to connect to OpenAI API',
    statusCode: 503,
  };
}
