{"version": 3, "file": "rateLimiter.js", "sourceRoot": "", "sources": ["../../src/middleware/rateLimiter.ts"], "names": [], "mappings": ";;;;;;AAwPA,sCAIC;AAGD,gDAcC;AA7QD,4EAA2C;AAE3C,2CAAqD;AACrD,sDAA8C;AAC9C,iDAAgD;AAUhD,MAAM,gBAAgB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;IACvD,MAAM,UAAU,GAAG;QACjB,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;QAChC,GAAG,EAAE,GAAG,CAAC,WAAW;QACpB,MAAM,EAAE,GAAG,CAAC,MAAM;KACnB,CAAC;IAGF,IAAA,oBAAW,EAAC,qBAAqB,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IAGvD,MAAM,IAAI,6BAAc,CAAC,2CAA2C,CAAC,CAAC;AACxE,CAAC,CAAC;AAGF,MAAM,aAAa,GAAG,CAAC,GAAY,EAAW,EAAE;IAE9C,IAAI,GAAG,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,GAAG,CAAC,EAAE,KAAK,WAAW,EAAE,CAAC;QACrE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAGF,MAAM,YAAY,GAAG,CAAC,GAAY,EAAU,EAAE;IAE5C,IAAI,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,SAAS,CAAC;IAG9B,IAAI,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;QACjB,GAAG,IAAI,SAAS,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IAChC,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAGW,QAAA,WAAW,GAAG,IAAA,4BAAS,EAAC;IACnC,QAAQ,EAAE,oBAAM,CAAC,oBAAoB;IACrC,GAAG,EAAE,oBAAM,CAAC,uBAAuB;IACnC,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,IAAI,EAAE,qBAAqB;YAC3B,OAAO,EAAE,2CAA2C;SACrD;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE,gBAAgB;IACzB,IAAI,EAAE,aAAa;IACnB,YAAY;IACZ,cAAc,EAAE,CAAC,GAAY,EAAE,EAAE;QAC/B,eAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAChC,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,GAAG,EAAE,GAAG,CAAC,WAAW;SACrB,CAAC,CAAC;IACL,CAAC;CACF,CAAC,CAAC;AAGU,QAAA,gBAAgB,GAAG,IAAA,4BAAS,EAAC;IACxC,QAAQ,EAAE,oBAAM,CAAC,oBAAoB;IACrC,GAAG,EAAE,oBAAM,CAAC,6BAA6B;IACzC,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,IAAI,EAAE,2BAA2B;YACjC,OAAO,EAAE,iDAAiD;SAC3D;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE,gBAAgB;IACzB,IAAI,EAAE,aAAa;IACnB,YAAY;IACZ,cAAc,EAAE,CAAC,GAAY,EAAE,EAAE;QAC/B,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACtC,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,GAAG,EAAE,GAAG,CAAC,WAAW;SACrB,CAAC,CAAC;IACL,CAAC;CACF,CAAC,CAAC;AAGU,QAAA,iBAAiB,GAAG,IAAA,4BAAS,EAAC;IACzC,QAAQ,EAAE,oBAAM,CAAC,oBAAoB;IACrC,GAAG,EAAE,EAAE;IACP,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,IAAI,EAAE,4BAA4B;YAClC,OAAO,EAAE,+CAA+C;SACzD;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE,gBAAgB;IACzB,IAAI,EAAE,aAAa;IACnB,YAAY;CACb,CAAC,CAAC;AAGU,QAAA,eAAe,GAAG,IAAA,4BAAS,EAAC;IACvC,QAAQ,EAAE,oBAAM,CAAC,oBAAoB;IACrC,GAAG,EAAE,EAAE;IACP,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,IAAI,EAAE,0BAA0B;YAChC,OAAO,EAAE,0CAA0C;SACpD;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE,gBAAgB;IACzB,IAAI,EAAE,aAAa;IACnB,YAAY;CACb,CAAC,CAAC;AAGU,QAAA,iBAAiB,GAAG,IAAA,4BAAS,EAAC;IACzC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,GAAG,EAAE,CAAC;IACN,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,IAAI,EAAE,4BAA4B;YAClC,OAAO,EAAE,8DAA8D;SACxE;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE,gBAAgB;IACzB,IAAI,EAAE,aAAa;IACnB,YAAY;CACb,CAAC,CAAC;AAGH,MAAM,sBAAsB;IAK1B,YAAY,YAAoB,GAAG,EAAE,WAAmB,KAAK;QAJrD,eAAU,GAAG,IAAI,GAAG,EAAoD,CAAC;QAK/E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED,QAAQ,CAAC,GAAW;QAClB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3C,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QAGD,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,aAAa,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;YAC1D,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;QAGD,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,eAAe,CAAC,GAAW;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE1C,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAC;YACpB,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE;gBACvB,KAAK,EAAE,CAAC;gBACR,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE;aAC1B,CAAC,CAAC;QACL,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,2CAA2C,EAAE;YACvD,GAAG;YACH,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,KAAK;YAC/C,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB;QACd,OAAO,IAAA,4BAAS,EAAC;YACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,GAAG,EAAE,CAAC,GAAY,EAAE,EAAE;gBACpB,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;gBAC9B,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;YACD,OAAO,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;gBACvC,MAAM,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;gBAC9B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;gBAC1B,gBAAgB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC7B,CAAC;YACD,IAAI,EAAE,aAAa;YACnB,YAAY;SACb,CAAC,CAAC;IACL,CAAC;CACF;AAGY,QAAA,sBAAsB,GAAG,IAAI,sBAAsB,EAAE,CAAC,gBAAgB,EAAE,CAAC;AAG/E,MAAM,aAAa,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAc,EAAE,EAAE;IAE3E,GAAG,CAAC,GAAG,CAAC;QACN,oBAAoB,EAAE,mDAAmD;QACzE,+BAA+B,EAAE,GAAG,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,SAAS;KAC7E,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AARW,QAAA,aAAa,iBAQxB;AAGK,KAAK,UAAU,aAAa,CAAC,EAAU,EAAE,QAAgB;IAG9D,OAAO,KAAK,CAAC;AACf,CAAC;AAGD,SAAgB,kBAAkB,CAAC,GAAY;IAK7C,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,GAAG,CAAC,CAAC;IAC1D,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,GAAG,CAAC,CAAC;IAClE,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,GAAG,CAAC,CAAC;IAE9D,OAAO;QACL,KAAK;QACL,SAAS;QACT,KAAK,EAAE,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KAClC,CAAC;AACJ,CAAC;AAGM,MAAM,mBAAmB,GAAG,CAAC,YAAsB,EAAE,EAAE,EAAE;IAC9D,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAc,EAAE,EAAE;QACrD,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;YAE/B,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC;QAC3B,CAAC;QACD,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AARW,QAAA,mBAAmB,uBAQ9B;AAGF,kBAAe,mBAAW,CAAC"}