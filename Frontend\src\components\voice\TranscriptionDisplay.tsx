import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { clsx } from 'clsx';
import { Volume2, Copy, Check, AlertCircle } from 'lucide-react';
import { TranscriptionDisplayProps } from '@/types';

export function TranscriptionDisplay({
  transcript,
  confidence,
  language,
  isLive = false,
  className,
}: TranscriptionDisplayProps) {
  const [showCopied, setShowCopied] = useState(false);
  const [displayText, setDisplayText] = useState('');

  // Animate text appearance for live transcription
  useEffect(() => {
    if (isLive && transcript) {
      // Simulate typing effect for live transcription
      let currentIndex = 0;
      const interval = setInterval(() => {
        if (currentIndex <= transcript.length) {
          setDisplayText(transcript.slice(0, currentIndex));
          currentIndex++;
        } else {
          clearInterval(interval);
        }
      }, 30); // Adjust speed as needed

      return () => clearInterval(interval);
    } else {
      setDisplayText(transcript);
    }
  }, [transcript, isLive]);

  // Copy transcript to clipboard
  const handleCopy = async () => {
    if (!transcript) return;

    try {
      await navigator.clipboard.writeText(transcript);
      setShowCopied(true);
      setTimeout(() => setShowCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy transcript:', error);
    }
  };

  // Get confidence color
  const getConfidenceColor = (conf?: number) => {
    if (!conf) return 'text-gray-500';
    if (conf >= 0.8) return 'text-green-600 dark:text-green-400';
    if (conf >= 0.6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  // Get confidence label
  const getConfidenceLabel = (conf?: number) => {
    if (!conf) return 'Unknown';
    if (conf >= 0.8) return 'High';
    if (conf >= 0.6) return 'Medium';
    return 'Low';
  };

  if (!transcript && !isLive) {
    return null;
  }

  return (
    <motion.div
      className={clsx(
        'bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 shadow-sm',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Volume2 className="w-4 h-4 text-primary-600 dark:text-primary-400" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {isLive ? 'Live Transcription' : 'Transcription'}
          </span>
          {isLive && (
            <motion.div
              className="w-2 h-2 bg-red-500 rounded-full"
              animate={{ opacity: [1, 0.3, 1] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            />
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2">
          {/* Language indicator */}
          {language && (
            <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded">
              {language.toUpperCase()}
            </span>
          )}

          {/* Copy button */}
          {transcript && (
            <button
              onClick={handleCopy}
              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              title="Copy transcript"
            >
              <AnimatePresence mode="wait">
                {showCopied ? (
                  <motion.div
                    key="check"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                  >
                    <Check className="w-4 h-4 text-green-600" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="copy"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                  >
                    <Copy className="w-4 h-4" />
                  </motion.div>
                )}
              </AnimatePresence>
            </button>
          )}
        </div>
      </div>

      {/* Transcript content */}
      <div className="min-h-[3rem]">
        {displayText ? (
          <motion.p
            className="text-gray-900 dark:text-gray-100 leading-relaxed"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            {displayText}
            {isLive && (
              <motion.span
                className="inline-block w-0.5 h-5 bg-primary-600 ml-1"
                animate={{ opacity: [1, 0, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
              />
            )}
          </motion.p>
        ) : isLive ? (
          <div className="flex items-center text-gray-500 dark:text-gray-400">
            <motion.div
              className="flex space-x-1"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              <div className="w-2 h-2 bg-current rounded-full" />
              <div className="w-2 h-2 bg-current rounded-full" />
              <div className="w-2 h-2 bg-current rounded-full" />
            </motion.div>
            <span className="ml-2 text-sm">Listening...</span>
          </div>
        ) : (
          <p className="text-gray-500 dark:text-gray-400 italic">
            No transcription available
          </p>
        )}
      </div>

      {/* Footer with confidence and metadata */}
      {(confidence !== undefined || isLive) && (
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between text-xs">
            {/* Confidence indicator */}
            {confidence !== undefined && (
              <div className="flex items-center space-x-2">
                <span className="text-gray-500 dark:text-gray-400">
                  Confidence:
                </span>
                <div className="flex items-center space-x-1">
                  <span className={getConfidenceColor(confidence)}>
                    {getConfidenceLabel(confidence)}
                  </span>
                  <span className="text-gray-400 dark:text-gray-500">
                    ({Math.round(confidence * 100)}%)
                  </span>
                </div>
                
                {/* Confidence bar */}
                <div className="w-16 h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <motion.div
                    className={clsx(
                      'h-full rounded-full',
                      confidence >= 0.8 ? 'bg-green-500' :
                      confidence >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                    )}
                    initial={{ width: 0 }}
                    animate={{ width: `${confidence * 100}%` }}
                    transition={{ duration: 0.5, ease: 'easeOut' }}
                  />
                </div>
              </div>
            )}

            {/* Status indicator */}
            <div className="flex items-center space-x-1">
              {isLive ? (
                <>
                  <motion.div
                    className="w-2 h-2 bg-green-500 rounded-full"
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  />
                  <span className="text-green-600 dark:text-green-400">
                    Live
                  </span>
                </>
              ) : (
                <>
                  <div className="w-2 h-2 bg-gray-400 rounded-full" />
                  <span className="text-gray-500 dark:text-gray-400">
                    Complete
                  </span>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
}

// Compact version for inline display
export function CompactTranscriptionDisplay({
  transcript,
  confidence,
  isLive = false,
  className,
}: Omit<TranscriptionDisplayProps, 'language'>) {
  if (!transcript && !isLive) {
    return null;
  }

  return (
    <motion.div
      className={clsx(
        'inline-flex items-center space-x-2 px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full text-sm',
        className
      )}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
    >
      {isLive && (
        <motion.div
          className="w-1.5 h-1.5 bg-red-500 rounded-full"
          animate={{ opacity: [1, 0.3, 1] }}
          transition={{ duration: 1.5, repeat: Infinity }}
        />
      )}
      
      <span className="text-gray-700 dark:text-gray-300 max-w-xs truncate">
        {transcript || 'Listening...'}
      </span>
      
      {confidence !== undefined && (
        <span className={clsx('text-xs', getConfidenceColor(confidence))}>
          {Math.round(confidence * 100)}%
        </span>
      )}
    </motion.div>
  );
}

// Helper function for confidence color (reused)
function getConfidenceColor(conf?: number) {
  if (!conf) return 'text-gray-500';
  if (conf >= 0.8) return 'text-green-600 dark:text-green-400';
  if (conf >= 0.6) return 'text-yellow-600 dark:text-yellow-400';
  return 'text-red-600 dark:text-red-400';
}

export default TranscriptionDisplay;
