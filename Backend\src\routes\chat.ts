import { Router } from 'express';
import { as<PERSON><PERSON><PERSON><PERSON> } from '@/middleware/errorHandler';
import { validate, chatSchemas } from '@/middleware/validation';
import { chatRateLimiter } from '@/middleware/rateLimiter';
import { ChatService } from '@/services/chatService';
import { ConversationService } from '@/services/conversationService';
import { logger } from '@/utils/logger';

const router = Router();
const chatService = new ChatService();
const conversationService = new ConversationService();

// Apply rate limiting to all chat routes
router.use(chatRateLimiter);

// Send message to AI
router.post('/message', 
  validate(chatSchemas.sendMessage),
  asyncHandler(async (req, res) => {
    const { message, conversationId, context } = req.body;
    const startTime = Date.now();

    logger.info('Processing chat message', {
      requestId: req.requestId,
      messageLength: message.length,
      conversationId,
      hasContext: !!context,
    });

    try {
      // Process the message through chat service
      const result = await chatService.processMessage({
        message,
        conversationId,
        context,
        userId: req.user?.id, // Optional user ID if authenticated
      });

      const responseTime = Date.now() - startTime;

      logger.info('Chat message processed successfully', {
        requestId: req.requestId,
        conversationId: result.conversationId,
        responseTime,
        tokensUsed: result.metadata.tokensUsed,
      });

      res.json({
        success: true,
        data: result,
        message: 'Message processed successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('Failed to process chat message', {
        requestId: req.requestId,
        error: error.message,
        messageLength: message.length,
      });
      throw error;
    }
  })
);

// Get conversation history
router.get('/history/:conversationId',
  validate(chatSchemas.getHistory),
  asyncHandler(async (req, res) => {
    const { conversationId } = req.params;
    const { limit, offset, before, after } = req.query;

    logger.debug('Retrieving conversation history', {
      requestId: req.requestId,
      conversationId,
      limit,
      offset,
    });

    try {
      const conversation = await conversationService.getConversationWithMessages(
        conversationId,
        {
          limit: Number(limit),
          offset: Number(offset),
          before: before ? new Date(before as string) : undefined,
          after: after ? new Date(after as string) : undefined,
        }
      );

      if (!conversation) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'CONVERSATION_NOT_FOUND',
            message: 'Conversation not found',
          },
          timestamp: new Date().toISOString(),
        });
      }

      // Check if user has access to this conversation (if authentication is enabled)
      if (req.user && conversation.userId && conversation.userId !== req.user.id) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Access denied to this conversation',
          },
          timestamp: new Date().toISOString(),
        });
      }

      const response = {
        conversationId: conversation.id,
        title: conversation.title,
        messages: conversation.messages || [],
        totalMessages: conversation.messages?.length || 0,
        hasMore: (conversation.messages?.length || 0) >= Number(limit),
        metadata: conversation.metadata,
        createdAt: conversation.createdAt,
        updatedAt: conversation.updatedAt,
      };

      logger.debug('Conversation history retrieved', {
        requestId: req.requestId,
        conversationId,
        messageCount: response.messages.length,
      });

      res.json({
        success: true,
        data: response,
        message: 'Conversation history retrieved successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('Failed to retrieve conversation history', {
        requestId: req.requestId,
        conversationId,
        error: error.message,
      });
      throw error;
    }
  })
);

// Get all conversations for user
router.get('/conversations',
  asyncHandler(async (req, res) => {
    const { page = 1, limit = 20, sortBy = 'updatedAt', sortOrder = 'desc' } = req.query;
    const userId = req.user?.id; // Optional user ID if authenticated

    logger.debug('Retrieving user conversations', {
      requestId: req.requestId,
      userId,
      page,
      limit,
    });

    try {
      const conversations = await conversationService.getUserConversations(userId, {
        page: Number(page),
        limit: Number(limit),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
      });

      res.json({
        success: true,
        data: conversations,
        message: 'Conversations retrieved successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('Failed to retrieve conversations', {
        requestId: req.requestId,
        userId,
        error: error.message,
      });
      throw error;
    }
  })
);

// Update conversation title
router.put('/conversations/:conversationId/title',
  validate({
    params: chatSchemas.deleteConversation.params,
    body: {
      title: require('joi').string().min(1).max(100).required(),
    },
  }),
  asyncHandler(async (req, res) => {
    const { conversationId } = req.params;
    const { title } = req.body;
    const userId = req.user?.id;

    logger.debug('Updating conversation title', {
      requestId: req.requestId,
      conversationId,
      newTitle: title,
    });

    try {
      const updatedConversation = await conversationService.updateConversationTitle(
        conversationId,
        title,
        userId
      );

      res.json({
        success: true,
        data: {
          conversationId: updatedConversation.id,
          title: updatedConversation.title,
          updatedAt: updatedConversation.updatedAt,
        },
        message: 'Conversation title updated successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('Failed to update conversation title', {
        requestId: req.requestId,
        conversationId,
        error: error.message,
      });
      throw error;
    }
  })
);

// Delete conversation
router.delete('/history/:conversationId',
  validate(chatSchemas.deleteConversation),
  asyncHandler(async (req, res) => {
    const { conversationId } = req.params;
    const userId = req.user?.id;

    logger.info('Deleting conversation', {
      requestId: req.requestId,
      conversationId,
      userId,
    });

    try {
      await conversationService.deleteConversation(conversationId, userId);

      logger.info('Conversation deleted successfully', {
        requestId: req.requestId,
        conversationId,
      });

      res.json({
        success: true,
        message: 'Conversation deleted successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('Failed to delete conversation', {
        requestId: req.requestId,
        conversationId,
        error: error.message,
      });
      throw error;
    }
  })
);

// Clear all conversations for user
router.delete('/conversations',
  asyncHandler(async (req, res) => {
    const userId = req.user?.id;

    logger.info('Clearing all conversations', {
      requestId: req.requestId,
      userId,
    });

    try {
      const deletedCount = await conversationService.clearUserConversations(userId);

      logger.info('All conversations cleared', {
        requestId: req.requestId,
        userId,
        deletedCount,
      });

      res.json({
        success: true,
        data: {
          deletedCount,
        },
        message: `${deletedCount} conversations deleted successfully`,
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('Failed to clear conversations', {
        requestId: req.requestId,
        userId,
        error: error.message,
      });
      throw error;
    }
  })
);

// Get conversation statistics
router.get('/stats',
  asyncHandler(async (req, res) => {
    const userId = req.user?.id;

    logger.debug('Retrieving conversation statistics', {
      requestId: req.requestId,
      userId,
    });

    try {
      const stats = await conversationService.getConversationStats(userId);

      res.json({
        success: true,
        data: stats,
        message: 'Conversation statistics retrieved successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('Failed to retrieve conversation statistics', {
        requestId: req.requestId,
        userId,
        error: error.message,
      });
      throw error;
    }
  })
);

export default router;
