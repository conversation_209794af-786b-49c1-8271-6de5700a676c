import { GoogleGenerativeAI } from '@google/generative-ai';
import { logger } from '@/utils/logger';
import { config } from '@/config/environment';

// Check if we're in mock mode
const isMockMode = process.env['GEMINI_MOCK_MODE'] === 'true';

// Initialize Google Generative AI (only if not in mock mode)
const genAI = isMockMode ? null : new GoogleGenerativeAI(config.GOOGLE_API_KEY);

// Gemini configuration
const GEMINI_CONFIG = {
  apiKey: config.GOOGLE_API_KEY,
  model: config.MODEL_NAME,
  maxTokens: config.GEMINI_MAX_TOKENS,
  temperature: config.GEMINI_TEMPERATURE,
  systemPrompt: `You are a helpful AI assistant in a voice-enabled chat application. 
You should provide clear, concise, and helpful responses. Keep your responses conversational 
and appropriate for voice interaction. Avoid overly long responses unless specifically requested.
Be friendly, professional, and engaging in your communication style.`,
};

// Available voices for TTS (we'll use a simple mapping for now)
const A<PERSON><PERSON>ABLE_VOICES = [
  { id: 'alloy', name: 'Alloy', language: 'en', gender: 'neutral' },
  { id: 'echo', name: 'Echo', language: 'en', gender: 'male' },
  { id: 'fable', name: 'Fable', language: 'en', gender: 'neutral' },
  { id: 'onyx', name: 'Onyx', language: 'en', gender: 'male' },
  { id: 'nova', name: 'Nova', language: 'en', gender: 'female' },
  { id: 'shimmer', name: 'Shimmer', language: 'en', gender: 'female' },
];

// Get Gemini model instance
export function getGeminiModel() {
  if (isMockMode) {
    return null; // Return null in mock mode
  }
  return genAI!.getGenerativeModel({ model: GEMINI_CONFIG.model });
}

// Chat completion function using Gemini
export async function generateChatCompletion(
  messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>,
  options: {
    maxTokens?: number;
    temperature?: number;
    model?: string;
  } = {}
) {
  const {
    maxTokens = GEMINI_CONFIG.maxTokens,
    temperature = GEMINI_CONFIG.temperature,
  } = options;

  try {
    let text: string;

    if (isMockMode) {
      // Mock response for development
      logger.info('Using mock Gemini response');
      text = `This is a mock response from Gemini AI. In a real implementation, this would be generated by the Gemini API. Your message was: "${messages[messages.length - 1]?.content || 'No message'}"`;
    } else {
      const model = getGeminiModel();

      // Convert messages to Gemini format
      const geminiMessages = convertMessagesToGeminiFormat(messages);

      // Generate content
      const result = await model!.generateContent({
        contents: geminiMessages,
        generationConfig: {
          maxOutputTokens: maxTokens,
          temperature: temperature,
        },
      });

      const response = await result.response;
      text = response.text();
    }

    // Convert to OpenAI-compatible format for existing code
    return {
      id: `gemini-${Date.now()}`,
      object: 'chat.completion',
      created: Math.floor(Date.now() / 1000),
      model: GEMINI_CONFIG.model,
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant' as const,
            content: text,
          },
          finish_reason: 'stop',
        },
      ],
      usage: {
        prompt_tokens: 0, // Gemini doesn't provide token counts in the same way
        completion_tokens: 0,
        total_tokens: 0,
      },
    };

  } catch (error: any) {
    logger.error('Gemini API error:', {
      error: error.message,
      stack: error.stack,
    });
    throw handleGeminiError(error);
  }
}

// Convert messages to Gemini format
function convertMessagesToGeminiFormat(
  messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>
) {
  const geminiMessages = [];
  let systemPrompt = GEMINI_CONFIG.systemPrompt;

  // Extract system message if present
  const systemMessage = messages.find(msg => msg.role === 'system');
  if (systemMessage) {
    systemPrompt = systemMessage.content;
  }

  // Convert user and assistant messages
  const conversationMessages = messages.filter(msg => msg.role !== 'system');
  
  for (const message of conversationMessages) {
    geminiMessages.push({
      role: message.role === 'assistant' ? 'model' : 'user',
      parts: [{ text: message.content }],
    });
  }

  // Add system prompt as the first user message if no conversation exists
  if (geminiMessages.length === 0 || (geminiMessages[0] && geminiMessages[0].role === 'model')) {
    geminiMessages.unshift({
      role: 'user',
      parts: [{ text: systemPrompt }],
    });
    geminiMessages.push({
      role: 'model',
      parts: [{ text: 'I understand. I\'ll be a helpful AI assistant.' }],
    });
  }

  return geminiMessages;
}

// Error handling for Gemini API
export function handleGeminiError(error: any) {
  const errorResponse = {
    error: {
      type: 'api_error',
      message: error.message || 'Unknown Gemini API error',
      code: error.code || 'unknown_error',
    },
    statusCode: 500,
  };

  if (error.message?.includes('API key')) {
    errorResponse.error.type = 'authentication_error';
    errorResponse.statusCode = 401;
  } else if (error.message?.includes('quota') || error.message?.includes('rate limit')) {
    errorResponse.error.type = 'rate_limit_error';
    errorResponse.statusCode = 429;
  } else if (error.message?.includes('safety')) {
    errorResponse.error.type = 'safety_error';
    errorResponse.statusCode = 400;
  }

  return errorResponse;
}

// Test Gemini connection
export async function testGeminiConnection(): Promise<boolean> {
  try {
    if (isMockMode) {
      logger.info('Gemini connection test successful (mock mode)');
      return true;
    }

    const model = getGeminiModel();
    const result = await model!.generateContent('Hello, this is a test.');
    const response = await result.response;
    const text = response.text();

    logger.info('Gemini connection test successful', {
      responseLength: text.length,
    });

    return true;
  } catch (error: any) {
    logger.error('Gemini connection test failed:', {
      error: error.message,
    });
    return false;
  }
}

// For now, we'll use placeholder functions for audio processing
// In a real implementation, you might want to integrate with other services
export async function transcribeAudio(
  _audioBuffer: Buffer,
  _options: {
    language?: string;
    model?: string;
    temperature?: number;
  } = {}
): Promise<{ text: string }> {
  // Placeholder - in real implementation, you'd use a speech-to-text service
  // like Google Cloud Speech-to-Text, Azure Speech Services, etc.
  logger.warn('Audio transcription not implemented - using placeholder');
  return { text: 'Audio transcription not yet implemented with Gemini' };
}

export async function generateSpeech(
  _text: string,
  _options: {
    voice?: string;
    speed?: number;
    model?: string;
  } = {}
): Promise<Buffer> {
  // Placeholder - in real implementation, you'd use a text-to-speech service
  // like Google Cloud Text-to-Speech, Azure Speech Services, etc.
  logger.warn('Speech synthesis not implemented - using placeholder');
  return Buffer.from('Speech synthesis not yet implemented');
}

// Export configuration and functions
export { genAI, GEMINI_CONFIG, AVAILABLE_VOICES };
