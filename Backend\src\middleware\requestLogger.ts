import { Request, Response, NextFunction } from 'express';
import { logger, logRequest, logSecurity } from '@/utils/logger';
import { v4 as uuidv4 } from 'uuid';

// Extend Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
      startTime?: number;
      user?: {
        id: string;
        email?: string;
      };
      skipRateLimit?: boolean;
    }
  }
}

// Request logging middleware
export function requestLogger(req: Request, res: Response, next: NextFunction): void {
  // Generate unique request ID
  req.requestId = uuidv4();
  req.startTime = Date.now();

  // Add request ID to response headers
  res.set('X-Request-ID', req.requestId);

  // Extract request information
  const requestInfo = {
    requestId: req.requestId,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent'),
    referer: req.get('Referer'),
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length'),
    acceptLanguage: req.get('Accept-Language'),
    timestamp: new Date().toISOString(),
  };

  // Log request start (debug level)
  logger.debug('Request Started', requestInfo);

  // Override res.json to capture response data
  const originalJson = res.json;
  res.json = function(body: any) {
    // Calculate response time
    const responseTime = req.startTime ? Date.now() - req.startTime : 0;

    // Log response information
    const responseInfo = {
      requestId: req.requestId,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      contentLength: JSON.stringify(body).length,
    };

    // Log based on status code
    if (res.statusCode >= 500) {
      logger.error('Request Completed with Server Error', {
        ...requestInfo,
        ...responseInfo,
        responseBody: process.env.NODE_ENV === 'development' ? body : undefined,
      });
    } else if (res.statusCode >= 400) {
      logger.warn('Request Completed with Client Error', {
        ...requestInfo,
        ...responseInfo,
        responseBody: process.env.NODE_ENV === 'development' ? body : undefined,
      });
    } else {
      logger.info('Request Completed Successfully', {
        ...requestInfo,
        ...responseInfo,
      });
    }

    // Call original json method
    return originalJson.call(this, body);
  };

  // Override res.send to capture non-JSON responses
  const originalSend = res.send;
  res.send = function(body: any) {
    if (!res.headersSent) {
      const responseTime = req.startTime ? Date.now() - req.startTime : 0;
      
      const responseInfo = {
        requestId: req.requestId,
        statusCode: res.statusCode,
        responseTime: `${responseTime}ms`,
        contentLength: typeof body === 'string' ? body.length : Buffer.byteLength(body),
      };

      logger.info('Request Completed', {
        ...requestInfo,
        ...responseInfo,
      });
    }

    return originalSend.call(this, body);
  };

  // Handle response finish event
  res.on('finish', () => {
    const responseTime = req.startTime ? Date.now() - req.startTime : 0;
    
    // Log slow requests
    if (responseTime > 5000) { // 5 seconds
      logger.warn('Slow Request Detected', {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        responseTime: `${responseTime}ms`,
        statusCode: res.statusCode,
      });
    }

    // Log security-relevant events
    if (res.statusCode === 401) {
      logSecurity('UNAUTHORIZED_ACCESS_ATTEMPT', {
        requestId: req.requestId,
        ip: req.ip,
        url: req.originalUrl,
        userAgent: req.get('User-Agent'),
      });
    } else if (res.statusCode === 403) {
      logSecurity('FORBIDDEN_ACCESS_ATTEMPT', {
        requestId: req.requestId,
        ip: req.ip,
        url: req.originalUrl,
        userAgent: req.get('User-Agent'),
      });
    } else if (res.statusCode === 429) {
      logSecurity('RATE_LIMIT_EXCEEDED', {
        requestId: req.requestId,
        ip: req.ip,
        url: req.originalUrl,
        userAgent: req.get('User-Agent'),
      });
    }
  });

  // Handle response close event (client disconnected)
  res.on('close', () => {
    if (!res.writableFinished) {
      logger.warn('Client Disconnected', {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
      });
    }
  });

  next();
}

// Sensitive data filter for logging
export function filterSensitiveData(data: any): any {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const sensitiveFields = [
    'password',
    'token',
    'apiKey',
    'secret',
    'authorization',
    'cookie',
    'session',
  ];

  const filtered = { ...data };

  for (const field of sensitiveFields) {
    if (field in filtered) {
      filtered[field] = '[REDACTED]';
    }
  }

  // Recursively filter nested objects
  for (const key in filtered) {
    if (typeof filtered[key] === 'object' && filtered[key] !== null) {
      filtered[key] = filterSensitiveData(filtered[key]);
    }
  }

  return filtered;
}

// Request body logging middleware (for debugging)
export function requestBodyLogger(req: Request, res: Response, next: NextFunction): void {
  if (process.env.NODE_ENV === 'development' && req.body) {
    logger.debug('Request Body', {
      requestId: req.requestId,
      body: filterSensitiveData(req.body),
    });
  }
  next();
}

// API usage tracking middleware
export function apiUsageTracker(req: Request, res: Response, next: NextFunction): void {
  // This would typically save to database for analytics
  const usageData = {
    endpoint: req.route?.path || req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date(),
  };

  // In a real implementation, you would save this to database
  logger.debug('API Usage', usageData);

  next();
}

// Performance monitoring middleware
export function performanceMonitor(req: Request, res: Response, next: NextFunction): void {
  const startTime = process.hrtime.bigint();

  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds

    // Log performance metrics
    logger.debug('Performance Metrics', {
      requestId: req.requestId,
      method: req.method,
      url: req.originalUrl,
      duration: `${duration.toFixed(2)}ms`,
      statusCode: res.statusCode,
      memoryUsage: process.memoryUsage(),
    });

    // Alert on performance issues
    if (duration > 10000) { // 10 seconds
      logger.error('Performance Alert: Very Slow Request', {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        duration: `${duration.toFixed(2)}ms`,
      });
    }
  });

  next();
}

// Error tracking middleware
export function errorTracker(error: Error, req: Request, res: Response, next: NextFunction): void {
  // Track error metrics
  logger.error('Request Error', {
    requestId: req.requestId,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
  });

  next(error);
}

// Health check request filter (reduce noise in logs)
export function healthCheckFilter(req: Request, res: Response, next: NextFunction): void {
  // Skip detailed logging for health check endpoints
  if (req.path === '/api/health' || req.path === '/health') {
    req.skipLogging = true;
  }
  next();
}

// CORS logging middleware
export function corsLogger(req: Request, res: Response, next: NextFunction): void {
  if (process.env.ENABLE_CORS_LOGGING === 'true') {
    const origin = req.get('Origin');
    const method = req.method;

    if (origin && method === 'OPTIONS') {
      logger.debug('CORS Preflight Request', {
        requestId: req.requestId,
        origin,
        method: req.get('Access-Control-Request-Method'),
        headers: req.get('Access-Control-Request-Headers'),
      });
    } else if (origin) {
      logger.debug('CORS Request', {
        requestId: req.requestId,
        origin,
        method,
      });
    }
  }
  next();
}

// Export middleware functions
export default requestLogger;
