"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SPEECH_CONFIG = exports.AVAILABLE_VOICES = exports.VOICE_CONFIG = exports.OPENAI_CONFIG = exports.openai = void 0;
exports.testOpenAIConnection = testOpenAIConnection;
exports.generateChatCompletion = generateChatCompletion;
exports.generateSpeech = generateSpeech;
exports.transcribeAudio = transcribeAudio;
exports.getOpenAIUsage = getOpenAIUsage;
exports.validateModel = validateModel;
exports.handleOpenAIError = handleOpenAIError;
const openai_1 = __importDefault(require("openai"));
const logger_1 = require("@/utils/logger");
const environment_1 = require("./environment");
exports.openai = new openai_1.default({
    apiKey: environment_1.config.OPENAI_API_KEY,
    timeout: 30000,
    maxRetries: 3,
});
exports.OPENAI_CONFIG = {
    model: environment_1.config.OPENAI_MODEL,
    maxTokens: environment_1.config.OPENAI_MAX_TOKENS,
    temperature: environment_1.config.OPENAI_TEMPERATURE,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0,
    systemPrompt: `You are a helpful AI assistant in a voice chat application. 
    Keep your responses conversational, concise, and natural for spoken interaction. 
    Avoid using markdown formatting, special characters, or overly technical language. 
    Respond as if you're having a natural conversation with the user.`,
};
exports.VOICE_CONFIG = {
    model: 'tts-1',
    voice: 'alloy',
    response_format: 'mp3',
    speed: 1.0,
};
exports.AVAILABLE_VOICES = [
    { id: 'alloy', name: 'Alloy', gender: 'neutral', language: 'en' },
    { id: 'echo', name: 'Echo', gender: 'male', language: 'en' },
    { id: 'fable', name: 'Fable', gender: 'neutral', language: 'en' },
    { id: 'onyx', name: 'Onyx', gender: 'male', language: 'en' },
    { id: 'nova', name: 'Nova', gender: 'female', language: 'en' },
    { id: 'shimmer', name: 'Shimmer', gender: 'female', language: 'en' },
];
exports.SPEECH_CONFIG = {
    model: 'whisper-1',
    language: 'en',
    response_format: 'json',
    temperature: 0,
};
async function testOpenAIConnection() {
    try {
        const response = await exports.openai.chat.completions.create({
            model: exports.OPENAI_CONFIG.model,
            messages: [{ role: 'user', content: 'Hello' }],
            max_tokens: 5,
            temperature: 0,
        });
        if (response.choices && response.choices.length > 0) {
            logger_1.logger.info('OpenAI connection test successful');
            return true;
        }
        logger_1.logger.warn('OpenAI connection test returned empty response');
        return false;
    }
    catch (error) {
        logger_1.logger.error('OpenAI connection test failed:', error);
        return false;
    }
}
async function generateChatCompletion(messages, options) {
    try {
        const response = await exports.openai.chat.completions.create({
            model: options?.model || exports.OPENAI_CONFIG.model,
            messages,
            max_tokens: options?.maxTokens || exports.OPENAI_CONFIG.maxTokens,
            temperature: options?.temperature || exports.OPENAI_CONFIG.temperature,
            top_p: exports.OPENAI_CONFIG.topP,
            frequency_penalty: exports.OPENAI_CONFIG.frequencyPenalty,
            presence_penalty: exports.OPENAI_CONFIG.presencePenalty,
        });
        logger_1.logger.debug('OpenAI chat completion generated:', {
            model: response.model,
            usage: response.usage,
            finishReason: response.choices[0]?.finish_reason,
        });
        return response;
    }
    catch (error) {
        logger_1.logger.error('Failed to generate chat completion:', error);
        throw error;
    }
}
async function generateSpeech(text, options) {
    try {
        const response = await exports.openai.audio.speech.create({
            model: options?.model || exports.VOICE_CONFIG.model,
            voice: options?.voice || exports.VOICE_CONFIG.voice,
            input: text,
            response_format: exports.VOICE_CONFIG.response_format,
            speed: options?.speed || exports.VOICE_CONFIG.speed,
        });
        const buffer = Buffer.from(await response.arrayBuffer());
        logger_1.logger.debug('OpenAI speech generated:', {
            textLength: text.length,
            audioSize: buffer.length,
            voice: options?.voice || exports.VOICE_CONFIG.voice,
        });
        return buffer;
    }
    catch (error) {
        logger_1.logger.error('Failed to generate speech:', error);
        throw error;
    }
}
async function transcribeAudio(audioBuffer, options) {
    try {
        const audioFile = new File([audioBuffer], 'audio.webm', {
            type: 'audio/webm',
        });
        const response = await exports.openai.audio.transcriptions.create({
            file: audioFile,
            model: options?.model || exports.SPEECH_CONFIG.model,
            language: options?.language || exports.SPEECH_CONFIG.language,
            response_format: exports.SPEECH_CONFIG.response_format,
            temperature: options?.temperature || exports.SPEECH_CONFIG.temperature,
        });
        logger_1.logger.debug('OpenAI transcription completed:', {
            audioSize: audioBuffer.length,
            transcriptLength: response.text?.length || 0,
            language: options?.language || exports.SPEECH_CONFIG.language,
        });
        return response;
    }
    catch (error) {
        logger_1.logger.error('Failed to transcribe audio:', error);
        throw error;
    }
}
async function getOpenAIUsage() {
    try {
        logger_1.logger.info('OpenAI usage statistics not available via SDK');
        return null;
    }
    catch (error) {
        logger_1.logger.error('Failed to get OpenAI usage:', error);
        return null;
    }
}
async function validateModel(model) {
    try {
        const models = await exports.openai.models.list();
        const availableModels = models.data.map(m => m.id);
        const isAvailable = availableModels.includes(model);
        if (!isAvailable) {
            logger_1.logger.warn(`Model ${model} is not available. Available models:`, availableModels);
        }
        return isAvailable;
    }
    catch (error) {
        logger_1.logger.error('Failed to validate model:', error);
        return false;
    }
}
function handleOpenAIError(error) {
    if (error?.error?.type) {
        switch (error.error.type) {
            case 'invalid_request_error':
                return {
                    code: 'INVALID_REQUEST',
                    message: error.error.message || 'Invalid request to OpenAI API',
                    statusCode: 400,
                };
            case 'authentication_error':
                return {
                    code: 'AUTHENTICATION_ERROR',
                    message: 'OpenAI API authentication failed',
                    statusCode: 401,
                };
            case 'permission_error':
                return {
                    code: 'PERMISSION_ERROR',
                    message: 'Insufficient permissions for OpenAI API',
                    statusCode: 403,
                };
            case 'rate_limit_error':
                return {
                    code: 'RATE_LIMIT_ERROR',
                    message: 'OpenAI API rate limit exceeded',
                    statusCode: 429,
                };
            case 'server_error':
                return {
                    code: 'SERVER_ERROR',
                    message: 'OpenAI API server error',
                    statusCode: 500,
                };
            default:
                return {
                    code: 'UNKNOWN_ERROR',
                    message: error.error.message || 'Unknown OpenAI API error',
                    statusCode: 500,
                };
        }
    }
    return {
        code: 'NETWORK_ERROR',
        message: 'Failed to connect to OpenAI API',
        statusCode: 503,
    };
}
//# sourceMappingURL=openai.js.map