# Voice AI Chat Frontend

A modern React/Next.js frontend for the Voice-Enabled AI Chat Application.

## Features

- 🎤 **Push-to-Talk Voice Recording** with visual feedback
- 🗣️ **Real-time Speech Recognition** using Web Speech API
- 🔊 **Text-to-Speech Synthesis** for AI responses
- 💬 **Chat Interface** with conversation history
- 🎨 **Modern UI** with dark mode support
- 📱 **Responsive Design** for desktop and mobile
- ⚡ **Real-time Updates** with optimistic UI
- 🔧 **Settings Panel** for voice and UI preferences

## Tech Stack

- **Framework**: Next.js 14 with React 18
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Animations**: Framer Motion
- **HTTP Client**: Axios
- **Icons**: Lucide React
- **Notifications**: React Hot Toast

## Getting Started

### Prerequisites

- Node.js 18+ and npm 8+
- Backend API running on http://localhost:8000

### Installation

1. Install dependencies:
```bash
npm install
```

2. Copy environment variables:
```bash
cp .env.local.example .env.local
```

3. Update `.env.local` with your configuration:
```bash
NEXT_PUBLIC_API_URL=http://localhost:8000
```

4. Start the development server:
```bash
npm run dev
```

5. Open http://localhost:3000 in your browser

### Build for Production

```bash
npm run build
npm start
```

## Project Structure

```
Frontend/
├── src/
│   ├── components/          # React components
│   │   ├── chat/           # Chat-related components
│   │   └── voice/          # Voice-related components
│   ├── hooks/              # Custom React hooks
│   ├── pages/              # Next.js pages
│   ├── services/           # API services
│   ├── store/              # Zustand stores
│   ├── styles/             # Global styles
│   ├── types/              # TypeScript types
│   └── utils/              # Utility functions
├── public/                 # Static assets
└── package.json
```

## Key Components

### Voice Components
- `PushToTalkButton` - Main recording button with visual states
- `VoiceVisualizer` - Audio level visualization
- `TranscriptionDisplay` - Live and final transcription display

### Chat Components
- `MessageBubble` - Individual message display
- `ChatContainer` - Main chat interface
- `ConversationList` - Sidebar conversation list

### Hooks
- `useSpeechRecognition` - Web Speech API integration
- `useSpeechSynthesis` - Text-to-speech functionality
- `useAudioRecorder` - Audio recording with MediaRecorder

### Stores
- `chatStore` - Chat messages and conversations
- `voiceStore` - Voice recording and processing
- `settingsStore` - User preferences and settings

## Browser Support

| Feature | Chrome | Firefox | Safari | Edge |
|---------|--------|---------|--------|------|
| Speech Recognition | ✅ | ❌ | ✅ | ✅ |
| Speech Synthesis | ✅ | ✅ | ✅ | ✅ |
| Audio Recording | ✅ | ✅ | ✅ | ✅ |

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks
- `npm run test` - Run tests

### Environment Variables

See `.env.local.example` for all available configuration options.

### Adding New Features

1. Create components in appropriate directories
2. Add types to `src/types/index.ts`
3. Update stores if needed
4. Add API calls to `src/services/api.ts`
5. Update documentation

## Troubleshooting

### Microphone Issues
- Ensure HTTPS in production (required for microphone access)
- Check browser permissions
- Verify microphone hardware

### API Connection Issues
- Check backend server is running
- Verify API URL in environment variables
- Check browser console for CORS errors

### Speech Recognition Issues
- Only works in Chrome, Safari, and Edge
- Requires internet connection
- Check browser language settings

## Contributing

1. Follow the existing code style
2. Add TypeScript types for new features
3. Update tests for changes
4. Update documentation

## License

MIT License - see LICENSE file for details
