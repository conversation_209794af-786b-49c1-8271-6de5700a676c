import { Router } from 'express';
import multer from 'multer';
import { asyncHand<PERSON> } from '@/middleware/errorHandler';
import { validate, fileSchemas, validateAudioFile } from '@/middleware/validation';
import { uploadRateLimiter } from '@/middleware/rateLimiter';
import { VoiceService } from '@/services/voiceService';
import { logger } from '@/utils/logger';
import { config } from '@/config/environment';

const router = Router();
const voiceService = new VoiceService();

// Configure multer for audio file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: config.MAX_FILE_SIZE,
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = config.ALLOWED_AUDIO_FORMATS.split(',');
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`));
    }
  },
});

// Upload audio file
router.post('/upload',
  uploadRateLimiter,
  upload.single('audio'),
  validate(fileSchemas.audioUpload),
  asyncHandler(async (req, res) => {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'NO_AUDIO_FILE',
          message: 'No audio file provided',
        },
        timestamp: new Date().toISOString(),
      });
    }

    logger.info('Processing audio file upload', {
      requestId: req.requestId,
      filename: req.file.originalname,
      size: req.file.size,
      mimeType: req.file.mimetype,
    });

    try {
      // Validate audio file
      validateAudioFile(req.file);

      // Parse metadata if provided
      let metadata = {};
      if (req.body.metadata) {
        try {
          metadata = JSON.parse(req.body.metadata);
        } catch (error) {
          logger.warn('Invalid metadata JSON provided', {
            requestId: req.requestId,
            metadata: req.body.metadata,
          });
        }
      }

      // Save audio file
      const result = await voiceService.saveAudioFile(
        req.file.buffer,
        req.file.originalname,
        req.file.mimetype,
        {
          ...metadata,
          uploadedAt: new Date().toISOString(),
          userAgent: req.get('User-Agent'),
          ip: req.ip,
        }
      );

      logger.info('Audio file uploaded successfully', {
        requestId: req.requestId,
        filename: result.filename,
        url: result.url,
        size: req.file.size,
      });

      res.json({
        success: true,
        data: {
          filename: result.filename,
          url: result.url,
          size: req.file.size,
          format: req.file.mimetype,
        },
        message: 'Audio file uploaded successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error: any) {
      logger.error('Audio file upload failed', {
        requestId: req.requestId,
        error: error.message,
        filename: req.file.originalname,
        size: req.file.size,
      });
      throw error;
    }
  })
);

// Download/stream audio file
router.get('/:filename',
  asyncHandler(async (req, res) => {
    const { filename } = req.params;

    logger.debug('Audio file requested', {
      requestId: req.requestId,
      filename,
      userAgent: req.get('User-Agent'),
    });

    try {
      const audioFile = await voiceService.getAudioFile(filename);

      if (!audioFile) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'AUDIO_FILE_NOT_FOUND',
            message: 'Audio file not found',
          },
          timestamp: new Date().toISOString(),
        });
      }

      // Set appropriate headers
      res.set({
        'Content-Type': audioFile.mimeType,
        'Content-Length': audioFile.buffer.length.toString(),
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'Accept-Ranges': 'bytes',
      });

      // Handle range requests for audio streaming
      const range = req.headers.range;
      if (range) {
        const parts = range.replace(/bytes=/, '').split('-');
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : audioFile.buffer.length - 1;
        const chunksize = (end - start) + 1;

        res.status(206);
        res.set({
          'Content-Range': `bytes ${start}-${end}/${audioFile.buffer.length}`,
          'Content-Length': chunksize.toString(),
        });

        res.end(audioFile.buffer.slice(start, end + 1));
      } else {
        res.end(audioFile.buffer);
      }

      logger.debug('Audio file served', {
        requestId: req.requestId,
        filename,
        size: audioFile.buffer.length,
        mimeType: audioFile.mimeType,
      });

    } catch (error: any) {
      logger.error('Failed to serve audio file', {
        requestId: req.requestId,
        filename,
        error: error.message,
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'AUDIO_SERVE_ERROR',
          message: 'Failed to serve audio file',
        },
        timestamp: new Date().toISOString(),
      });
    }
  })
);

// Get audio file metadata
router.get('/:filename/info',
  asyncHandler(async (req, res) => {
    const { filename } = req.params;

    logger.debug('Audio file info requested', {
      requestId: req.requestId,
      filename,
    });

    try {
      // This would require querying the database for file metadata
      // For now, we'll return basic info
      const audioFile = await voiceService.getAudioFile(filename);

      if (!audioFile) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'AUDIO_FILE_NOT_FOUND',
            message: 'Audio file not found',
          },
          timestamp: new Date().toISOString(),
        });
      }

      res.json({
        success: true,
        data: {
          filename,
          size: audioFile.buffer.length,
          mimeType: audioFile.mimeType,
          url: `/api/audio/${filename}`,
        },
        message: 'Audio file info retrieved successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error: any) {
      logger.error('Failed to get audio file info', {
        requestId: req.requestId,
        filename,
        error: error.message,
      });
      throw error;
    }
  })
);

// Cleanup expired files (admin endpoint)
router.post('/cleanup',
  asyncHandler(async (req, res) => {
    logger.info('Starting audio file cleanup', {
      requestId: req.requestId,
    });

    try {
      const deletedCount = await voiceService.cleanupExpiredFiles();

      logger.info('Audio file cleanup completed', {
        requestId: req.requestId,
        deletedCount,
      });

      res.json({
        success: true,
        data: {
          deletedCount,
        },
        message: `Cleaned up ${deletedCount} expired audio files`,
        timestamp: new Date().toISOString(),
      });

    } catch (error: any) {
      logger.error('Audio file cleanup failed', {
        requestId: req.requestId,
        error: error.message,
      });
      throw error;
    }
  })
);

export default router;
