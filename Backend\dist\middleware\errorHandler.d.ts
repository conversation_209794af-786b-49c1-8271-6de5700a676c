import { Request, Response, NextFunction } from 'express';
export declare class AppError extends Error {
    statusCode: number;
    code: string;
    isOperational: boolean;
    constructor(message: string, statusCode?: number, code?: string);
}
export declare class ValidationError extends AppError {
    details: any;
    constructor(message: string, details?: any);
}
export declare class NotFoundError extends AppError {
    constructor(resource?: string);
}
export declare class UnauthorizedError extends AppError {
    constructor(message?: string);
}
export declare class ForbiddenError extends AppError {
    constructor(message?: string);
}
export declare class RateLimitError extends AppError {
    constructor(message?: string);
}
export declare class FileUploadError extends AppError {
    constructor(message: string);
}
export declare class AudioProcessingError extends AppError {
    constructor(message: string);
}
export declare class AIServiceError extends AppError {
    constructor(message: string, statusCode?: number);
}
export declare class DatabaseError extends AppError {
    constructor(message: string);
}
export declare function errorHandler(error: Error, req: Request, res: Response, next: NextFunction): void;
export declare function asyncHandler(fn: (req: Request, res: Response, next: NextFunction) => Promise<any>): (req: Request, res: Response, next: NextFunction) => void;
export declare function notFoundHandler(req: Request, res: Response, next: NextFunction): void;
export declare function setupGlobalErrorHandlers(): void;
export declare const createError: {
    validation: (message: string, details?: any) => ValidationError;
    notFound: (resource?: string) => NotFoundError;
    unauthorized: (message?: string) => UnauthorizedError;
    forbidden: (message?: string) => ForbiddenError;
    rateLimit: (message?: string) => RateLimitError;
    fileUpload: (message: string) => FileUploadError;
    audioProcessing: (message: string) => AudioProcessingError;
    aiService: (message: string, statusCode?: number) => AIServiceError;
    database: (message: string) => DatabaseError;
    generic: (message: string, statusCode?: number, code?: string) => AppError;
};
//# sourceMappingURL=errorHandler.d.ts.map