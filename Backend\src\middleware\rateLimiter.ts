import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { logger, logSecurity } from '@/utils/logger';
import { config } from '@/config/environment';
import { RateLimitError } from './errorHandler';

// Rate limit store interface (for future Redis implementation)
interface RateLimitStore {
  incr(key: string): Promise<{ totalHits: number; timeToExpire: number }>;
  decrement(key: string): Promise<void>;
  resetKey(key: string): Promise<void>;
}

// Custom rate limit handler
const rateLimitHandler = (req: Request, res: Response) => {
  const clientInfo = {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    url: req.originalUrl,
    method: req.method,
  };

  // Log security event
  logSecurity('RATE_LIMIT_EXCEEDED', clientInfo, 'warn');

  // Throw rate limit error (will be handled by error middleware)
  throw new RateLimitError('Too many requests, please try again later');
};

// Skip function for rate limiting
const skipRateLimit = (req: Request): boolean => {
  // Skip rate limiting for health checks
  if (req.path === '/api/health') {
    return true;
  }

  // Skip for localhost in development (optional)
  if (process.env.NODE_ENV === 'development' && req.ip === '127.0.0.1') {
    return false; // Still apply rate limiting in development for testing
  }

  return false;
};

// Key generator for rate limiting
const keyGenerator = (req: Request): string => {
  // Use IP address as the primary key
  let key = req.ip || 'unknown';

  // Add user ID if authenticated (for future implementation)
  if (req.user?.id) {
    key += `:user:${req.user.id}`;
  }

  return key;
};

// General API rate limiter
export const rateLimiter = rateLimit({
  windowMs: config.RATE_LIMIT_WINDOW_MS,
  max: config.RATE_LIMIT_MAX_REQUESTS,
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests, please try again later',
    },
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  handler: rateLimitHandler,
  skip: skipRateLimit,
  keyGenerator,
  onLimitReached: (req: Request) => {
    logger.warn('Rate limit reached', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl,
    });
  },
});

// Voice processing rate limiter (more restrictive)
export const voiceRateLimiter = rateLimit({
  windowMs: config.RATE_LIMIT_WINDOW_MS,
  max: config.RATE_LIMIT_VOICE_MAX_REQUESTS,
  message: {
    success: false,
    error: {
      code: 'VOICE_RATE_LIMIT_EXCEEDED',
      message: 'Too many voice requests, please try again later',
    },
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: rateLimitHandler,
  skip: skipRateLimit,
  keyGenerator,
  onLimitReached: (req: Request) => {
    logger.warn('Voice rate limit reached', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl,
    });
  },
});

// File upload rate limiter
export const uploadRateLimiter = rateLimit({
  windowMs: config.RATE_LIMIT_WINDOW_MS,
  max: 10, // 10 uploads per minute
  message: {
    success: false,
    error: {
      code: 'UPLOAD_RATE_LIMIT_EXCEEDED',
      message: 'Too many file uploads, please try again later',
    },
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: rateLimitHandler,
  skip: skipRateLimit,
  keyGenerator,
});

// Chat rate limiter (moderate restriction)
export const chatRateLimiter = rateLimit({
  windowMs: config.RATE_LIMIT_WINDOW_MS,
  max: 30, // 30 chat messages per minute
  message: {
    success: false,
    error: {
      code: 'CHAT_RATE_LIMIT_EXCEEDED',
      message: 'Too many chat messages, please slow down',
    },
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: rateLimitHandler,
  skip: skipRateLimit,
  keyGenerator,
});

// Strict rate limiter for sensitive operations
export const strictRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 requests per 15 minutes
  message: {
    success: false,
    error: {
      code: 'STRICT_RATE_LIMIT_EXCEEDED',
      message: 'Too many requests for this operation, please try again later',
    },
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: rateLimitHandler,
  skip: skipRateLimit,
  keyGenerator,
});

// Progressive rate limiter (increases restriction based on violations)
class ProgressiveRateLimiter {
  private violations = new Map<string, { count: number; lastViolation: number }>();
  private readonly baseLimit: number;
  private readonly windowMs: number;

  constructor(baseLimit: number = 100, windowMs: number = 60000) {
    this.baseLimit = baseLimit;
    this.windowMs = windowMs;
  }

  getLimit(key: string): number {
    const violation = this.violations.get(key);
    
    if (!violation) {
      return this.baseLimit;
    }

    // Reset violations after 1 hour
    if (Date.now() - violation.lastViolation > 60 * 60 * 1000) {
      this.violations.delete(key);
      return this.baseLimit;
    }

    // Reduce limit based on violation count
    const reductionFactor = Math.min(violation.count * 0.1, 0.8); // Max 80% reduction
    return Math.floor(this.baseLimit * (1 - reductionFactor));
  }

  recordViolation(key: string): void {
    const existing = this.violations.get(key);
    
    if (existing) {
      existing.count += 1;
      existing.lastViolation = Date.now();
    } else {
      this.violations.set(key, {
        count: 1,
        lastViolation: Date.now(),
      });
    }

    logger.warn('Progressive rate limit violation recorded', {
      key,
      violationCount: this.violations.get(key)?.count,
      newLimit: this.getLimit(key),
    });
  }

  createMiddleware() {
    return rateLimit({
      windowMs: this.windowMs,
      max: (req: Request) => {
        const key = keyGenerator(req);
        return this.getLimit(key);
      },
      handler: (req: Request, res: Response) => {
        const key = keyGenerator(req);
        this.recordViolation(key);
        rateLimitHandler(req, res);
      },
      skip: skipRateLimit,
      keyGenerator,
    });
  }
}

// Create progressive rate limiter instance
export const progressiveRateLimiter = new ProgressiveRateLimiter().createMiddleware();

// Rate limit info middleware (adds rate limit info to response headers)
export const rateLimitInfo = (req: Request, res: Response, next: Function) => {
  // Add custom rate limit headers
  res.set({
    'X-RateLimit-Policy': 'General API: 100/min, Voice: 20/min, Chat: 30/min',
    'X-RateLimit-Remaining-General': res.get('RateLimit-Remaining') || 'unknown',
  });
  
  next();
};

// Utility function to check if IP is rate limited
export async function isRateLimited(ip: string, endpoint: string): Promise<boolean> {
  // This would typically check against a Redis store
  // For now, we'll return false as rate limiting is handled by middleware
  return false;
}

// Utility function to get rate limit status
export function getRateLimitStatus(req: Request): {
  limit: number;
  remaining: number;
  reset: Date;
} {
  const limit = parseInt(req.get('RateLimit-Limit') || '0');
  const remaining = parseInt(req.get('RateLimit-Remaining') || '0');
  const resetTime = parseInt(req.get('RateLimit-Reset') || '0');
  
  return {
    limit,
    remaining,
    reset: new Date(resetTime * 1000),
  };
}

// Whitelist middleware (bypass rate limiting for specific IPs)
export const whitelistMiddleware = (whitelist: string[] = []) => {
  return (req: Request, res: Response, next: Function) => {
    if (whitelist.includes(req.ip)) {
      // Skip rate limiting for whitelisted IPs
      req.skipRateLimit = true;
    }
    next();
  };
};

// Export default rate limiter
export default rateLimiter;
