{"version": 3, "file": "requestLogger.js", "sourceRoot": "", "sources": ["../../src/middleware/requestLogger.ts"], "names": [], "mappings": ";;AAoBA,sCA0IC;AAGD,kDA+BC;AAGD,8CAQC;AAGD,0CAcC;AAGD,gDA6BC;AAGD,oCAgBC;AAGD,8CAMC;AAGD,gCAqBC;AA/SD,2CAAiE;AACjE,+BAAoC;AAkBpC,SAAgB,aAAa,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;IAE3E,GAAG,CAAC,SAAS,GAAG,IAAA,SAAM,GAAE,CAAC;IACzB,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAG3B,GAAG,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;IAGvC,MAAM,WAAW,GAAG;QAClB,SAAS,EAAE,GAAG,CAAC,SAAS;QACxB,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,GAAG,EAAE,GAAG,CAAC,WAAW;QACpB,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa;QAC1C,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;QAChC,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC;QAC3B,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC;QACpC,aAAa,EAAE,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC;QACxC,cAAc,EAAE,GAAG,CAAC,GAAG,CAAC,iBAAiB,CAAC;QAC1C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC;IAGF,eAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;IAG7C,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;IAC9B,GAAG,CAAC,IAAI,GAAG,UAAS,IAAS;QAE3B,MAAM,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAGpE,MAAM,YAAY,GAAG;YACnB,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,YAAY,EAAE,GAAG,YAAY,IAAI;YACjC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM;SAC3C,CAAC;QAGF,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;YAC1B,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBAClD,GAAG,WAAW;gBACd,GAAG,YAAY;gBACf,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;aACxE,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;YACjC,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;gBACjD,GAAG,WAAW;gBACd,GAAG,YAAY;gBACf,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;aACxE,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;gBAC5C,GAAG,WAAW;gBACd,GAAG,YAAY;aAChB,CAAC,CAAC;QACL,CAAC;QAGD,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;IAGF,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;IAC9B,GAAG,CAAC,IAAI,GAAG,UAAS,IAAS;QAC3B,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;YACrB,MAAM,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAEpE,MAAM,YAAY,GAAG;gBACnB,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,UAAU,EAAE,GAAG,CAAC,UAAU;gBAC1B,YAAY,EAAE,GAAG,YAAY,IAAI;gBACjC,aAAa,EAAE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;aAChF,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC/B,GAAG,WAAW;gBACd,GAAG,YAAY;aAChB,CAAC,CAAC;QACL,CAAC;QAED,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;IAGF,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACpB,MAAM,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAGpE,IAAI,YAAY,GAAG,IAAI,EAAE,CAAC;YACxB,eAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACnC,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,GAAG,EAAE,GAAG,CAAC,WAAW;gBACpB,YAAY,EAAE,GAAG,YAAY,IAAI;gBACjC,UAAU,EAAE,GAAG,CAAC,UAAU;aAC3B,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;YAC3B,IAAA,oBAAW,EAAC,6BAA6B,EAAE;gBACzC,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,GAAG,EAAE,GAAG,CAAC,WAAW;gBACpB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;aACjC,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;YAClC,IAAA,oBAAW,EAAC,0BAA0B,EAAE;gBACtC,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,GAAG,EAAE,GAAG,CAAC,WAAW;gBACpB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;aACjC,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;YAClC,IAAA,oBAAW,EAAC,qBAAqB,EAAE;gBACjC,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,GAAG,EAAE,GAAG,CAAC,WAAW;gBACpB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;aACjC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IAGH,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;QACnB,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;YAC1B,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBACjC,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,GAAG,EAAE,GAAG,CAAC,WAAW;gBACpB,EAAE,EAAE,GAAG,CAAC,EAAE;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC;AAGD,SAAgB,mBAAmB,CAAC,IAAS;IAC3C,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,eAAe,GAAG;QACtB,UAAU;QACV,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,eAAe;QACf,QAAQ;QACR,SAAS;KACV,CAAC;IAEF,MAAM,QAAQ,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;IAE7B,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;QACpC,IAAI,KAAK,IAAI,QAAQ,EAAE,CAAC;YACtB,QAAQ,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC;QACjC,CAAC;IACH,CAAC;IAGD,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC3B,IAAI,OAAO,QAAQ,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC;YAChE,QAAQ,CAAC,GAAG,CAAC,GAAG,mBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAGD,SAAgB,iBAAiB,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;IAC/E,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;QACvD,eAAM,CAAC,KAAK,CAAC,cAAc,EAAE;YAC3B,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,IAAI,EAAE,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC;SACpC,CAAC,CAAC;IACL,CAAC;IACD,IAAI,EAAE,CAAC;AACT,CAAC;AAGD,SAAgB,eAAe,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;IAE7E,MAAM,SAAS,GAAG;QAChB,QAAQ,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI;QACrC,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;QAChC,SAAS,EAAE,IAAI,IAAI,EAAE;KACtB,CAAC;IAGF,eAAM,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IAErC,IAAI,EAAE,CAAC;AACT,CAAC;AAGD,SAAgB,kBAAkB,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;IAChF,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;IAE1C,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACpB,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACxC,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC,GAAG,OAAO,CAAC;QAGvD,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;YAClC,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,WAAW;YACpB,QAAQ,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI;YACpC,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE;SACnC,CAAC,CAAC;QAGH,IAAI,QAAQ,GAAG,KAAK,EAAE,CAAC;YACrB,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE;gBACnD,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,GAAG,EAAE,GAAG,CAAC,WAAW;gBACpB,QAAQ,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI;aACrC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC;AAGD,SAAgB,YAAY,CAAC,KAAY,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB;IAExF,eAAM,CAAC,KAAK,CAAC,eAAe,EAAE;QAC5B,SAAS,EAAE,GAAG,CAAC,SAAS;QACxB,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,GAAG,EAAE,GAAG,CAAC,WAAW;QACpB,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;QAChC,KAAK,EAAE;YACL,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB;KACF,CAAC,CAAC;IAEH,IAAI,CAAC,KAAK,CAAC,CAAC;AACd,CAAC;AAGD,SAAgB,iBAAiB,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;IAE/E,IAAI,GAAG,CAAC,IAAI,KAAK,aAAa,IAAI,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QACzD,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC;IACzB,CAAC;IACD,IAAI,EAAE,CAAC;AACT,CAAC;AAGD,SAAgB,UAAU,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;IACxE,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,KAAK,MAAM,EAAE,CAAC;QAC/C,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjC,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAE1B,IAAI,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACnC,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBACrC,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,MAAM;gBACN,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,+BAA+B,CAAC;gBAChD,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,gCAAgC,CAAC;aACnD,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,MAAM,EAAE,CAAC;YAClB,eAAM,CAAC,KAAK,CAAC,cAAc,EAAE;gBAC3B,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,MAAM;gBACN,MAAM;aACP,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IACD,IAAI,EAAE,CAAC;AACT,CAAC;AAGD,kBAAe,aAAa,CAAC"}