import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        <meta charSet="utf-8" />
        <meta name="description" content="Voice-enabled AI chat application with real-time speech recognition and synthesis" />
        <meta name="keywords" content="AI, voice chat, speech recognition, text-to-speech, conversation" />
        <meta name="author" content="Voice Chat Team" />
        
        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        
        {/* Theme color */}
        <meta name="theme-color" content="#3b82f6" />
        <meta name="msapplication-TileColor" content="#3b82f6" />
        
        {/* Open Graph */}
        <meta property="og:type" content="website" />
        <meta property="og:title" content="Voice AI Chat" />
        <meta property="og:description" content="Have natural conversations with AI using your voice" />
        <meta property="og:image" content="/og-image.png" />
        
        {/* Twitter Card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Voice AI Chat" />
        <meta name="twitter:description" content="Have natural conversations with AI using your voice" />
        <meta name="twitter:image" content="/twitter-image.png" />
        
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* CSS custom properties for theming */}
        <style jsx>{`
          :root {
            --toast-bg: #ffffff;
            --toast-color: #374151;
            --toast-border: #e5e7eb;
          }
          
          .dark {
            --toast-bg: #1f2937;
            --toast-color: #f9fafb;
            --toast-border: #374151;
          }
        `}</style>
      </Head>
      <body className="antialiased">
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
