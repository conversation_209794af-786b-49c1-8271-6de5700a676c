"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationService = void 0;
const database_1 = require("@/config/database");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
class ConversationService {
    async getConversationWithMessages(conversationId, options = {}) {
        try {
            const { limit = 50, offset = 0, before, after } = options;
            const conversation = await database_1.prisma.conversation.findUnique({
                where: { id: conversationId },
                include: {
                    messages: {
                        where: {
                            ...(before && { createdAt: { lt: before } }),
                            ...(after && { createdAt: { gt: after } }),
                        },
                        orderBy: { createdAt: 'asc' },
                        skip: offset,
                        take: limit,
                    },
                },
            });
            if (!conversation) {
                return null;
            }
            return {
                ...conversation,
                metadata: this.parseMetadata(conversation.metadata),
                messages: conversation.messages.map(msg => ({
                    ...msg,
                    metadata: this.parseMetadata(msg.metadata),
                })),
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get conversation with messages', {
                conversationId,
                error: error.message,
            });
            throw errorHandler_1.createError.database('Failed to retrieve conversation');
        }
    }
    async getUserConversations(userId, options = {}) {
        try {
            const { page = 1, limit = 20, sortBy = 'updatedAt', sortOrder = 'desc' } = options;
            const skip = (page - 1) * limit;
            const where = userId ? { userId } : {};
            const [conversations, total] = await Promise.all([
                database_1.prisma.conversation.findMany({
                    where,
                    orderBy: { [sortBy]: sortOrder },
                    skip,
                    take: limit,
                    include: {
                        _count: {
                            select: { messages: true },
                        },
                    },
                }),
                database_1.prisma.conversation.count({ where }),
            ]);
            const totalPages = Math.ceil(total / limit);
            return {
                data: conversations.map(conv => ({
                    ...conv,
                    metadata: this.parseMetadata(conv.metadata),
                    messageCount: conv._count.messages,
                })),
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages,
                    hasNext: page < totalPages,
                    hasPrev: page > 1,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get user conversations', {
                userId,
                error: error.message,
            });
            throw errorHandler_1.createError.database('Failed to retrieve conversations');
        }
    }
    async updateConversationTitle(conversationId, title, userId) {
        try {
            const where = { id: conversationId };
            if (userId) {
                where.userId = userId;
            }
            const conversation = await database_1.prisma.conversation.findUnique({ where });
            if (!conversation) {
                throw errorHandler_1.createError.notFound('Conversation');
            }
            const updatedConversation = await database_1.prisma.conversation.update({
                where: { id: conversationId },
                data: {
                    title,
                    updatedAt: new Date(),
                },
            });
            logger_1.logger.info('Conversation title updated', {
                conversationId,
                newTitle: title,
                userId,
            });
            return {
                ...updatedConversation,
                metadata: this.parseMetadata(updatedConversation.metadata),
            };
        }
        catch (error) {
            if (error.isOperational) {
                throw error;
            }
            logger_1.logger.error('Failed to update conversation title', {
                conversationId,
                title,
                error: error.message,
            });
            throw errorHandler_1.createError.database('Failed to update conversation title');
        }
    }
    async deleteConversation(conversationId, userId) {
        try {
            const where = { id: conversationId };
            if (userId) {
                where.userId = userId;
            }
            const conversation = await database_1.prisma.conversation.findUnique({ where });
            if (!conversation) {
                throw errorHandler_1.createError.notFound('Conversation');
            }
            await database_1.prisma.message.deleteMany({
                where: { conversationId },
            });
            await database_1.prisma.conversation.delete({
                where: { id: conversationId },
            });
            logger_1.logger.info('Conversation deleted', {
                conversationId,
                userId,
            });
        }
        catch (error) {
            if (error.isOperational) {
                throw error;
            }
            logger_1.logger.error('Failed to delete conversation', {
                conversationId,
                error: error.message,
            });
            throw errorHandler_1.createError.database('Failed to delete conversation');
        }
    }
    async clearUserConversations(userId) {
        try {
            const where = userId ? { userId } : {};
            const conversations = await database_1.prisma.conversation.findMany({
                where,
                select: { id: true },
            });
            const conversationIds = conversations.map(c => c.id);
            if (conversationIds.length === 0) {
                return 0;
            }
            await database_1.prisma.message.deleteMany({
                where: {
                    conversationId: { in: conversationIds },
                },
            });
            const result = await database_1.prisma.conversation.deleteMany({ where });
            logger_1.logger.info('User conversations cleared', {
                userId,
                deletedCount: result.count,
            });
            return result.count;
        }
        catch (error) {
            logger_1.logger.error('Failed to clear user conversations', {
                userId,
                error: error.message,
            });
            throw errorHandler_1.createError.database('Failed to clear conversations');
        }
    }
    async getConversationStats(userId) {
        try {
            const where = userId ? { userId } : {};
            const [totalConversations, totalMessages, recentConversations, avgMessagesPerConversation,] = await Promise.all([
                database_1.prisma.conversation.count({ where }),
                database_1.prisma.message.count({
                    where: {
                        conversation: where,
                    },
                }),
                database_1.prisma.conversation.count({
                    where: {
                        ...where,
                        createdAt: {
                            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                        },
                    },
                }),
                database_1.prisma.conversation.aggregate({
                    where,
                    _avg: {
                        id: true,
                    },
                }),
            ]);
            const avgMessages = totalConversations > 0
                ? Math.round(totalMessages / totalConversations * 100) / 100
                : 0;
            return {
                totalConversations,
                totalMessages,
                recentConversations,
                avgMessagesPerConversation: avgMessages,
                stats: {
                    conversationsThisWeek: recentConversations,
                    messagesThisWeek: await this.getRecentMessageCount(userId, 7),
                    longestConversation: await this.getLongestConversation(userId),
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get conversation stats', {
                userId,
                error: error.message,
            });
            throw errorHandler_1.createError.database('Failed to retrieve conversation statistics');
        }
    }
    async getRecentMessageCount(userId, days = 7) {
        const where = userId ? { conversation: { userId } } : {};
        return database_1.prisma.message.count({
            where: {
                ...where,
                createdAt: {
                    gte: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
                },
            },
        });
    }
    async getLongestConversation(userId) {
        const where = userId ? { userId } : {};
        const conversations = await database_1.prisma.conversation.findMany({
            where,
            include: {
                _count: {
                    select: { messages: true },
                },
            },
            orderBy: {
                messages: {
                    _count: 'desc',
                },
            },
            take: 1,
        });
        return conversations[0] ? {
            id: conversations[0].id,
            title: conversations[0].title,
            messageCount: conversations[0]._count.messages,
        } : null;
    }
    parseMetadata(metadata) {
        try {
            return JSON.parse(metadata);
        }
        catch {
            return {};
        }
    }
}
exports.ConversationService = ConversationService;
exports.default = ConversationService;
//# sourceMappingURL=conversationService.js.map