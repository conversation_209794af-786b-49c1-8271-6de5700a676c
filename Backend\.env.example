# Environment Configuration
NODE_ENV=development
PORT=8000

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/voicechat?schema=public"
# For development with SQLite:
# DATABASE_URL="file:./dev.db"

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=150
OPENAI_TEMPERATURE=0.7

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_VOICE_MAX_REQUESTS=20

# File Upload Configuration
MAX_FILE_SIZE=10485760
MAX_RECORDING_DURATION=60
UPLOAD_DIR=./uploads
ALLOWED_AUDIO_FORMATS=audio/wav,audio/mp3,audio/webm,audio/ogg

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE=./logs/app.log

# Security Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# Audio Processing
AUDIO_SAMPLE_RATE=16000
AUDIO_CHANNELS=1
AUDIO_BIT_DEPTH=16

# External Services (Optional)
SENTRY_DSN=your_sentry_dsn_here
REDIS_URL=redis://localhost:6379

# Development Settings
ENABLE_API_DOCS=true
ENABLE_CORS_LOGGING=true
ENABLE_REQUEST_LOGGING=true
