import { useState, useEffect, useRef, useCallback } from 'react';
import { UseSpeechRecognitionOptions, UseSpeechRecognitionReturn } from '@/types';

// Check for browser support
const SpeechRecognition = typeof window !== 'undefined' 
  ? window.SpeechRecognition || window.webkitSpeechRecognition 
  : null;

export function useSpeechRecognition(options: UseSpeechRecognitionOptions = {}): UseSpeechRecognitionReturn {
  const {
    language = 'en-US',
    continuous = false,
    interimResults = true,
    maxAlternatives = 1,
  } = options;

  const [transcript, setTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [finalTranscript, setFinalTranscript] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [error, setError] = useState<string>();

  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const hasRecognitionSupport = Boolean(SpeechRecognition);

  // Initialize speech recognition
  useEffect(() => {
    if (!SpeechRecognition) {
      setError('Speech recognition is not supported in this browser');
      return;
    }

    const recognition = new SpeechRecognition();
    
    // Configure recognition
    recognition.continuous = continuous;
    recognition.interimResults = interimResults;
    recognition.lang = language;
    recognition.maxAlternatives = maxAlternatives;

    // Event handlers
    recognition.onstart = () => {
      setIsListening(true);
      setError(undefined);
      console.log('Speech recognition started');
    };

    recognition.onend = () => {
      setIsListening(false);
      console.log('Speech recognition ended');
    };

    recognition.onerror = (event) => {
      setIsListening(false);
      
      let errorMessage = 'Speech recognition error';
      
      switch (event.error) {
        case 'no-speech':
          errorMessage = 'No speech detected. Please try again.';
          break;
        case 'audio-capture':
          errorMessage = 'Audio capture failed. Please check your microphone.';
          break;
        case 'not-allowed':
          errorMessage = 'Microphone permission denied. Please allow microphone access.';
          break;
        case 'network':
          errorMessage = 'Network error occurred during speech recognition.';
          break;
        case 'service-not-allowed':
          errorMessage = 'Speech recognition service is not allowed.';
          break;
        case 'bad-grammar':
          errorMessage = 'Speech recognition grammar error.';
          break;
        case 'language-not-supported':
          errorMessage = `Language "${language}" is not supported.`;
          break;
        default:
          errorMessage = `Speech recognition error: ${event.error}`;
      }
      
      setError(errorMessage);
      console.error('Speech recognition error:', event.error);
    };

    recognition.onresult = (event) => {
      let interimTranscriptText = '';
      let finalTranscriptText = '';

      // Process all results
      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const transcriptText = result[0].transcript;

        if (result.isFinal) {
          finalTranscriptText += transcriptText;
        } else {
          interimTranscriptText += transcriptText;
        }
      }

      // Update state
      setInterimTranscript(interimTranscriptText);
      
      if (finalTranscriptText) {
        setFinalTranscript(prev => prev + finalTranscriptText);
        setTranscript(prev => prev + finalTranscriptText);
      }

      // Update combined transcript
      const combinedTranscript = finalTranscript + finalTranscriptText + interimTranscriptText;
      setTranscript(combinedTranscript);

      console.log('Speech recognition result:', {
        interim: interimTranscriptText,
        final: finalTranscriptText,
        combined: combinedTranscript,
        confidence: event.results[event.results.length - 1]?.[0]?.confidence,
      });
    };

    recognitionRef.current = recognition;

    return () => {
      if (recognition) {
        recognition.abort();
      }
    };
  }, [language, continuous, interimResults, maxAlternatives, finalTranscript]);

  const startListening = useCallback(() => {
    if (!recognitionRef.current) {
      setError('Speech recognition is not available');
      return;
    }

    if (isListening) {
      console.warn('Speech recognition is already active');
      return;
    }

    try {
      recognitionRef.current.start();
    } catch (error) {
      console.error('Failed to start speech recognition:', error);
      setError('Failed to start speech recognition');
    }
  }, [isListening]);

  const stopListening = useCallback(() => {
    if (!recognitionRef.current) {
      return;
    }

    if (!isListening) {
      console.warn('Speech recognition is not active');
      return;
    }

    try {
      recognitionRef.current.stop();
    } catch (error) {
      console.error('Failed to stop speech recognition:', error);
    }
  }, [isListening]);

  const resetTranscript = useCallback(() => {
    setTranscript('');
    setInterimTranscript('');
    setFinalTranscript('');
    setError(undefined);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (recognitionRef.current && isListening) {
        recognitionRef.current.abort();
      }
    };
  }, [isListening]);

  return {
    transcript,
    interimTranscript,
    finalTranscript,
    isListening,
    hasRecognitionSupport,
    startListening,
    stopListening,
    resetTranscript,
    error,
  };
}

// Hook for checking speech recognition support
export function useSpeechRecognitionSupport() {
  const [isSupported, setIsSupported] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkSupport = async () => {
      try {
        // Check for basic API support
        const hasAPI = Boolean(SpeechRecognition);
        
        if (hasAPI) {
          // Try to create an instance to verify it works
          const recognition = new SpeechRecognition!();
          recognition.abort(); // Clean up immediately
          setIsSupported(true);
        } else {
          setIsSupported(false);
        }
      } catch (error) {
        console.error('Speech recognition support check failed:', error);
        setIsSupported(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkSupport();
  }, []);

  return { isSupported, isChecking };
}

// Hook for getting available languages
export function useSpeechRecognitionLanguages() {
  const [languages, setLanguages] = useState<string[]>([]);

  useEffect(() => {
    // Common speech recognition languages
    const commonLanguages = [
      'en-US', 'en-GB', 'en-AU', 'en-CA',
      'es-ES', 'es-MX', 'es-AR',
      'fr-FR', 'fr-CA',
      'de-DE', 'de-AT', 'de-CH',
      'it-IT',
      'pt-PT', 'pt-BR',
      'ru-RU',
      'ja-JP',
      'ko-KR',
      'zh-CN', 'zh-TW', 'zh-HK',
    ];

    setLanguages(commonLanguages);
  }, []);

  return languages;
}
