import { GoogleGenerativeAI } from '@google/generative-ai';
declare const genAI: GoogleGenerativeAI;
export declare const GEMINI_CONFIG: {
    apiKey: any;
    model: any;
    maxTokens: any;
    temperature: any;
    systemPrompt: string;
};
export declare const AVAILABLE_VOICES: {
    id: string;
    name: string;
    language: string;
    gender: string;
}[];
export declare function getGeminiModel(): import("@google/generative-ai").GenerativeModel;
export declare function generateChatCompletion(messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
}>, options?: {
    maxTokens?: number;
    temperature?: number;
    model?: string;
}): Promise<{
    id: string;
    object: string;
    created: number;
    model: any;
    choices: {
        index: number;
        message: {
            role: "assistant";
            content: string;
        };
        finish_reason: string;
    }[];
    usage: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}>;
export declare function handleGeminiError(error: any): {
    error: {
        type: string;
        message: any;
        code: any;
    };
    statusCode: number;
};
export declare function testGeminiConnection(): Promise<boolean>;
export declare function transcribeAudio(audioBuffer: Buffer, options?: {
    language?: string;
    model?: string;
    temperature?: number;
}): Promise<{
    text: string;
}>;
export declare function generateSpeech(text: string, options?: {
    voice?: string;
    speed?: number;
    model?: string;
}): Promise<Buffer>;
export { genAI, GEMINI_CONFIG, AVAILABLE_VOICES };
//# sourceMappingURL=gemini.d.ts.map