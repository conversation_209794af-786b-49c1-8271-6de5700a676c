// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  timestamp: string;
}

export interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    stack?: string;
  };
  timestamp: string;
}

// Chat Types
export interface ChatMessage {
  id: string;
  conversationId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  audioUrl?: string;
  audioDuration?: number;
  audioFormat?: string;
  audioSize?: number;
  metadata?: Record<string, any>;
  transcript?: string;
  transcriptConfidence?: number;
  transcriptLanguage?: string;
  tokensUsed?: number;
  responseTime?: number;
  modelUsed?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Conversation {
  id: string;
  userId?: string;
  title?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  messages?: ChatMessage[];
}

export interface SendMessageRequest {
  message: string;
  conversationId?: string;
  context?: {
    previousMessages?: number;
    userPreferences?: Record<string, any>;
  };
}

export interface SendMessageResponse {
  conversationId: string;
  response: string;
  audioUrl?: string;
  metadata: {
    responseTime: number;
    tokensUsed: number;
    confidence?: number;
    modelUsed: string;
  };
}

// Voice Processing Types
export interface TranscribeRequest {
  language?: string;
  model?: string;
  temperature?: number;
}

export interface TranscribeResponse {
  transcript: string;
  confidence: number;
  language: string;
  duration: number;
  alternatives?: Array<{
    transcript: string;
    confidence: number;
  }>;
}

export interface SynthesizeRequest {
  text: string;
  voice?: 'alloy' | 'echo' | 'fable' | 'onyx' | 'nova' | 'shimmer';
  speed?: number;
  model?: string;
}

export interface SynthesizeResponse {
  audioUrl: string;
  duration: number;
  format: string;
  size: number;
}

export interface VoiceProcessRequest {
  conversationId?: string;
  language?: string;
  voiceSettings?: {
    voice?: string;
    speed?: number;
  };
}

export interface VoiceProcessResponse {
  conversationId: string;
  userMessage: {
    transcript: string;
    confidence: number;
    audioUrl?: string;
  };
  aiResponse: {
    text: string;
    audioUrl: string;
    duration: number;
  };
  metadata: {
    processingTime: number;
    totalTokens: number;
  };
}

// Audio File Types
export interface AudioFile {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  duration?: number;
  path: string;
  url?: string;
  status: 'UPLOADED' | 'PROCESSING' | 'PROCESSED' | 'FAILED' | 'EXPIRED';
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  expiresAt?: Date;
}

export interface AudioUploadResponse {
  filename: string;
  url: string;
  size: number;
  duration?: number;
  format: string;
}

// User Types
export interface User {
  id: string;
  email?: string;
  name?: string;
  createdAt: Date;
  updatedAt: Date;
  preferences?: UserPreferences;
}

export interface UserPreferences {
  id: string;
  userId: string;
  voiceSettings: {
    voice?: string;
    speed?: number;
    language?: string;
    autoPlay?: boolean;
  };
  uiPreferences: {
    theme?: 'light' | 'dark' | 'auto';
    fontSize?: 'small' | 'medium' | 'large';
    showTimestamps?: boolean;
    showConfidence?: boolean;
    compactMode?: boolean;
  };
  aiSettings: {
    responseLength?: 'short' | 'medium' | 'long';
    personality?: string;
    contextMemory?: number;
    temperature?: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Feedback Types
export interface Feedback {
  id: string;
  type: 'bug' | 'feature' | 'general' | 'rating';
  message: string;
  rating?: number;
  conversationId?: string;
  userAgent?: string;
  ipAddress?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
}

export interface SubmitFeedbackRequest {
  type: 'bug' | 'feature' | 'general' | 'rating';
  message: string;
  rating?: number;
  conversationId?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
}

// Configuration Types
export interface ClientConfig {
  features: {
    speechRecognition: boolean;
    speechSynthesis: boolean;
    audioRecording: boolean;
  };
  limits: {
    maxRecordingDuration: number;
    maxMessageLength: number;
    maxConversationHistory: number;
    maxFileSize: number;
  };
  voiceOptions: Array<{
    id: string;
    name: string;
    language: string;
    gender: 'male' | 'female' | 'neutral';
  }>;
  supportedLanguages: Array<{
    code: string;
    name: string;
    nativeName: string;
  }>;
}

// Health Check Types
export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  version: string;
  services: {
    database: 'connected' | 'disconnected' | 'error';
    openai: 'available' | 'unavailable' | 'error';
    speechService: 'available' | 'unavailable' | 'error';
  };
  uptime: number;
  memory?: {
    used: number;
    total: number;
    percentage: number;
  };
  performance?: {
    averageResponseTime: number;
    requestsPerMinute: number;
    errorRate: number;
  };
}

// API Usage Types
export interface ApiUsage {
  id: string;
  endpoint: string;
  method: string;
  ipAddress?: string;
  userAgent?: string;
  requestSize?: number;
  responseSize?: number;
  responseTime?: number;
  statusCode: number;
  tokensUsed?: number;
  createdAt: Date;
}

// OpenAI Types
export interface OpenAIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface OpenAIUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

export interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: OpenAIMessage;
    finish_reason: string;
  }>;
  usage: OpenAIUsage;
}

// Error Types
export interface ValidationErrorDetail {
  field: string;
  message: string;
  type: 'body' | 'params' | 'query' | 'headers';
}

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: Date;
  retryAfter?: number;
}

// Pagination Types
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Sorting Types
export interface SortParams {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Filter Types
export interface DateFilter {
  before?: Date;
  after?: Date;
}

export interface ConversationFilter extends DateFilter {
  userId?: string;
  hasMessages?: boolean;
}

// Utility Types
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
export type Partial<T> = { [P in keyof T]?: T[P] };
export type Required<T> = { [P in keyof T]-?: T[P] };

// Database Types (Prisma-compatible)
export type MessageRole = 'USER' | 'ASSISTANT' | 'SYSTEM';
export type AudioFileStatus = 'UPLOADED' | 'PROCESSING' | 'PROCESSED' | 'FAILED' | 'EXPIRED';
export type FeedbackType = 'BUG' | 'FEATURE' | 'GENERAL' | 'RATING';

// Express Request Extensions
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
      startTime?: number;
      user?: User;
      skipRateLimit?: boolean;
      skipLogging?: boolean;
    }
  }
}

// Environment Types
export interface EnvironmentVariables {
  NODE_ENV: 'development' | 'staging' | 'production' | 'test';
  PORT: number;
  DATABASE_URL: string;
  OPENAI_API_KEY: string;
  OPENAI_MODEL: string;
  OPENAI_MAX_TOKENS: number;
  OPENAI_TEMPERATURE: number;
  CORS_ORIGIN: string;
  CORS_CREDENTIALS: boolean;
  RATE_LIMIT_ENABLED: boolean;
  RATE_LIMIT_WINDOW_MS: number;
  RATE_LIMIT_MAX_REQUESTS: number;
  RATE_LIMIT_VOICE_MAX_REQUESTS: number;
  MAX_FILE_SIZE: number;
  MAX_RECORDING_DURATION: number;
  UPLOAD_DIR: string;
  ALLOWED_AUDIO_FORMATS: string;
  LOG_LEVEL: 'error' | 'warn' | 'info' | 'debug';
  LOG_FILE: string;
}
